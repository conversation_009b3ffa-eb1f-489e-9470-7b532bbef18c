{"version": 3, "file": "pi-BCynSWdr.mjs", "sources": ["../../../engine-render/src/components/docs/layout/hyphenation/patterns/pi.ts"], "sourcesContent": ["/**\n * Copyright 2023-present DreamNum Co., Ltd.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const Pi = [\n    '0223,022,01,232,322,2322,212,23',\n    '{\".\":[{\"n\":[{\"h\":[{\"ā\":0}],\"t\":1}]}],\"a\":2,\"b\":[{\"b\":3,\"h\":4,\"m\":3,\"r\":4,\"y\":4}],\"c\":[{\"c\":3,\"h\":4}],\"d\":[{\"d\":3,\"h\":4,\"m\":3,\"r\":4,\"v\":4}],\"e\":2,\"g\":[{\"d\":[{\"h\":5}],\"g\":3,\"h\":4,\"r\":3,\"y\":3}],\"h\":[{\"m\":3,\"v\":3,\"y\":3}],\"i\":2,\"j\":[{\"h\":4,\"j\":3}],\"k\":[{\"h\":4,\"k\":3,\"l\":3,\"r\":4,\"v\":4,\"y\":4}],\"l\":[{\"l\":3,\"y\":3}],\"m\":[{\"b\":3,\"h\":3,\"m\":3,\"p\":3,\"y\":3}],\"n\":[{\"d\":3,\"h\":6,\"n\":3,\"t\":6,\"v\":3}],\"o\":2,\"p\":[{\"h\":4,\"l\":3,\"p\":3}],\"s\":[{\"m\":3,\"n\":4,\"s\":3,\"t\":3,\"v\":4,\"y\":4}],\"t\":[{\"h\":4,\"n\":3,\"r\":3,\"t\":3,\"v\":4,\"y\":4}],\"u\":2,\"v\":[{\"h\":3,\"y\":4}],\"y\":[{\"h\":3,\"v\":4,\"y\":3}],\"ñ\":[{\"c\":3,\"h\":3,\"j\":3,\"ñ\":3}],\"ā\":2,\"ī\":2,\"ū\":2,\"ḍ\":[{\"ḍ\":3}],\"ḷ\":[{\"h\":4}],\"ṁ\":[{\"n\":[{\"h\":5}]},7],\"ṃ\":[{\"n\":[{\"h\":5}]},7],\"ṅ\":[{\"g\":3,\"k\":3}],\"ṇ\":[{\"h\":3,\"y\":3,\"ḍ\":3,\"ṇ\":3,\"ṭ\":3}],\"ṭ\":[{\"h\":4,\"ṭ\":3}]}',\n    [],\n];\n"], "names": [], "mappings": "AAgBO,MAAM,KAAK;AAAA,EACd;AAAA,EACA;AAAA,EACA,CAAA;AACJ;"}