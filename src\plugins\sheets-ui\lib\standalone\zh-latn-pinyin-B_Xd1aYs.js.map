{"version": 3, "file": "zh-latn-pinyin-B_Xd1aYs.js", "sources": ["../../../engine-render/src/components/docs/layout/hyphenation/patterns/zh-latn-pinyin.ts"], "sourcesContent": ["/**\n * Copyright 2023-present DreamNum Co., Ltd.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const ZhLatnPinyin = [\n    '01,1',\n    '{\"a\":[{\"b\":0,\"c\":0,\"d\":0,\"f\":0,\"g\":0,\"h\":0,\"j\":0,\"k\":0,\"l\":0,\"m\":0,\"p\":0,\"q\":0,\"r\":0,\"s\":0,\"t\":0,\"w\":0,\"x\":0,\"y\":0,\"z\":0}],\"e\":[{\"b\":0,\"c\":0,\"d\":0,\"f\":0,\"g\":0,\"h\":0,\"j\":0,\"k\":0,\"l\":0,\"m\":0,\"p\":0,\"q\":0,\"s\":0,\"t\":0,\"w\":0,\"x\":0,\"y\":0,\"z\":0}],\"g\":[{\"a\":1,\"b\":0,\"c\":0,\"d\":0,\"e\":1,\"f\":0,\"g\":0,\"h\":0,\"j\":0,\"k\":0,\"l\":0,\"m\":0,\"n\":0,\"o\":1,\"p\":0,\"q\":0,\"r\":0,\"s\":0,\"t\":0,\"u\":1,\"w\":0,\"x\":0,\"y\":0,\"z\":0}],\"i\":[{\"b\":0,\"c\":0,\"d\":0,\"f\":0,\"g\":0,\"h\":0,\"j\":0,\"k\":0,\"l\":0,\"m\":0,\"p\":0,\"q\":0,\"r\":0,\"s\":0,\"t\":0,\"w\":0,\"x\":0,\"y\":0,\"z\":0}],\"n\":[{\"a\":1,\"b\":0,\"c\":0,\"d\":0,\"e\":1,\"f\":0,\"h\":0,\"i\":1,\"j\":0,\"k\":0,\"l\":0,\"m\":0,\"n\":0,\"o\":1,\"p\":0,\"q\":0,\"r\":0,\"s\":0,\"t\":0,\"u\":1,\"ü\":1,\"w\":0,\"x\":0,\"y\":0,\"z\":0}],\"o\":[{\"b\":0,\"c\":0,\"d\":0,\"f\":0,\"g\":0,\"h\":0,\"j\":0,\"k\":0,\"l\":0,\"m\":0,\"p\":0,\"q\":0,\"r\":0,\"s\":0,\"t\":0,\"w\":0,\"x\":0,\"y\":0,\"z\":0}],\"r\":[{\"a\":1,\"b\":0,\"c\":0,\"d\":0,\"e\":1,\"f\":0,\"g\":0,\"h\":0,\"i\":1,\"j\":0,\"k\":0,\"l\":0,\"m\":0,\"n\":0,\"o\":1,\"p\":0,\"q\":0,\"r\":0,\"s\":0,\"t\":0,\"u\":1,\"w\":0,\"x\":0,\"y\":0,\"z\":0}],\"u\":[{\"b\":0,\"c\":0,\"d\":0,\"f\":0,\"g\":0,\"h\":0,\"j\":0,\"k\":0,\"l\":0,\"m\":0,\"p\":0,\"q\":0,\"r\":0,\"s\":0,\"t\":0,\"w\":0,\"x\":0,\"y\":0,\"z\":0}],\"ü\":[{\"b\":0,\"c\":0,\"d\":0,\"f\":0,\"g\":0,\"h\":0,\"j\":0,\"k\":0,\"l\":0,\"m\":0,\"n\":0,\"p\":0,\"q\":0,\"r\":0,\"s\":0,\"t\":0,\"w\":0,\"x\":0,\"y\":0,\"z\":0}],\"\\'\":[{\"a\":0,\"e\":0,\"o\":0}]}',\n    [],\n];\n"], "names": [], "mappings": ";;AAgBO,MAAM,eAAe;AAAA,EACxB;AAAA,EACA;AAAA,EACA,CAAA;AACJ;;"}