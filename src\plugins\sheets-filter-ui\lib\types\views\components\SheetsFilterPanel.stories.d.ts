import { Meta } from '@storybook/react';
import { FilterPanel } from './SheetsFilterPanel';
declare const meta: Meta<typeof FilterPanel>;
export default meta;
export declare const FilterWithConditions: {
    render(): import("react/jsx-runtime").JSX.Element;
};
export declare const FilterWithValues: {
    render(): import("react/jsx-runtime").JSX.Element;
};
export declare const FilterWithChinese: {
    render(): import("react/jsx-runtime").JSX.Element;
};
