let titles = [
  [
    {
      excel_column: "0",
      sql_column: "zero",
      name: "原表表头",
      type: "string",
      enum: [],
      is_gray: 1,
    },
    {
      excel_column: "0",
      sql_column: "zero",
      name: "",
      type: "string",
      enum: [],
      is_gray: 1,
    },
    {
      excel_column: "A",
      sql_column: "name",
      name: "招聘岗位",
      type: "string",
      enum: [],
      is_show_column: 1,
      is_gray: 1,
    },
    {
      excel_column: "B",
      sql_column: "code",
      name: "",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "C",
      sql_column: "job_nature",
      name: "岗位类别及等级",
      type: "string",
      enum: [],
      is_show_column: 1,
      is_gray: 1,
    },
    {
      excel_column: "D",
      sql_column: "job_level",
      name: "",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "E",
      sql_column: "job_desc",
      name: "",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "F",
      sql_column: "need_num",
      name: "",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "I",
      sql_column: "region_address",
      name: "",
      type: "string",
      enum: [],
      is_show_column: 1,
      is_show_modal: 1,
    },
    {
      excel_column: "J",
      sql_column: "department",
      name: "主管部门",
      type: "string",
      enum: [],
      is_show_column: 1,
      is_gray: 1,
    },
    {
      excel_column: "K",
      sql_column: "department_code",
      name: "",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "L",
      sql_column: "organ_level",
      name: "",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "M",
      sql_column: "work_unit",
      name: "招聘单位",
      type: "string",
      enum: [],
      is_show_column: 1,
      is_gray: 1,
    },
    {
      excel_column: "N",
      sql_column: "unit_nature",
      name: "",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "O",
      sql_column: "contact_phone",
      name: "联系人",
      type: "string",
      enum: [],
      is_show_column: 1,
      is_gray: 1,
    },
    {
      excel_column: "P",
      sql_column: "education_range_text",
      name: "学历（学位）",
      type: "string",
      enum: [],
      is_show_column: 1,
      is_gray: 1,
    },
    {
      excel_column: "Q",
      sql_column: "education_range",
      name: "",
      type: "array",
      enum: [
        { id: 6, name: "本科", range: [1, 2, 4, 6], has_major: 1 },
        { id: 8, name: "硕士研究生", range: [1, 2, 4, 6, 8], has_major: 1 },
        {
          id: 10,
          name: "博士研究生",
          range: [1, 2, 4, 6, 8, 10],
          has_major: 1,
        },
        { id: 4, name: "专科", range: [1, 2, 4], has_major: 1 },
        { id: 2, name: "中专", range: [2], has_major: 0 },
        { id: 1, name: "高中", range: [1], has_major: 0 },
      ],
      key: "education_list",
      is_show_column: 1,
    },
    {
      excel_column: "R",
      sql_column: "education_nature_text",
      name: "",
      type: "array",
      enum: [
        { id: 1, name: "全日制" },
        { id: 2, name: "非全日制" },
      ],
      key: "education_nature_list",
      is_show_column: 1,
    },
    {
      excel_column: "S",
      sql_column: "degree",
      name: "",
      type: "array",
      enum: [
        { id: 6, name: "学士", range: [-1, 6] },
        { id: 8, name: "硕士", range: [-1, 6, 8] },
        { id: 10, name: "博士", range: [-1, 6, 8, 10] },
        { id: -1, name: "无学位", range: [-1] },
      ],
      key: "degree_list",
      is_show_column: 1,
    },
    {
      excel_column: "T",
      sql_column: "major_text",
      name: "专业（学科）",
      type: "string",
      enum: [],
      is_show_column: 1,
      is_gray: 1,
    },
    {
      excel_column: "U",
      sql_column: "major_4",
      name: "",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "V",
      sql_column: "major_6",
      name: "",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "W",
      sql_column: "major_8",
      name: "",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "X",
      sql_column: "age_text",
      name: "年龄",
      type: "string",
      enum: [],
      is_show_column: 1,
      is_gray: 1,
    },
    {
      excel_column: "Y",
      sql_column: "birth_date_start",
      name: "",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "Y",
      sql_column: "birth_date_end",
      name: "",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "Y",
      sql_column: "min_age",
      name: "",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "Z",
      sql_column: "max_age",
      name: "",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "AA",
      sql_column: "gender",
      name: "",
      type: "array",
      enum: [
        { id: 1, name: "男" },
        { id: 2, name: "女" },
      ],
      key: "gender_list",
      is_show_column: 1,
    },
    {
      excel_column: "AB",
      sql_column: "fresh_graduate_text",
      name: "",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "AC",
      sql_column: "fresh_graduate",
      name: "",
      type: "array",
      enum: [
        { name: "是", value: 1, id: 1 },
        { name: "否", value: 0, id: 0 },
      ],
      is_show_column: 1,
    },
    {
      excel_column: "AD",
      sql_column: "politics_face_text",
      name: "",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "AE",
      sql_column: "politics_face",
      name: "",
      is_show_column: 1,
    },
    {
      excel_column: "AF",
      sql_column: "service_project_text",
      name: "",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "AG",
      sql_column: "service_project",
      name: "",
      is_show_column: 1,
    },
    {
      excel_column: "AH",
      sql_column: "work_years_text",
      name: "",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "AI",
      sql_column: "work_years",
      name: "",
      type: "array",
      enum: [
        { id: -1, name: "无工作经验" },
        { id: 1, name: "一年" },
        { id: 2, name: "二年" },
        { id: 3, name: "三年" },
        { id: 4, name: "四年" },
        { id: 5, name: "五年及以上" },
      ],
      key: "work_years_list",
      is_show_column: 1,
    },
    {
      excel_column: "AJ",
      sql_column: "household_text",
      name: "",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "AK",
      sql_column: "household",
      name: "",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "AL",
      sql_column: "service_years",
      name: "最低服务期",
      type: "string",
      enum: [],
      is_show_column: 1,
      is_gray: 1,
    },
    {
      excel_column: "AM",
      sql_column: "credential_text",
      name: "",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "AN",
      sql_column: "source_place",
      name: "",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "AO",
      sql_column: "other_require",
      name: "其他条件",
      type: "string",
      enum: [],
      is_show_column: 1,
      is_gray: 1,
    },
    {
      excel_column: "AP",
      sql_column: "paper_type",
      name: "公共科目",
      type: "string",
      enum: [],
      is_show_column: 1,
      is_gray: 1,
    },
    {
      excel_column: "AQ",
      sql_column: "remarks",
      name: "备注",
      type: "string",
      enum: [],
      is_show_column: 1,
      is_gray: 1,
    },
    {
      excel_column: "AR",
      sql_column: "interview_rate",
      name: "",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
  ],
  [
    {
      excel_column: "0",
      sql_column: "zero",
      name: "AI匹配表头",
      type: "string",
      enum: [],
      is_gray: 1,
    },
    {
      excel_column: "A",
      sql_column: "name",
      name: "职位名称",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "B",
      sql_column: "code",
      name: "职位代码",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "C",
      sql_column: "job_nature",
      name: "职位性质",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "D",
      sql_column: "job_level",
      name: "职级层次",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "E",
      sql_column: "job_desc",
      name: "职位描述",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "F",
      sql_column: "need_num",
      name: "招录人数（数字）",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "I",
      sql_column: "region_address",
      name: "报考地区",
      type: "string",
      enum: [],
      is_show_column: 1,
      is_show_modal: 1,
    },
    {
      excel_column: "J",
      sql_column: "department",
      name: "主管部门",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "K",
      sql_column: "department_code",
      name: "部门代码",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "L",
      sql_column: "organ_level",
      name: "部门层级",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "M",
      sql_column: "work_unit",
      name: "招录单位",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "N",
      sql_column: "unit_nature",
      name: "单位性质",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "O",
      sql_column: "contact_phone",
      name: "联系电话",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "P",
      sql_column: "education_range_text",
      name: "学历要求（填写文本）",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "Q",
      sql_column: "education_range",
      name: "学历要求（数据）",
      type: "array",
      enum: [
        { id: 6, name: "本科", range: [1, 2, 4, 6], has_major: 1 },
        { id: 8, name: "硕士研究生", range: [1, 2, 4, 6, 8], has_major: 1 },
        {
          id: 10,
          name: "博士研究生",
          range: [1, 2, 4, 6, 8, 10],
          has_major: 1,
        },
        { id: 4, name: "专科", range: [1, 2, 4], has_major: 1 },
        { id: 2, name: "中专", range: [2], has_major: 0 },
        { id: 1, name: "高中", range: [1], has_major: 0 },
      ],
      key: "education_list",
      is_show_column: 1,
    },
    {
      excel_column: "R",
      sql_column: "education_nature_text",
      name: "学历性质",
      type: "array",
      enum: [
        { id: 1, name: "全日制" },
        { id: 2, name: "非全日制" },
      ],
      key: "education_nature_list",
      is_show_column: 1,
    },
    {
      excel_column: "S",
      sql_column: "degree",
      name: "学位要求（数据）",
      type: "array",
      enum: [
        { id: 6, name: "学士", range: [-1, 6] },
        { id: 8, name: "硕士", range: [-1, 6, 8] },
        { id: 10, name: "博士", range: [-1, 6, 8, 10] },
        { id: -1, name: "无学位", range: [-1] },
      ],
      key: "degree_list",
      is_show_column: 1,
    },
    {
      excel_column: "T",
      sql_column: "major_text",
      name: "专业要求（填写文本）",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "U",
      sql_column: "major_4",
      name: "专科专业要求（数据）",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "V",
      sql_column: "major_6",
      name: "本科专业要求（数据）",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "W",
      sql_column: "major_8",
      name: "研究生专业要求（数据）",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "X",
      sql_column: "age_text",
      name: "年龄要求（填写文本）",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "Y",
      sql_column: "birth_date_start",
      name: "最早出生日期要求",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "Y",
      sql_column: "birth_date_end",
      name: "最晚出生日期要求",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "Y",
      sql_column: "min_age",
      name: "最小年龄要求",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "Z",
      sql_column: "max_age",
      name: "最大年龄要求",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "AA",
      sql_column: "gender",
      name: "性别要求（数据）",
      type: "array",
      enum: [
        { id: 1, name: "男" },
        { id: 2, name: "女" },
      ],
      key: "gender_list",
      is_show_column: 1,
    },
    {
      excel_column: "AB",
      sql_column: "fresh_graduate_text",
      name: "应届显示（填写文本）",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "AC",
      sql_column: "fresh_graduate",
      name: "应届限制（数据）",
      type: "array",
      enum: [
        { name: "是", value: 1, id: 1 },
        { name: "否", value: 0, id: 0 },
      ],
      is_show_column: 1,
    },
    {
      excel_column: "AD",
      sql_column: "politics_face_text",
      name: "政治面貌（填写文本）",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "AE",
      sql_column: "politics_face",
      name: "政治面貌（数据）",
      is_show_column: 1,
    },
    {
      excel_column: "AF",
      sql_column: "service_project_text",
      name: "定向招录（填写文本）",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "AG",
      sql_column: "service_project",
      name: "定向招录（数据）",
      is_show_column: 1,
    },
    {
      excel_column: "AH",
      sql_column: "work_years_text",
      name: "基层工作经验（填写文本）",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "AI",
      sql_column: "work_years",
      name: "基层工作经验（数据）",
      type: "array",
      enum: [
        { id: -1, name: "无工作经验" },
        { id: 1, name: "一年" },
        { id: 2, name: "二年" },
        { id: 3, name: "三年" },
        { id: 4, name: "四年" },
        { id: 5, name: "五年及以上" },
      ],
      key: "work_years_list",
      is_show_column: 1,
    },
    {
      excel_column: "AJ",
      sql_column: "household_text",
      name: "户籍要求（填写文本）",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "AK",
      sql_column: "household",
      name: "户籍要求（数据）",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "AL",
      sql_column: "service_years",
      name: "最低服务年限",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "AM",
      sql_column: "credential_text",
      name: "证书要求",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "AN",
      sql_column: "source_place",
      name: "生源地要求",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "AO",
      sql_column: "other_require",
      name: "其他要求",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "AP",
      sql_column: "paper_type",
      name: "试卷类型",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "AQ",
      sql_column: "remarks",
      name: "备注",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
    {
      excel_column: "AR",
      sql_column: "interview_rate",
      name: "进面比例",
      type: "string",
      enum: [],
      is_show_column: 1,
    },
  ],
];
let lists = [
  {
    id: 1059,
    article_id: 3,
    record_uuid: "1741093dd7954a4aa1205eb4a50164eb",
    uuid: "5be02295b0bd41be91da558e86ea6287",
    sheet: 1,
    sort_num: 5,
    line: 9,
    job_id: 0,
    source: "ai",
    name: "新闻编辑岗",
    code: "",
    region_province: "重庆",
    region_city: "重庆市",
    region_district: "",
    department: "市委宣传部",
    department_code: "",
    work_unit: "重庆日报报业集团",
    unit_nature: "",
    organ_level: "",
    need_num: 10,
    need_num_text: "10",
    has_tenure: -1,
    job_nature: "专技岗位（七级以上）",
    job_desc: "",
    job_level: "",
    interview_rate: "",
    contact_phone: "渝中区中山四路36号；管老师，023-63898157",
    education_range: null,
    education_range_text: "本科以上学历",
    education_nature: -1,
    education_nature_text: null,
    degree: null,
    degree_text: "本科以上学历",
    major: "",
    major_text: "不限专业",
    major_4: "",
    major_6: "不限专业",
    major_8: "不限专业",
    major_10: "不限专业",
    gender: null,
    birth_date_start: "",
    birth_date_end: "",
    min_age: 0,
    max_age: 45,
    age_text: "45周岁以下",
    fresh_graduate: "否",
    fresh_graduate_text: "",
    politics_face: "",
    politics_face_text: "",
    work_years: null,
    work_years_text: "",
    service_project: 0,
    service_project_text: "",
    household_province: "0",
    household_city: "0",
    household_district: "0",
    household_text: "",
    target_hire: "",
    service_years: "不约定服务期",
    credential: null,
    credential_text: "",
    source_place: "",
    skill_level: "",
    paper_type: "——",
    other: [
      { title: "序号", text: "5" },
      { title: "专业科目", text: "新闻类专业知识" },
      { title: "专业面试", text: "答辩" },
      { title: "综合面试", text: "——" },
      { title: "成绩计算类型", text: "戊类" },
    ],
    other_require:
      "须同时满足以下条件：1.具有新闻专业副高级以上职称；2.获得中国新闻奖",
    remarks:
      "1.获得中国新闻奖一等奖的，年龄可放宽至50周岁；2.本岗位笔试考试内容为专业科目笔试，不收取笔试考务费用",
    apply_num: 0,
    approved_num: 0,
    pay_num: 0,
    min_score: null,
    data_source: "",
    indexid: 500,
    status: 2,
    created_mcid: 0,
    updated_mcid: 0,
    created_time: "2025-07-24 16:49:41",
    updated_time: "2025-07-24 16:50:30",
    region_address: "重庆-重庆市",
  },
  {
    id: 1058,
    article_id: 3,
    record_uuid: "1741093dd7954a4aa1205eb4a50164eb",
    uuid: "4e3c49ef5b8742ac8f82097f1170578d",
    sheet: 1,
    sort_num: 4,
    line: 8,
    job_id: 0,
    source: "ai",
    name: "话务服务岗2",
    code: "",
    region_province: "重庆",
    region_city: "重庆市",
    region_district: "",
    department: "市委办公厅",
    department_code: "",
    work_unit: "市党政专用通信服务中心",
    unit_nature: "",
    organ_level: "",
    need_num: 1,
    need_num_text: "1",
    has_tenure: -1,
    job_nature: "管理岗位（九级）",
    job_desc: "",
    job_level: "",
    interview_rate: "",
    contact_phone: "渝中区中山四路36号；杨老师，023-63899155",
    education_range: null,
    education_range_text: "本科以上学历及相应学位",
    education_nature: -1,
    education_nature_text: null,
    degree: null,
    degree_text: "本科以上学历及相应学位",
    major: "",
    major_text:
      "本科：0503-新闻传播学【类】、3601-新闻出版【类】、0501-中国语言文学【类】，130309-播音与主持艺术专业、1414-播音与主持专业、360201-播音与主持专业；研究生：0552-新闻与传播【类】、0503-新闻传播学【类】、0501-中国语言文学【类】",
    major_4: "",
    major_6:
      "0503-新闻传播学【类】,3601-新闻出版【类】,0501-中国语言文学【类】,130309-播音与主持艺术专业,1414-播音与主持专业,360201-播音与主持专业",
    major_8:
      "0552-新闻与传播【类】,0503-新闻传播学【类】,0501-中国语言文学【类】",
    major_10:
      "0552-新闻与传播【类】,0503-新闻传播学【类】,0501-中国语言文学【类】",
    gender: "女",
    birth_date_start: "",
    birth_date_end: "",
    min_age: 0,
    max_age: 35,
    age_text: "35周岁以下",
    fresh_graduate: "否",
    fresh_graduate_text: "",
    politics_face: "1,2",
    politics_face_text: "中共党员,中共预备党员",
    work_years: null,
    work_years_text: "",
    service_project: 0,
    service_project_text: "",
    household_province: "0",
    household_city: "0",
    household_district: "0",
    household_text: "",
    target_hire: "",
    service_years: "不约定服务期",
    credential: null,
    credential_text: "",
    source_place: "",
    skill_level: "",
    paper_type: "综合管理类（A类）",
    other: [
      { title: "序号", text: "4" },
      { title: "专业科目", text: "——" },
      { title: "专业面试", text: "——" },
      { title: "综合面试", text: "结构化面试" },
      { title: "成绩计算类型", text: "甲类" },
    ],
    other_require:
      "须同时满足以下条件：1.中共党员（含预备党员）；2.具有普通话水平测试一级乙等及以上证书；3.限女性",
    remarks: "",
    apply_num: 0,
    approved_num: 0,
    pay_num: 0,
    min_score: null,
    data_source: "",
    indexid: 500,
    status: 2,
    created_mcid: 0,
    updated_mcid: 0,
    created_time: "2025-07-24 16:49:41",
    updated_time: "2025-07-24 16:50:30",
    region_address: "重庆-重庆市",
  },
  {
    id: 1057,
    article_id: 3,
    record_uuid: "1741093dd7954a4aa1205eb4a50164eb",
    uuid: "3607c3c586264b059062641dd439feb3",
    sheet: 1,
    sort_num: 3,
    line: 7,
    job_id: 0,
    source: "ai",
    name: "话务服务岗1",
    code: "",
    region_province: "重庆",
    region_city: "重庆市",
    region_district: "",
    department: "市委办公厅",
    department_code: "",
    work_unit: "市党政专用通信服务中心",
    unit_nature: "",
    organ_level: "",
    need_num: 1,
    need_num_text: "1",
    has_tenure: -1,
    job_nature: "管理岗位（九级）",
    job_desc: "",
    job_level: "",
    interview_rate: "",
    contact_phone: "渝中区中山四路36号；杨老师，023-63899155",
    education_range: null,
    education_range_text: "本科以上学历及相应学位",
    education_nature: -1,
    education_nature_text: null,
    degree: null,
    degree_text: "本科以上学历及相应学位",
    major: "",
    major_text:
      "本科：0503-新闻传播学【类】、3601-新闻出版【类】、0501-中国语言文学【类】，130309-播音与主持艺术专业、1414-播音与主持专业、360201-播音与主持专业；研究生：0552-新闻与传播【类】、0503-新闻传播学【类】、0501-中国语言文学【类】",
    major_4: "",
    major_6:
      "0503-新闻传播学【类】,3601-新闻出版【类】,0501-中国语言文学【类】,130309-播音与主持艺术专业,1414-播音与主持专业,360201-播音与主持专业",
    major_8:
      "0552-新闻与传播【类】,0503-新闻传播学【类】,0501-中国语言文学【类】",
    major_10:
      "0552-新闻与传播【类】,0503-新闻传播学【类】,0501-中国语言文学【类】",
    gender: "男",
    birth_date_start: "",
    birth_date_end: "",
    min_age: 0,
    max_age: 35,
    age_text: "35周岁以下",
    fresh_graduate: "否",
    fresh_graduate_text: "",
    politics_face: "1,2",
    politics_face_text: "中共党员,中共预备党员",
    work_years: null,
    work_years_text: "",
    service_project: 0,
    service_project_text: "",
    household_province: "0",
    household_city: "0",
    household_district: "0",
    household_text: "",
    target_hire: "",
    service_years: "不约定服务期",
    credential: null,
    credential_text: "",
    source_place: "",
    skill_level: "",
    paper_type: "综合管理类（A类）",
    other: [
      { title: "序号", text: "3" },
      { title: "专业科目", text: "——" },
      { title: "专业面试", text: "——" },
      { title: "综合面试", text: "结构化面试" },
      { title: "成绩计算类型", text: "甲类" },
    ],
    other_require:
      "须同时满足以下条件：1.中共党员（含预备党员）；2.具有普通话水平测试一级乙等及以上证书；3.限男性",
    remarks: "",
    apply_num: 0,
    approved_num: 0,
    pay_num: 0,
    min_score: null,
    data_source: "",
    indexid: 500,
    status: 2,
    created_mcid: 0,
    updated_mcid: 0,
    created_time: "2025-07-24 16:49:41",
    updated_time: "2025-07-24 16:50:30",
    region_address: "重庆-重庆市",
  },
  {
    id: 1056,
    article_id: 3,
    record_uuid: "1741093dd7954a4aa1205eb4a50164eb",
    uuid: "16ca2bfa430d4c02bf0eb702f2357263",
    sheet: 1,
    sort_num: 2,
    line: 6,
    job_id: 0,
    source: "ai",
    name: "通信建设岗2",
    code: "",
    region_province: "重庆",
    region_city: "重庆市",
    region_district: "",
    department: "市委办公厅",
    department_code: "",
    work_unit: "市党政专用通信服务中心",
    unit_nature: "",
    organ_level: "",
    need_num: 1,
    need_num_text: "1",
    has_tenure: -1,
    job_nature: "专技岗位（十二级）",
    job_desc: "",
    job_level: "",
    interview_rate: "",
    contact_phone: "渝中区中山四路36号；杨老师，023-63899155",
    education_range: null,
    education_range_text: "本科以上学历及相应学位",
    education_nature: -1,
    education_nature_text: null,
    degree: null,
    degree_text: "本科以上学历及相应学位",
    major: "",
    major_text:
      "本科：3103-通信【类】，310101-电子信息工程技术专业、080701-电子信息工程专业、310104-光电信息工程技术专业、080703-通信工程专业、080705-光电信息科学与工程专业、080706-信息工程专业、080707T-广播电视工程专业、080714T-电子信息科学与技术专业、080715T-电信工程及管理专业；研究生：0810-信息与通信工程【类】，085400-电子信息专业、085401-新一代电子信息技术（含量子技术等）专业、085402-通信工程（含宽带网络、移动通信等）专业、085408-光电信息工程专业",
    major_4: "",
    major_6:
      "3103-通信【类】,310101-电子信息工程技术专业,080701-电子信息工程专业,310104-光电信息工程技术专业,080703-通信工程专业,080705-光电信息科学与工程专业,080706-信息工程专业,080707T-广播电视工程专业,080714T-电子信息科学与技术专业,080715T-电信工程及管理专业",
    major_8:
      "0810-信息与通信工程【类】,085400-电子信息专业,085401-新一代电子信息技术（含量子技术等）专业,085402-通信工程（含宽带网络、移动通信等）专业,085408-光电信息工程专业",
    major_10:
      "0810-信息与通信工程【类】,085400-电子信息专业,085401-新一代电子信息技术（含量子技术等）专业,085402-通信工程（含宽带网络、移动通信等）专业,085408-光电信息工程专业",
    gender: "女",
    birth_date_start: "",
    birth_date_end: "",
    min_age: 0,
    max_age: 35,
    age_text: "35周岁以下",
    fresh_graduate: "否",
    fresh_graduate_text: "",
    politics_face: "1,2",
    politics_face_text: "中共党员,中共预备党员",
    work_years: "二年",
    work_years_text: "",
    service_project: 0,
    service_project_text: "",
    household_province: "0",
    household_city: "0",
    household_district: "0",
    household_text: "",
    target_hire: "",
    service_years: "不约定服务期",
    credential: null,
    credential_text: "",
    source_place: "",
    skill_level: "",
    paper_type: "自然科学专技类（C类）",
    other: [
      { title: "序号", text: "2" },
      { title: "专业科目", text: "——" },
      { title: "专业面试", text: "——" },
      { title: "综合面试", text: "结构化面试" },
      { title: "成绩计算类型", text: "甲类" },
    ],
    other_require:
      "须同时满足以下条件：1.中共党员（含预备党员）；2.具有2年以上岗位相关工作经历；3.限女性",
    remarks: "",
    apply_num: 0,
    approved_num: 0,
    pay_num: 0,
    min_score: null,
    data_source: "",
    indexid: 500,
    status: 2,
    created_mcid: 0,
    updated_mcid: 0,
    created_time: "2025-07-24 16:49:41",
    updated_time: "2025-07-24 16:50:30",
    region_address: "重庆-重庆市",
  },
  {
    id: 1055,
    article_id: 3,
    record_uuid: "1741093dd7954a4aa1205eb4a50164eb",
    uuid: "2696eabcff2c4da2957fedcc665ccb38",
    sheet: 1,
    sort_num: 1,
    line: 5,
    job_id: 0,
    source: "ai",
    name: "通信建设岗1",
    code: "",
    region_province: "重庆",
    region_city: "重庆市",
    region_district: "",
    department: "市委办公厅",
    department_code: "",
    work_unit: "市党政专用通信服务中心",
    unit_nature: "",
    organ_level: "",
    need_num: 1,
    need_num_text: "1",
    has_tenure: -1,
    job_nature: "专技岗位（十二级）",
    job_desc: "",
    job_level: "",
    interview_rate: "",
    contact_phone: "渝中区中山四路36号；杨老师，023-63899155",
    education_range: null,
    education_range_text: "本科以上学历及相应学位",
    education_nature: -1,
    education_nature_text: null,
    degree: null,
    degree_text: "本科以上学历及相应学位",
    major: "",
    major_text:
      "本科：3103-通信【类】，310101-电子信息工程技术专业、080701-电子信息工程专业、310104-光电信息工程技术专业、080703-通信工程专业、080705-光电信息科学与工程专业、080706-信息工程专业、080707T-广播电视工程专业、080714T-电子信息科学与技术专业、080715T-电信工程及管理专业；研究生：0810-信息与通信工程【类】，085400-电子信息专业、085401-新一代电子信息技术（含量子技术等）专业、085402-通信工程（含宽带网络、移动通信等）专业、085408-光电信息工程专业",
    major_4: "",
    major_6:
      "3103-通信【类】,310101-电子信息工程技术专业,080701-电子信息工程专业,310104-光电信息工程技术专业,080703-通信工程专业,080705-光电信息科学与工程专业,080706-信息工程专业,080707T-广播电视工程专业,080714T-电子信息科学与技术专业,080715T-电信工程及管理专业",
    major_8:
      "0810-信息与通信工程【类】,085400-电子信息专业,085401-新一代电子信息技术（含量子技术等）专业,085402-通信工程（含宽带网络、移动通信等）专业,085408-光电信息工程专业",
    major_10:
      "0810-信息与通信工程【类】,085400-电子信息专业,085401-新一代电子信息技术（含量子技术等）专业,085402-通信工程（含宽带网络、移动通信等）专业,085408-光电信息工程专业",
    gender: "男",
    birth_date_start: "",
    birth_date_end: "",
    min_age: 0,
    max_age: 35,
    age_text: "35周岁以下",
    fresh_graduate: "否",
    fresh_graduate_text: "",
    politics_face: "1,2",
    politics_face_text: "中共党员,中共预备党员",
    work_years: "二年",
    work_years_text: "",
    service_project: 0,
    service_project_text: "",
    household_province: "0",
    household_city: "0",
    household_district: "0",
    household_text: "",
    target_hire: "",
    service_years: "不约定服务期",
    credential: null,
    credential_text: "",
    source_place: "",
    skill_level: "",
    paper_type: "自然科学专技类（C类）",
    other: [
      { title: "序号", text: "1" },
      { title: "专业科目", text: "——" },
      { title: "专业面试", text: "——" },
      { title: "综合面试", text: "结构化面试" },
      { title: "成绩计算类型", text: "甲类" },
    ],
    other_require:
      "须同时满足以下条件：1.中共党员（含预备党员）；2.具有2年以上岗位相关工作经历；3.限男性",
    remarks: "",
    apply_num: 0,
    approved_num: 0,
    pay_num: 0,
    min_score: null,
    data_source: "",
    indexid: 500,
    status: 2,
    created_mcid: 0,
    updated_mcid: 0,
    created_time: "2025-07-24 16:49:41",
    updated_time: "2025-07-24 16:50:30",
    region_address: "重庆-重庆市",
  },
];
