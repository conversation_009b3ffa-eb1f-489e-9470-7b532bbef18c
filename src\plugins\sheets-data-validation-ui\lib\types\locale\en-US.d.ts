/**
 * Copyright 2023-present DreamNum Co., Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
declare const locale: {
    dataValidation: {
        title: string;
        validFail: {
            value: string;
            common: string;
            number: string;
            formula: string;
            integer: string;
            date: string;
            list: string;
            listInvalid: string;
            checkboxEqual: string;
            formulaError: string;
            listIntersects: string;
            primitive: string;
        };
        panel: {
            title: string;
            addTitle: string;
            removeAll: string;
            add: string;
            range: string;
            type: string;
            options: string;
            operator: string;
            removeRule: string;
            done: string;
            formulaPlaceholder: string;
            valuePlaceholder: string;
            formulaAnd: string;
            invalid: string;
            showWarning: string;
            rejectInput: string;
            messageInfo: string;
            showInfo: string;
            rangeError: string;
            allowBlank: string;
        };
        operators: {
            between: string;
            greaterThan: string;
            greaterThanOrEqual: string;
            lessThan: string;
            lessThanOrEqual: string;
            equal: string;
            notEqual: string;
            notBetween: string;
            legal: string;
        };
        ruleName: {
            between: string;
            greaterThan: string;
            greaterThanOrEqual: string;
            lessThan: string;
            lessThanOrEqual: string;
            equal: string;
            notEqual: string;
            notBetween: string;
            legal: string;
        };
        errorMsg: {
            between: string;
            greaterThan: string;
            greaterThanOrEqual: string;
            lessThan: string;
            lessThanOrEqual: string;
            equal: string;
            notEqual: string;
            notBetween: string;
            legal: string;
        };
        any: {
            title: string;
            error: string;
        };
        date: {
            title: string;
            operators: {
                between: string;
                greaterThan: string;
                greaterThanOrEqual: string;
                lessThan: string;
                lessThanOrEqual: string;
                equal: string;
                notEqual: string;
                notBetween: string;
                legal: string;
            };
            ruleName: {
                between: string;
                greaterThan: string;
                greaterThanOrEqual: string;
                lessThan: string;
                lessThanOrEqual: string;
                equal: string;
                notEqual: string;
                notBetween: string;
                legal: string;
            };
            errorMsg: {
                between: string;
                greaterThan: string;
                greaterThanOrEqual: string;
                lessThan: string;
                lessThanOrEqual: string;
                equal: string;
                notEqual: string;
                notBetween: string;
                legal: string;
            };
        };
        list: {
            title: string;
            name: string;
            error: string;
            emptyError: string;
            add: string;
            dropdown: string;
            options: string;
            customOptions: string;
            refOptions: string;
            formulaError: string;
            edit: string;
        };
        listMultiple: {
            title: string;
            dropdown: string;
        };
        textLength: {
            title: string;
            errorMsg: {
                between: string;
                greaterThan: string;
                greaterThanOrEqual: string;
                lessThan: string;
                lessThanOrEqual: string;
                equal: string;
                notEqual: string;
                notBetween: string;
            };
        };
        decimal: {
            title: string;
        };
        whole: {
            title: string;
        };
        checkbox: {
            title: string;
            error: string;
            tips: string;
            checked: string;
            unchecked: string;
        };
        custom: {
            title: string;
            error: string;
            validFail: string;
            ruleName: string;
        };
        alert: {
            title: string;
            ok: string;
        };
        error: {
            title: string;
        };
        renderMode: {
            arrow: string;
            chip: string;
            text: string;
            label: string;
        };
        showTime: {
            label: string;
        };
    };
};
export default locale;
