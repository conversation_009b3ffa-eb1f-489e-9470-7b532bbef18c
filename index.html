<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Univer Sheet 演示</title>
</head>
<style>
  html,
  body {
    margin: 0;
    padding: 0;
    height: 100%;
    overflow: hidden;
  }

  #app {
    height: 100vh;
    width: 100%;
  }

  #univer {
    height: 100%;
    width: 100%;
    border: none;
  }
</style>

<body>
  <div id="app">
    <div id="univer"></div>
  </div>
  <script src="./data/data.js"></script>
  <script src="./business/jobTableUniver.js"></script>

  <script type="module" src="/src/main.js"></script>

  <script>
    const SHEET_CALL_EVENT = {
      activateCellEdit(data) {
        console.log(data);
      },
    };
  </script>
  <script>
    // 等待模块加载完成后初始化 JobTableUniver
    document.addEventListener("DOMContentLoaded", function () {
      initUniverApp();
    });

    function initUniverApp() {
      // 创建 Univer 实例
      const { univerAPI, univer } = window.createUniver();
      window.univerAPI = univerAPI
      // 创建 JobTableUniver 实例
      window.jobTableUniver = new JobTableUniver({ univerAPI, univer });
      jobTableUniver.initUniverApp({
        headerDictionaryIndex: 0,
        headers: titles,
        cells: lists,
        sheetCallEvent: SHEET_CALL_EVENT,
      });
    }
  </script>
</body>

</html>