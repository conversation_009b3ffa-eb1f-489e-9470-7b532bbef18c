{"version": 3, "file": "pt-BIKGx1hO.mjs", "sources": ["../../../engine-render/src/components/docs/layout/hyphenation/patterns/pt.ts"], "sourcesContent": ["/**\n * Copyright 2023-present DreamNum Co., Ltd.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const Pt = [\n    '12,1,104,03',\n    '{\"b\":[{\"l\":0,\"r\":0,\"a\":1,\"e\":1,\"i\":1,\"o\":1,\"u\":1,\"á\":1,\"â\":1,\"ã\":1,\"é\":1,\"í\":1,\"ó\":1,\"ú\":1,\"ê\":1,\"õ\":1}],\"c\":[{\"h\":0,\"l\":0,\"r\":0,\"a\":1,\"e\":1,\"i\":1,\"o\":1,\"u\":1,\"á\":1,\"â\":1,\"ã\":1,\"é\":1,\"í\":1,\"ó\":1,\"ú\":1,\"ê\":1,\"õ\":1,\"c\":3}],\"ç\":[{\"a\":1,\"e\":1,\"i\":1,\"o\":1,\"u\":1,\"á\":1,\"â\":1,\"ã\":1,\"é\":1,\"í\":1,\"ó\":1,\"ú\":1,\"ê\":1,\"õ\":1}],\"d\":[{\"l\":0,\"r\":0,\"a\":1,\"e\":1,\"i\":1,\"o\":1,\"u\":1,\"á\":1,\"â\":1,\"ã\":1,\"é\":1,\"í\":1,\"ó\":1,\"ú\":1,\"ê\":1,\"õ\":1}],\"f\":[{\"l\":0,\"r\":0,\"a\":1,\"e\":1,\"i\":1,\"o\":1,\"u\":1,\"á\":1,\"â\":1,\"ã\":1,\"é\":1,\"í\":1,\"ó\":1,\"ú\":1,\"ê\":1,\"õ\":1}],\"g\":[{\"l\":0,\"r\":0,\"a\":1,\"e\":1,\"i\":1,\"o\":1,\"u\":[{\"a\":2,\"e\":2,\"i\":2,\"o\":2},1],\"á\":1,\"â\":1,\"ã\":1,\"é\":1,\"í\":1,\"ó\":1,\"ú\":1,\"ê\":1,\"õ\":1}],\"j\":[{\"a\":1,\"e\":1,\"i\":1,\"o\":1,\"u\":1,\"á\":1,\"â\":1,\"ã\":1,\"é\":1,\"í\":1,\"ó\":1,\"ú\":1,\"ê\":1,\"õ\":1}],\"k\":[{\"l\":0,\"r\":0,\"a\":1,\"e\":1,\"i\":1,\"o\":1,\"u\":1,\"á\":1,\"â\":1,\"ã\":1,\"é\":1,\"í\":1,\"ó\":1,\"ú\":1,\"ê\":1,\"õ\":1}],\"l\":[{\"h\":0,\"a\":1,\"e\":1,\"i\":1,\"o\":1,\"u\":1,\"á\":1,\"â\":1,\"ã\":1,\"é\":1,\"í\":1,\"ó\":1,\"ú\":1,\"ê\":1,\"õ\":1}],\"m\":[{\"a\":1,\"e\":1,\"i\":1,\"o\":1,\"u\":1,\"á\":1,\"â\":1,\"ã\":1,\"é\":1,\"í\":1,\"ó\":1,\"ú\":1,\"ê\":1,\"õ\":1}],\"n\":[{\"h\":0,\"a\":1,\"e\":1,\"i\":1,\"o\":1,\"u\":1,\"á\":1,\"â\":1,\"ã\":1,\"é\":1,\"í\":1,\"ó\":1,\"ú\":1,\"ê\":1,\"õ\":1}],\"p\":[{\"l\":0,\"r\":0,\"a\":1,\"e\":1,\"i\":1,\"o\":1,\"u\":1,\"á\":1,\"â\":1,\"ã\":1,\"é\":1,\"í\":1,\"ó\":1,\"ú\":1,\"ê\":1,\"õ\":1}],\"q\":[{\"u\":[{\"a\":2,\"e\":2,\"i\":2,\"o\":2}]}],\"r\":[{\"a\":1,\"e\":1,\"i\":1,\"o\":1,\"u\":1,\"á\":1,\"â\":1,\"ã\":1,\"é\":1,\"í\":1,\"ó\":1,\"ú\":1,\"ê\":1,\"õ\":1,\"r\":3}],\"s\":[{\"a\":1,\"e\":1,\"i\":1,\"o\":1,\"u\":1,\"á\":1,\"â\":1,\"ã\":1,\"é\":1,\"í\":1,\"ó\":1,\"ú\":1,\"ê\":1,\"õ\":1,\"s\":3}],\"t\":[{\"l\":0,\"r\":0,\"a\":1,\"e\":1,\"i\":1,\"o\":1,\"u\":1,\"á\":1,\"â\":1,\"ã\":1,\"é\":1,\"í\":1,\"ó\":1,\"ú\":1,\"ê\":1,\"õ\":1}],\"v\":[{\"l\":0,\"r\":0,\"a\":1,\"e\":1,\"i\":1,\"o\":1,\"u\":1,\"á\":1,\"â\":1,\"ã\":1,\"é\":1,\"í\":1,\"ó\":1,\"ú\":1,\"ê\":1,\"õ\":1}],\"w\":[{\"l\":0,\"r\":0}],\"x\":[{\"a\":1,\"e\":1,\"i\":1,\"o\":1,\"u\":1,\"á\":1,\"â\":1,\"ã\":1,\"é\":1,\"í\":1,\"ó\":1,\"ú\":1,\"ê\":1,\"õ\":1}],\"z\":[{\"a\":1,\"e\":1,\"i\":1,\"o\":1,\"u\":1,\"á\":1,\"â\":1,\"ã\":1,\"é\":1,\"í\":1,\"ó\":1,\"ú\":1,\"ê\":1,\"õ\":1}],\"a\":[{\"a\":3,\"e\":3,\"o\":3}],\"e\":[{\"a\":3,\"e\":3,\"o\":3}],\"i\":[{\"a\":3,\"e\":3,\"i\":3,\"o\":3,\"â\":3,\"ê\":3,\"ô\":3}],\"o\":[{\"a\":3,\"e\":3,\"o\":3}],\"u\":[{\"a\":3,\"e\":3,\"o\":3,\"u\":3}],\"-\":1}',\n    ['hard-ware', 'soft-ware'],\n];\n"], "names": [], "mappings": "AAgBO,MAAM,KAAK;AAAA,EACd;AAAA,EACA;AAAA,EACA,CAAC,aAAa,WAAW;AAC7B;"}