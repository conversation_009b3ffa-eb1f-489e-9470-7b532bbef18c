{"version": 3, "file": "or-CKqO0QN6.mjs", "sources": ["../../../engine-render/src/components/docs/layout/hyphenation/patterns/or.ts"], "sourcesContent": ["/**\n * Copyright 2023-present DreamNum Co., Ltd.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const Or = [\n    '22,11,01,1,21',\n    '{\"‍\":0,\"‌\":1,\"ଅ\":2,\"ଆ\":2,\"ଇ\":2,\"ଈ\":2,\"ଉ\":2,\"ଊ\":2,\"ଋ\":2,\"ୠ\":2,\"ଌ\":2,\"ୡ\":2,\"ଏ\":2,\"ଐ\":2,\"ଓ\":2,\"ଔ\":2,\"ା\":2,\"ି\":2,\"ୀ\":2,\"ୁ\":2,\"ୂ\":2,\"ୃ\":2,\"େ\":2,\"ୈ\":2,\"ୋ\":2,\"ୌ\":2,\"କ\":3,\"ଖ\":3,\"ଗ\":3,\"ଘ\":3,\"ଙ\":3,\"ଚ\":3,\"ଛ\":3,\"ଜ\":3,\"ଝ\":3,\"ଞ\":3,\"ଟ\":3,\"ଠ\":3,\"ଡ\":3,\"ଢ\":3,\"ଣ\":3,\"ତ\":3,\"ଥ\":3,\"ଦ\":3,\"ଧ\":3,\"ନ\":3,\"ପ\":3,\"ଫ\":3,\"ବ\":3,\"ଭ\":3,\"ମ\":3,\"ଯ\":3,\"ର\":3,\"ଲ\":3,\"ଳ\":3,\"ଵ\":3,\"ଶ\":3,\"ଷ\":3,\"ସ\":3,\"ହ\":3,\"ଂ\":4,\"ଃ\":4,\"ୗ\":4,\"ଁ\":4,\"୍\":0}',\n    [],\n];\n"], "names": [], "mappings": "AAgBO,MAAM,KAAK;AAAA,EACd;AAAA,EACA;AAAA,EACA,CAAA;AACJ;"}