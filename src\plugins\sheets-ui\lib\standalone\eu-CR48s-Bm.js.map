{"version": 3, "file": "eu-CR48s-Bm.js", "sources": ["../../../engine-render/src/components/docs/layout/hyphenation/patterns/eu.ts"], "sourcesContent": ["/**\n * Copyright 2023-present DreamNum Co., Ltd.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const Eu = [\n    '1,12,0022',\n    '{\"b\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0,\"l\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}],\"r\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}]}],\"c\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0}],\"d\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0,\"r\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}]}],\"f\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0,\"l\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}],\"r\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}]}],\"g\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0,\"l\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}],\"r\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}]}],\"j\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0}],\"k\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0,\"l\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}],\"r\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}]}],\"l\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0,\"l\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}]}],\"m\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0}],\"n\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0}],\"ñ\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0}],\"p\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0,\"l\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}],\"r\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}]}],\"q\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0}],\"r\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0,\"r\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}]}],\"s\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":[{\"b\":[{\"r\":2,\"l\":2}]},0]}],\"t\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0,\"s\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}],\"x\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}],\"z\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}],\"r\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}]}],\"v\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0}],\"w\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0}],\"x\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0}],\"y\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0}],\"z\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0}]}',\n    [],\n];\n"], "names": [], "mappings": ";;AAgBO,MAAM,KAAK;AAAA,EACd;AAAA,EACA;AAAA,EACA,CAAA;AACJ;;"}