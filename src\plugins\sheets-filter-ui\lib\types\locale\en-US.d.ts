/**
 * Copyright 2023-present DreamNum Co., Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
declare const locale: {
    'sheets-filter': {
        toolbar: {
            'smart-toggle-filter-tooltip': string;
            'clear-filter-criteria': string;
            're-calc-filter-conditions': string;
        };
        command: {
            'not-valid-filter-range': string;
        };
        shortcut: {
            'smart-toggle-filter': string;
        };
        panel: {
            'clear-filter': string;
            cancel: string;
            confirm: string;
            'by-values': string;
            'by-colors': string;
            'filter-by-cell-fill-color': string;
            'filter-by-cell-text-color': string;
            'filter-by-color-none': string;
            'by-conditions': string;
            'filter-only': string;
            'search-placeholder': string;
            'select-all': string;
            'input-values-placeholder': string;
            and: string;
            or: string;
            empty: string;
            '?': string;
            '*': string;
        };
        conditions: {
            none: string;
            empty: string;
            'not-empty': string;
            'text-contains': string;
            'does-not-contain': string;
            'starts-with': string;
            'ends-with': string;
            equals: string;
            'greater-than': string;
            'greater-than-or-equal': string;
            'less-than': string;
            'less-than-or-equal': string;
            equal: string;
            'not-equal': string;
            between: string;
            'not-between': string;
            custom: string;
        };
        msg: {
            'filter-header-forbidden': string;
        };
        date: {
            1: string;
            2: string;
            3: string;
            4: string;
            5: string;
            6: string;
            7: string;
            8: string;
            9: string;
            10: string;
            11: string;
            12: string;
        };
    };
};
export default locale;
