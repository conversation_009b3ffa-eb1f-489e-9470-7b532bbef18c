{"name": "univer-sheet-start-kit", "version": "0.9.1", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "univer-sheet-start-kit", "version": "0.9.1", "dependencies": {"@univerjs/find-replace": "^0.9.1", "@univerjs/preset-docs-core": "^0.9.1", "@univerjs/preset-sheets-data-validation": "^0.9.1", "@univerjs/preset-sheets-filter": "^0.9.1", "@univerjs/presets": "^0.9.1"}, "devDependencies": {"@univerjs/vite-plugin": "^0.5.1", "typescript": "^5.8.3", "vite": "^6.3.5"}}, "node_modules/@babel/runtime": {"version": "7.27.6", "resolved": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.27.6.tgz", "integrity": "sha512-vbavdySgbTTrmFE+EsiqUTzlOr5bzlnJtUv9PynGCAKvfQqjIXbvFdumPM/GxMDfyuGMJaJAU6TO4zc1Jf1i8Q==", "engines": {"node": ">=6.9.0"}}, "node_modules/@esbuild/win32-x64": {"version": "0.25.5", "resolved": "https://registry.npmmirror.com/@esbuild/win32-x64/-/win32-x64-0.25.5.tgz", "integrity": "sha512-TXv6YnJ8ZMVdX+SXWVBo/0p8LTcrUYngpWjvm91TMjjBQii7Oz11Lw5lbDV5Y0TzuhSJHwiH4hEtC1I42mMS0g==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/@flatten-js/interval-tree": {"version": "1.1.3", "resolved": "https://registry.npmmirror.com/@flatten-js/interval-tree/-/interval-tree-1.1.3.tgz", "integrity": "sha512-xhFWUBoHJFF77cJO1D6REjdgJEMRf2Y2Z+eKEPav8evGKcLSnj1ud5pLXQSbGuxF3VSvT1rWhMfVpXEKJLTL+A=="}, "node_modules/@floating-ui/core": {"version": "1.7.2", "resolved": "https://registry.npmmirror.com/@floating-ui/core/-/core-1.7.2.tgz", "integrity": "sha512-wNB5ooIKHQc+Kui96jE/n69rHFWAVoxn5CAzL1Xdd8FG03cgY3MLO+GF9U3W737fYDSgPWA6MReKhBQBop6Pcw==", "dependencies": {"@floating-ui/utils": "^0.2.10"}}, "node_modules/@floating-ui/dom": {"version": "1.7.2", "resolved": "https://registry.npmmirror.com/@floating-ui/dom/-/dom-1.7.2.tgz", "integrity": "sha512-7cfaOQuCS27HD7DX+6ib2OrnW+b4ZBwDNnCcT0uTyidcmyWb03FnQqJybDBoCnpdxwBSfA94UAYlRCt7mV+TbA==", "dependencies": {"@floating-ui/core": "^1.7.2", "@floating-ui/utils": "^0.2.10"}}, "node_modules/@floating-ui/react-dom": {"version": "2.1.4", "resolved": "https://registry.npmmirror.com/@floating-ui/react-dom/-/react-dom-2.1.4.tgz", "integrity": "sha512-JbbpPhp38UmXDDAu60RJmbeme37Jbgsm7NrHGgzYYFKmblzRUh6Pa641dII6LsjwF4XlScDrde2UAzDo/b9KPw==", "dependencies": {"@floating-ui/dom": "^1.7.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}, "node_modules/@floating-ui/utils": {"version": "0.2.10", "resolved": "https://registry.npmmirror.com/@floating-ui/utils/-/utils-0.2.10.tgz", "integrity": "sha512-aGTxbpbg8/b5JfU1HXSrbH3wXZuLPJcNEcZQFMxLs3oSzgtVu6nFPkbbGGUvBcUjKV2YyB9Wxxabo+HEH9tcRQ=="}, "node_modules/@grpc/grpc-js": {"version": "1.13.4", "resolved": "https://registry.npmmirror.com/@grpc/grpc-js/-/grpc-js-1.13.4.tgz", "integrity": "sha512-GsFaMXCkMqkKIvwCQjCrwH+GHbPKBjhwo/8ZuUkWHqbI73Kky9I+pQltrlT0+MWpedCoosda53lgjYfyEPgxBg==", "peer": true, "dependencies": {"@grpc/proto-loader": "^0.7.13", "@js-sdsl/ordered-map": "^4.4.2"}, "engines": {"node": ">=12.10.0"}}, "node_modules/@grpc/proto-loader": {"version": "0.7.15", "resolved": "https://registry.npmmirror.com/@grpc/proto-loader/-/proto-loader-0.7.15.tgz", "integrity": "sha512-tMXdRCfYVixjuFK+Hk0Q1s38gV9zDiDJfWL3h1rv4Qc39oILCu1TRTDt7+fGUI8K4G1Fj125Hx/ru3azECWTyQ==", "peer": true, "dependencies": {"lodash.camelcase": "^4.3.0", "long": "^5.0.0", "protobufjs": "^7.2.5", "yargs": "^17.7.2"}, "bin": {"proto-loader-gen-types": "build/bin/proto-loader-gen-types.js"}, "engines": {"node": ">=6"}}, "node_modules/@js-sdsl/ordered-map": {"version": "4.4.2", "resolved": "https://registry.npmmirror.com/@js-sdsl/ordered-map/-/ordered-map-4.4.2.tgz", "integrity": "sha512-iUKgm52T8HOE/makSxjqoWhe95ZJA1/G1sYsGev2JDKUSS14KAgg1LHb+Ba+IPow0xflbnSkOsZcO08C7w1gYw==", "peer": true, "funding": {"type": "opencollective", "url": "https://opencollective.com/js-sdsl"}}, "node_modules/@noble/ed25519": {"version": "2.3.0", "resolved": "https://registry.npmmirror.com/@noble/ed25519/-/ed25519-2.3.0.tgz", "integrity": "sha512-M7dvXL2B92/M7dw9+gzuydL8qn/jiqNHaoR3Q+cb1q1GHV7uwE17WCyFMG+Y+TZb5izcaXk5TdJRrDUxHXL78A==", "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@noble/hashes": {"version": "1.8.0", "resolved": "https://registry.npmmirror.com/@noble/hashes/-/hashes-1.8.0.tgz", "integrity": "sha512-jCs9ldd7NwzpgXDIf6P3+NrHh9/sD6CQdxHyjQI+h/6rDNo88ypBxxz45UDuZHz9r3tNz7N/VInSVoVdtXEI4A==", "engines": {"node": "^14.21.3 || >=16"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@protobufjs/aspromise": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/@protobufjs/aspromise/-/aspromise-1.1.2.tgz", "integrity": "sha512-j+gKExEuLmKwvz3OgROXtrJ2UG2x8Ch2YZUxahh+s1F2HZ+wAceUNLkvy6zKCPVRkU++ZWQrdxsUeQXmcg4uoQ==", "peer": true}, "node_modules/@protobufjs/base64": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/@protobufjs/base64/-/base64-1.1.2.tgz", "integrity": "sha512-AZkcAA5vnN/v4PDqKyMR5lx7hZttPDgClv83E//FMNhR2TMcLUhfRUBHCmSl0oi9zMgDDqRUJkSxO3wm85+XLg==", "peer": true}, "node_modules/@protobufjs/codegen": {"version": "2.0.4", "resolved": "https://registry.npmmirror.com/@protobufjs/codegen/-/codegen-2.0.4.tgz", "integrity": "sha512-YyFaikqM5sH0ziFZCN3xDC7zeGaB/d0IUb9CATugHWbd1FRFwWwt4ld4OYMPWu5a3Xe01mGAULCdqhMlPl29Jg==", "peer": true}, "node_modules/@protobufjs/eventemitter": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/@protobufjs/eventemitter/-/eventemitter-1.1.0.tgz", "integrity": "sha512-j9ednRT81vYJ9OfVuXG6ERSTdEL1xVsNgqpkxMsbIabzSo3goCjDIveeGv5d03om39ML71RdmrGNjG5SReBP/Q==", "peer": true}, "node_modules/@protobufjs/fetch": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/@protobufjs/fetch/-/fetch-1.1.0.tgz", "integrity": "sha512-lljVXpqXebpsijW71PZaCYeIcE5on1w5DlQy5WH6GLbFryLUrBD4932W/E2BSpfRJWseIL4v/KPgBFxDOIdKpQ==", "peer": true, "dependencies": {"@protobufjs/aspromise": "^1.1.1", "@protobufjs/inquire": "^1.1.0"}}, "node_modules/@protobufjs/float": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/@protobufjs/float/-/float-1.0.2.tgz", "integrity": "sha512-Ddb+kVXlXst9d+R9PfTIxh1EdNkgoRe5tOX6t01f1lYWOvJnSPDBlG241QLzcyPdoNTsblLUdujGSE4RzrTZGQ==", "peer": true}, "node_modules/@protobufjs/inquire": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/@protobufjs/inquire/-/inquire-1.1.0.tgz", "integrity": "sha512-kdSefcPdruJiFMVSbn801t4vFK7KB/5gd2fYvrxhuJYg8ILrmn9SKSX2tZdV6V+ksulWqS7aXjBcRXl3wHoD9Q==", "peer": true}, "node_modules/@protobufjs/path": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/@protobufjs/path/-/path-1.1.2.tgz", "integrity": "sha512-6JOcJ5Tm08dOHAbdR3GrvP+yUUfkjG5ePsHYczMFLq3ZmMkAD98cDgcT2iA1lJ9NVwFd4tH/iSSoe44YWkltEA==", "peer": true}, "node_modules/@protobufjs/pool": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/@protobufjs/pool/-/pool-1.1.0.tgz", "integrity": "sha512-0kELaGSIDBKvcgS4zkjz1PeddatrjYcmMWOlAuAPwAeccUrPHdUqo/J6LiymHHEiJT5NrF1UVwxY14f+fy4WQw==", "peer": true}, "node_modules/@protobufjs/utf8": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/@protobufjs/utf8/-/utf8-1.1.0.tgz", "integrity": "sha512-Vvn3zZrhQZkkBE8LSuW3em98c0FwgO4nxzv6OdSxPKJIEKY2bGbHn+mhGIPerzI4twdxaP8/0+06HBpwf345Lw==", "peer": true}, "node_modules/@radix-ui/primitive": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/@radix-ui/primitive/-/primitive-1.1.2.tgz", "integrity": "sha512-XnbHrrprsNqZKQhStrSwgRUQzoCI1glLzdw79xiZPoofhGICeZRSQ3dIxAKH1gb3OHfNf4d6f+vAv3kil2eggA=="}, "node_modules/@radix-ui/react-arrow": {"version": "1.1.7", "resolved": "https://registry.npmmirror.com/@radix-ui/react-arrow/-/react-arrow-1.1.7.tgz", "integrity": "sha512-F+M1tLhO+mlQaOWspE8Wstg+z6PwxwRd8oQ8IXceWz92kfAmalTRf0EjrouQeo7QssEPfCn05B4Ihs1K9WQ/7w==", "dependencies": {"@radix-ui/react-primitive": "2.1.3"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-collection": {"version": "1.1.7", "resolved": "https://registry.npmmirror.com/@radix-ui/react-collection/-/react-collection-1.1.7.tgz", "integrity": "sha512-Fh9rGN0MoI4ZFUNyfFVNU4y9LUz93u9/0K+yLgA2bwRojxM8JU1DyvvMBabnZPBgMWREAJvU2jjVzq+LrFUglw==", "dependencies": {"@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.3", "@radix-ui/react-slot": "1.2.3"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-compose-refs": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2.tgz", "integrity": "sha512-z4eqJvfiNnFMHIIvXP3CY57y2WJs5g2v3X0zm9mEJkrkNv4rDxu+sg9Jh8EkXyeqBkB7SOcboo9dMVqhyrACIg==", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-context": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/@radix-ui/react-context/-/react-context-1.1.2.tgz", "integrity": "sha512-jCi/QKUM2r1Ju5a3J64TH2A5SpKAgh0LpknyqdQ4m6DCV0xJ2HG1xARRwNGPQfi1SLdLWZ1OJz6F4OMBBNiGJA==", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-dialog": {"version": "1.1.14", "resolved": "https://registry.npmmirror.com/@radix-ui/react-dialog/-/react-dialog-1.1.14.tgz", "integrity": "sha512-+CpweKjqpzTmwRwcYECQcNYbI8V9VSQt0SNFKeEBLgfucbsLssU6Ppq7wUdNXEGb573bMjFhVjKVll8rmV6zMw==", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-dismissable-layer": "1.1.10", "@radix-ui/react-focus-guards": "1.1.2", "@radix-ui/react-focus-scope": "1.1.7", "@radix-ui/react-id": "1.1.1", "@radix-ui/react-portal": "1.1.9", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-primitive": "2.1.3", "@radix-ui/react-slot": "1.2.3", "@radix-ui/react-use-controllable-state": "1.2.2", "aria-hidden": "^1.2.4", "react-remove-scroll": "^2.6.3"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-direction": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/@radix-ui/react-direction/-/react-direction-1.1.1.tgz", "integrity": "sha512-1UEWRX6jnOA2y4H5WczZ44gOOjTEmlqv1uNW4GAJEO5+bauCBhv8snY65Iw5/VOS/ghKN9gr2KjnLKxrsvoMVw==", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-dismissable-layer": {"version": "1.1.10", "resolved": "https://registry.npmmirror.com/@radix-ui/react-dismissable-layer/-/react-dismissable-layer-1.1.10.tgz", "integrity": "sha512-IM1zzRV4W3HtVgftdQiiOmA0AdJlCtMLe00FXaHwgt3rAnNsIyDqshvkIW3hj/iu5hu8ERP7KIYki6NkqDxAwQ==", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-primitive": "2.1.3", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-escape-keydown": "1.1.1"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-dropdown-menu": {"version": "2.1.15", "resolved": "https://registry.npmmirror.com/@radix-ui/react-dropdown-menu/-/react-dropdown-menu-2.1.15.tgz", "integrity": "sha512-mIBnOjgwo9AH3FyKaSWoSu/dYj6VdhJ7frEPiGTeXCdUFHjl9h3mFh2wwhEtINOmYXWhdpf1rY2minFsmaNgVQ==", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-id": "1.1.1", "@radix-ui/react-menu": "2.1.15", "@radix-ui/react-primitive": "2.1.3", "@radix-ui/react-use-controllable-state": "1.2.2"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-focus-guards": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/@radix-ui/react-focus-guards/-/react-focus-guards-1.1.2.tgz", "integrity": "sha512-fyjAACV62oPV925xFCrH8DR5xWhg9KYtJT4s3u54jxp+L/hbpTY2kIeEFFbFe+a/HCE94zGQMZLIpVTPVZDhaA==", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-focus-scope": {"version": "1.1.7", "resolved": "https://registry.npmmirror.com/@radix-ui/react-focus-scope/-/react-focus-scope-1.1.7.tgz", "integrity": "sha512-t2ODlkXBQyn7jkl6TNaw/MtVEVvIGelJDCG41Okq/KwUsJBwQ4XVZsHAVUkK4mBv3ewiAS3PGuUWuY2BoK4ZUw==", "dependencies": {"@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-primitive": "2.1.3", "@radix-ui/react-use-callback-ref": "1.1.1"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-hover-card": {"version": "1.1.14", "resolved": "https://registry.npmmirror.com/@radix-ui/react-hover-card/-/react-hover-card-1.1.14.tgz", "integrity": "sha512-CPYZ24Mhirm+g6D8jArmLzjYu4Eyg3TTUHswR26QgzXBHBe64BO/RHOJKzmF/Dxb4y4f9PKyJdwm/O/AhNkb+Q==", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-dismissable-layer": "1.1.10", "@radix-ui/react-popper": "1.2.7", "@radix-ui/react-portal": "1.1.9", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-primitive": "2.1.3", "@radix-ui/react-use-controllable-state": "1.2.2"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-id": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/@radix-ui/react-id/-/react-id-1.1.1.tgz", "integrity": "sha512-kGkGegYIdQsOb4XjsfM97rXsiHaBwco+hFI66oO4s9LU+PLAC5oJ7khdOVFxkhsmlbpUqDAvXw11CluXP+jkHg==", "dependencies": {"@radix-ui/react-use-layout-effect": "1.1.1"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-menu": {"version": "2.1.15", "resolved": "https://registry.npmmirror.com/@radix-ui/react-menu/-/react-menu-2.1.15.tgz", "integrity": "sha512-tVlmA3Vb9n8SZSd+YSbuFR66l87Wiy4du+YE+0hzKQEANA+7cWKH1WgqcEX4pXqxUFQKrWQGHdvEfw00TjFiew==", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-collection": "1.1.7", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.10", "@radix-ui/react-focus-guards": "1.1.2", "@radix-ui/react-focus-scope": "1.1.7", "@radix-ui/react-id": "1.1.1", "@radix-ui/react-popper": "1.2.7", "@radix-ui/react-portal": "1.1.9", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-primitive": "2.1.3", "@radix-ui/react-roving-focus": "1.1.10", "@radix-ui/react-slot": "1.2.3", "@radix-ui/react-use-callback-ref": "1.1.1", "aria-hidden": "^1.2.4", "react-remove-scroll": "^2.6.3"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-popover": {"version": "1.1.14", "resolved": "https://registry.npmmirror.com/@radix-ui/react-popover/-/react-popover-1.1.14.tgz", "integrity": "sha512-ODz16+1iIbGUfFEfKx2HTPKizg2MN39uIOV8MXeHnmdd3i/N9Wt7vU46wbHsqA0xoaQyXVcs0KIlBdOA2Y95bw==", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-dismissable-layer": "1.1.10", "@radix-ui/react-focus-guards": "1.1.2", "@radix-ui/react-focus-scope": "1.1.7", "@radix-ui/react-id": "1.1.1", "@radix-ui/react-popper": "1.2.7", "@radix-ui/react-portal": "1.1.9", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-primitive": "2.1.3", "@radix-ui/react-slot": "1.2.3", "@radix-ui/react-use-controllable-state": "1.2.2", "aria-hidden": "^1.2.4", "react-remove-scroll": "^2.6.3"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-popper": {"version": "1.2.7", "resolved": "https://registry.npmmirror.com/@radix-ui/react-popper/-/react-popper-1.2.7.tgz", "integrity": "sha512-IUFAccz1JyKcf/RjB552PlWwxjeCJB8/4KxT7EhBHOJM+mN7LdW+B3kacJXILm32xawcMMjb2i0cIZpo+f9kiQ==", "dependencies": {"@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-arrow": "1.1.7", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.3", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/rect": "1.1.1"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-portal": {"version": "1.1.9", "resolved": "https://registry.npmmirror.com/@radix-ui/react-portal/-/react-portal-1.1.9.tgz", "integrity": "sha512-bpIxvq03if6UNwXZ+HTK71JLh4APvnXntDc6XOX8UVq4XQOVl7lwok0AvIl+b8zgCw3fSaVTZMpAPPagXbKmHQ==", "dependencies": {"@radix-ui/react-primitive": "2.1.3", "@radix-ui/react-use-layout-effect": "1.1.1"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-presence": {"version": "1.1.4", "resolved": "https://registry.npmmirror.com/@radix-ui/react-presence/-/react-presence-1.1.4.tgz", "integrity": "sha512-ueDqRbdc4/bkaQT3GIpLQssRlFgWaL/U2z/S31qRwwLWoxHLgry3SIfCwhxeQNbirEUXFa+lq3RL3oBYXtcmIA==", "dependencies": {"@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-primitive": {"version": "2.1.3", "resolved": "https://registry.npmmirror.com/@radix-ui/react-primitive/-/react-primitive-2.1.3.tgz", "integrity": "sha512-m9gTwRkhy2lvCPe6QJp4d3G1TYEUHn/FzJUtq9MjH46an1wJU+GdoGC5VLof8RX8Ft/DlpshApkhswDLZzHIcQ==", "dependencies": {"@radix-ui/react-slot": "1.2.3"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-roving-focus": {"version": "1.1.10", "resolved": "https://registry.npmmirror.com/@radix-ui/react-roving-focus/-/react-roving-focus-1.1.10.tgz", "integrity": "sha512-dT9aOXUen9JSsxnMPv/0VqySQf5eDQ6LCk5Sw28kamz8wSOW2bJdlX2Bg5VUIIcV+6XlHpWTIuTPCf/UNIyq8Q==", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-collection": "1.1.7", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-id": "1.1.1", "@radix-ui/react-primitive": "2.1.3", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-separator": {"version": "1.1.7", "resolved": "https://registry.npmmirror.com/@radix-ui/react-separator/-/react-separator-1.1.7.tgz", "integrity": "sha512-0HEb8R9E8A+jZjvmFCy/J4xhbXy3TV+9XSnGJ3KvTtjlIUy/YQ/p6UYZvi7YbeoeXdyU9+Y3scizK6hkY37baA==", "dependencies": {"@radix-ui/react-primitive": "2.1.3"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-slot": {"version": "1.2.3", "resolved": "https://registry.npmmirror.com/@radix-ui/react-slot/-/react-slot-1.2.3.tgz", "integrity": "sha512-aeNmHnBxbi2St0au6VBVC7JXFlhLlOnvIIlePNniyUNAClzmtAUEY8/pBiK3iHjufOlwA+c20/8jngo7xcrg8A==", "dependencies": {"@radix-ui/react-compose-refs": "1.1.2"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-tooltip": {"version": "1.2.7", "resolved": "https://registry.npmmirror.com/@radix-ui/react-tooltip/-/react-tooltip-1.2.7.tgz", "integrity": "sha512-Ap+fNYwKTYJ9pzqW+Xe2HtMRbQ/EeWkj2qykZ6SuEV4iS/o1bZI5ssJbk4D2r8XuDuOBVz/tIx2JObtuqU+5Zw==", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-dismissable-layer": "1.1.10", "@radix-ui/react-id": "1.1.1", "@radix-ui/react-popper": "1.2.7", "@radix-ui/react-portal": "1.1.9", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-primitive": "2.1.3", "@radix-ui/react-slot": "1.2.3", "@radix-ui/react-use-controllable-state": "1.2.2", "@radix-ui/react-visually-hidden": "1.2.3"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-use-callback-ref": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.1.tgz", "integrity": "sha512-FkBMwD+qbGQeMu1cOHnuGB6x4yzPjho8ap5WtbEJ26umhgqVXbhekKUQO+hZEL1vU92a3wHwdp0HAcqAUF5iDg==", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-use-controllable-state": {"version": "1.2.2", "resolved": "https://registry.npmmirror.com/@radix-ui/react-use-controllable-state/-/react-use-controllable-state-1.2.2.tgz", "integrity": "sha512-BjasUjixPFdS+NKkypcyyN5Pmg83Olst0+c6vGov0diwTEo6mgdqVR6hxcEgFuh4QrAs7Rc+9KuGJ9TVCj0Zzg==", "dependencies": {"@radix-ui/react-use-effect-event": "0.0.2", "@radix-ui/react-use-layout-effect": "1.1.1"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-use-effect-event": {"version": "0.0.2", "resolved": "https://registry.npmmirror.com/@radix-ui/react-use-effect-event/-/react-use-effect-event-0.0.2.tgz", "integrity": "sha512-Qp8WbZOBe+blgpuUT+lw2xheLP8q0oatc9UpmiemEICxGvFLYmHm9QowVZGHtJlGbS6A6yJ3iViad/2cVjnOiA==", "dependencies": {"@radix-ui/react-use-layout-effect": "1.1.1"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-use-escape-keydown": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.1.tgz", "integrity": "sha512-Il0+boE7w/XebUHyBjroE+DbByORGR9KKmITzbR7MyQ4akpORYP/ZmbhAr0DG7RmmBqoOnZdy2QlvajJ2QA59g==", "dependencies": {"@radix-ui/react-use-callback-ref": "1.1.1"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-use-layout-effect": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/@radix-ui/react-use-layout-effect/-/react-use-layout-effect-1.1.1.tgz", "integrity": "sha512-RbJRS4UWQFkzHTTwVymMTUv8EqYhOp8dOOviLj2ugtTiXRaRQS7GLGxZTLL1jWhMeoSCf5zmcZkqTl9IiYfXcQ==", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-use-rect": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/@radix-ui/react-use-rect/-/react-use-rect-1.1.1.tgz", "integrity": "sha512-QTYuDesS0VtuHNNvMh+CjlKJ4LJickCMUAqjlE3+j8w+RlRpwyX3apEQKGFzbZGdo7XNG1tXa+bQqIE7HIXT2w==", "dependencies": {"@radix-ui/rect": "1.1.1"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-use-size": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/@radix-ui/react-use-size/-/react-use-size-1.1.1.tgz", "integrity": "sha512-ewrXRDTAqAXlkl6t/fkXWNAhFX9I+CkKlw6zjEwk86RSPKwZr3xpBRso655aqYafwtnbpHLj6toFzmd6xdVptQ==", "dependencies": {"@radix-ui/react-use-layout-effect": "1.1.1"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-visually-hidden": {"version": "1.2.3", "resolved": "https://registry.npmmirror.com/@radix-ui/react-visually-hidden/-/react-visually-hidden-1.2.3.tgz", "integrity": "sha512-pzJq12tEaaIhqjbzpCuv/OypJY/BPavOofm+dbab+MHLajy277+1lLm6JFcGgF5eskJ6mquGirhXY2GD/8u8Ug==", "dependencies": {"@radix-ui/react-primitive": "2.1.3"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/rect": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/@radix-ui/rect/-/rect-1.1.1.tgz", "integrity": "sha512-HPwpGIzkl28mWyZqG52jiqDJ12waP11Pa1lGoiyUkIEuMLBP0oeK/C89esbXrxsky5we7dfd8U58nm0SgAWpVw=="}, "node_modules/@rc-component/portal": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/@rc-component/portal/-/portal-1.1.2.tgz", "integrity": "sha512-6f813C0IsasTZms08kfA8kPAGxbbkYToa8ALaiDIGGECU4i9hj8Plgbx0sNJDrey3EtHO30hmdaxtT0138xZcg==", "dependencies": {"@babel/runtime": "^7.18.0", "classnames": "^2.3.2", "rc-util": "^5.24.4"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/@rc-component/trigger": {"version": "2.2.7", "resolved": "https://registry.npmmirror.com/@rc-component/trigger/-/trigger-2.2.7.tgz", "integrity": "sha512-Qggj4Z0AA2i5dJhzlfFSmg1Qrziu8dsdHOihROL5Kl18seO2Eh/ZaTYt2c8a/CyGaTChnFry7BEYew1+/fhSbA==", "dependencies": {"@babel/runtime": "^7.23.2", "@rc-component/portal": "^1.1.0", "classnames": "^2.3.2", "rc-motion": "^2.0.0", "rc-resize-observer": "^1.3.1", "rc-util": "^5.44.0"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/@rollup/rollup-win32-x64-msvc": {"version": "4.44.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.44.2.tgz", "integrity": "sha512-3+QZROYfJ25PDcxFF66UEk8jGWigHJeecZILvkPkyQN7oc5BvFo4YEXFkOs154j3FTMp9mn9Ky8RCOwastduEA==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["win32"]}, "node_modules/@types/estree": {"version": "1.0.8", "resolved": "https://registry.npmmirror.com/@types/estree/-/estree-1.0.8.tgz", "integrity": "sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==", "dev": true}, "node_modules/@types/hoist-non-react-statics": {"version": "3.3.6", "resolved": "https://registry.npmmirror.com/@types/hoist-non-react-statics/-/hoist-non-react-statics-3.3.6.tgz", "integrity": "sha512-lPByRJUer/iN/xa4qpyL0qmL11DqNW81iU/IG1S3uvRUq4oKagz8VCxZjiWkumgt66YT3vOdDgZ0o32sGKtCEw==", "dependencies": {"@types/react": "*", "hoist-non-react-statics": "^3.3.0"}}, "node_modules/@types/node": {"version": "24.0.10", "resolved": "https://registry.npmmirror.com/@types/node/-/node-24.0.10.tgz", "integrity": "sha512-ENHwaH+JIRTDIEEbDK6QSQntAYGtbvdDXnMXnZaZ6k13Du1dPMmprkEHIL7ok2Wl2aZevetwTAb5S+7yIF+enA==", "peer": true, "dependencies": {"undici-types": "~7.8.0"}}, "node_modules/@types/react": {"version": "19.1.8", "resolved": "https://registry.npmmirror.com/@types/react/-/react-19.1.8.tgz", "integrity": "sha512-AwAfQ2Wa5bCx9WP8nZL2uMZWod7J7/JSplxbTmBQ5ms6QpqNYm672H0Vu9ZVKVngQ+ii4R/byguVEUZQyeg44g==", "dependencies": {"csstype": "^3.0.2"}}, "node_modules/@types/react-redux": {"version": "7.1.34", "resolved": "https://registry.npmmirror.com/@types/react-redux/-/react-redux-7.1.34.tgz", "integrity": "sha512-GdFaVjEbYv4Fthm2ZLvj1VSCedV7TqE5y1kNwnjSdBOTXuRSgowux6J8TAct15T3CKBr63UMk+2CO7ilRhyrAQ==", "dependencies": {"@types/hoist-non-react-statics": "^3.3.0", "@types/react": "*", "hoist-non-react-statics": "^3.3.0", "redux": "^4.0.0"}}, "node_modules/@univerjs-pro/collaboration": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs-pro/collaboration/-/collaboration-0.9.1.tgz", "integrity": "sha512-+VJsr8/080I2kp8Gnyi//LiP1R73O4LpkOVfUtdRyAV5rdm/ZNz5/NHI4Z3OUzjw5Qv24FFyOXepiuirb9Uhtg==", "dependencies": {"@univerjs-pro/license": "0.9.1", "@univerjs/core": "0.9.1", "@univerjs/data-validation": "0.9.1", "@univerjs/docs": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/protocol": "0.1.47-alpha.0", "@univerjs/sheets": "0.9.1", "@univerjs/sheets-conditional-formatting": "0.9.1", "@univerjs/sheets-drawing": "0.9.1", "@univerjs/sheets-filter": "0.9.1", "@univerjs/sheets-hyper-link": "0.9.1", "@univerjs/thread-comment": "0.9.1", "uuid": "^11.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}}, "node_modules/@univerjs-pro/collaboration-client": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs-pro/collaboration-client/-/collaboration-client-0.9.1.tgz", "integrity": "sha512-oe7mSwAX4trg9T9xxAmSVKgWJMcu6K4sOPSZ+FmONDC8Kdcu0IID+MpQ44pjPSUDIDefIA35k9C4lMeuylR7pw==", "dependencies": {"@univerjs-pro/collaboration": "0.9.1", "@univerjs-pro/license": "0.9.1", "@univerjs/core": "0.9.1", "@univerjs/docs": "0.9.1", "@univerjs/drawing": "0.9.1", "@univerjs/network": "0.9.1", "@univerjs/protocol": "0.1.47-alpha.0", "@univerjs/sheets": "0.9.1", "@univerjs/telemetry": "0.9.1", "crypto-js": "4.2.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/collaboration-client-ui": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs-pro/collaboration-client-ui/-/collaboration-client-ui-0.9.1.tgz", "integrity": "sha512-XD3DizFv3k6gDM++4rAfXE1LmmX3aQ+QaXT6YnfkeJnGGmfsMk9EiTqiUblylSq3H3yxxBfuCc3BXiXfByMPbA==", "dependencies": {"@univerjs-pro/collaboration": "0.9.1", "@univerjs-pro/collaboration-client": "0.9.1", "@univerjs/core": "0.9.1", "@univerjs/design": "0.9.1", "@univerjs/docs": "0.9.1", "@univerjs/docs-ui": "0.9.1", "@univerjs/drawing": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/icons": "^0.4.4", "@univerjs/network": "0.9.1", "@univerjs/protocol": "0.1.47-alpha.0", "@univerjs/rpc": "0.9.1", "@univerjs/sheets": "0.9.1", "@univerjs/sheets-ui": "0.9.1", "@univerjs/ui": "0.9.1", "crypto-js": "4.2.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/collaboration-client-ui/node_modules/@univerjs/protocol": {"version": "0.1.47-alpha.0", "resolved": "https://registry.npmmirror.com/@univerjs/protocol/-/protocol-0.1.47-alpha.0.tgz", "integrity": "sha512-ieybjEWT7CjM8XfLyMg23sEERte3fATnPy/CyILkD21ei4RPG0EnAIUWkYh2DBebrl7dGCM1JUjp98GYZz3BHw==", "engines": {"node": ">=18.0.0", "pnpm": ">=10.0.0"}, "peerDependencies": {"@grpc/grpc-js": ">=1", "rxjs": ">=7.8"}}, "node_modules/@univerjs-pro/collaboration-client/node_modules/@univerjs/protocol": {"version": "0.1.47-alpha.0", "resolved": "https://registry.npmmirror.com/@univerjs/protocol/-/protocol-0.1.47-alpha.0.tgz", "integrity": "sha512-ieybjEWT7CjM8XfLyMg23sEERte3fATnPy/CyILkD21ei4RPG0EnAIUWkYh2DBebrl7dGCM1JUjp98GYZz3BHw==", "engines": {"node": ">=18.0.0", "pnpm": ">=10.0.0"}, "peerDependencies": {"@grpc/grpc-js": ">=1", "rxjs": ">=7.8"}}, "node_modules/@univerjs-pro/collaboration/node_modules/@univerjs/protocol": {"version": "0.1.47-alpha.0", "resolved": "https://registry.npmmirror.com/@univerjs/protocol/-/protocol-0.1.47-alpha.0.tgz", "integrity": "sha512-ieybjEWT7CjM8XfLyMg23sEERte3fATnPy/CyILkD21ei4RPG0EnAIUWkYh2DBebrl7dGCM1JUjp98GYZz3BHw==", "engines": {"node": ">=18.0.0", "pnpm": ">=10.0.0"}, "peerDependencies": {"@grpc/grpc-js": ">=1", "rxjs": ">=7.8"}}, "node_modules/@univerjs-pro/docs-exchange-client": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs-pro/docs-exchange-client/-/docs-exchange-client-0.9.1.tgz", "integrity": "sha512-Ow4cu60twRB+Am6QYaAGt+5k4rRXo5JDZxWWNY5OrYZowwvXtfaxnavtDcR5ckVL2pFK/s/DpP8Dm8GNXvq4sg==", "dependencies": {"@univerjs-pro/exchange-client": "0.9.1", "@univerjs/core": "0.9.1", "@univerjs/ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}}, "node_modules/@univerjs-pro/docs-print": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs-pro/docs-print/-/docs-print-0.9.1.tgz", "integrity": "sha512-9biAed8VPzs+6BsyYhPeltD2Zi/RP9q7BpBKB8xi/rxbI95RFu8oLDGTHyaKW1rmGbCAJZaR4Bu0skybVrBh4w==", "dependencies": {"@univerjs-pro/license": "0.9.1", "@univerjs-pro/print": "0.9.1", "@univerjs/core": "0.9.1", "@univerjs/docs": "0.9.1", "@univerjs/docs-ui": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/icons": "^0.4.4", "@univerjs/network": "0.9.1", "@univerjs/ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "optionalDependencies": {"@univerjs-pro/collaboration-client": "0.9.1"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}}, "node_modules/@univerjs-pro/edit-history-loader": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs-pro/edit-history-loader/-/edit-history-loader-0.9.1.tgz", "integrity": "sha512-OspK4zq9O8muiDFAwlzJdNFAX5DsLmMDlFq400hK0DVYMvTKUqOKOWBpxGI59ZjABYQIwJuecwJDcSEqOdocnQ==", "dependencies": {"@univerjs-pro/collaboration": "0.9.1", "@univerjs-pro/collaboration-client": "0.9.1", "@univerjs-pro/edit-history-viewer": "0.9.1", "@univerjs-pro/license": "0.9.1", "@univerjs-pro/sheets-pivot": "0.9.1", "@univerjs-pro/sheets-sparkline": "0.9.1", "@univerjs-pro/sheets-sparkline-ui": "0.9.1", "@univerjs/core": "0.9.1", "@univerjs/data-validation": "0.9.1", "@univerjs/design": "0.9.1", "@univerjs/docs": "0.9.1", "@univerjs/docs-ui": "0.9.1", "@univerjs/drawing": "0.9.1", "@univerjs/drawing-ui": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/icons": "^0.4.4", "@univerjs/network": "0.9.1", "@univerjs/rpc": "0.9.1", "@univerjs/sheets": "0.9.1", "@univerjs/sheets-conditional-formatting": "0.9.1", "@univerjs/sheets-conditional-formatting-ui": "0.9.1", "@univerjs/sheets-data-validation": "0.9.1", "@univerjs/sheets-data-validation-ui": "0.9.1", "@univerjs/sheets-drawing": "0.9.1", "@univerjs/sheets-drawing-ui": "0.9.1", "@univerjs/sheets-filter": "0.9.1", "@univerjs/sheets-filter-ui": "0.9.1", "@univerjs/sheets-formula": "0.9.1", "@univerjs/sheets-formula-ui": "0.9.1", "@univerjs/sheets-hyper-link": "0.9.1", "@univerjs/sheets-hyper-link-ui": "0.9.1", "@univerjs/sheets-numfmt": "0.9.1", "@univerjs/sheets-ui": "0.9.1", "@univerjs/ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/edit-history-viewer": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs-pro/edit-history-viewer/-/edit-history-viewer-0.9.1.tgz", "integrity": "sha512-XrE1/up/BmiHqXkn6785RhU9On4BKNkNHozRCRDfi26lP5DFa0k+vVyLmqJOK8dxL3vqa6UI75aOB7B9AjNILw==", "dependencies": {"@univerjs-pro/collaboration": "0.9.1", "@univerjs-pro/collaboration-client": "0.9.1", "@univerjs-pro/collaboration-client-ui": "0.9.1", "@univerjs-pro/sheets-sparkline": "0.9.1", "@univerjs/core": "0.9.1", "@univerjs/data-validation": "0.9.1", "@univerjs/design": "0.9.1", "@univerjs/drawing": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/icons": "^0.4.4", "@univerjs/network": "0.9.1", "@univerjs/protocol": "0.1.47-alpha.0", "@univerjs/sheets": "0.9.1", "@univerjs/sheets-conditional-formatting": "0.9.1", "@univerjs/sheets-filter": "0.9.1", "@univerjs/sheets-ui": "0.9.1", "@univerjs/ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/edit-history-viewer/node_modules/@univerjs/protocol": {"version": "0.1.47-alpha.0", "resolved": "https://registry.npmmirror.com/@univerjs/protocol/-/protocol-0.1.47-alpha.0.tgz", "integrity": "sha512-ieybjEWT7CjM8XfLyMg23sEERte3fATnPy/CyILkD21ei4RPG0EnAIUWkYh2DBebrl7dGCM1JUjp98GYZz3BHw==", "engines": {"node": ">=18.0.0", "pnpm": ">=10.0.0"}, "peerDependencies": {"@grpc/grpc-js": ">=1", "rxjs": ">=7.8"}}, "node_modules/@univerjs-pro/engine-chart": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs-pro/engine-chart/-/engine-chart-0.9.1.tgz", "integrity": "sha512-uD2+WreM7wOmkGEQYLhvyBeQezWeC+8/EXU/iGcqyqij0LvzO7AZsfhRNXe4xp7/hXWV3sUO1AXSRo/jkPwr6A==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/engine-render": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/engine-formula": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs-pro/engine-formula/-/engine-formula-0.9.1.tgz", "integrity": "sha512-vPxCf2WOYAc3PNYim5wi4iDOpjj7lH6g4/FPfcjVNXNQ6TsHD7hcc/UKoSZbFytOryP6V33eKYnRlI+6OnMP9Q==", "dependencies": {"@univerjs-pro/license": "0.9.1", "@univerjs/core": "0.9.1", "@univerjs/engine-formula": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/engine-pivot": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs-pro/engine-pivot/-/engine-pivot-0.9.1.tgz", "integrity": "sha512-TIs0RO1OTVPUDEd1YEig/GS+hzAkinCLO4xhrA9YiwSY2BTBBjQ9EZRu79IHJUPbGE7NUpHEE4cDC/wrFIL/Sg==", "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}}, "node_modules/@univerjs-pro/exchange-client": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs-pro/exchange-client/-/exchange-client-0.9.1.tgz", "integrity": "sha512-9NSbz5WEH6X2Bz0Kl+uh2UsV5L6AcyxbVrH4bd6g7rXKaonB5mxcH5DfYLVZF+SeZZ8UfdlG9r6U1OOElM34nA==", "dependencies": {"@univerjs-pro/collaboration": "0.9.1", "@univerjs-pro/license": "0.9.1", "@univerjs/core": "0.9.1", "@univerjs/design": "0.9.1", "@univerjs/icons": "^0.4.4", "@univerjs/network": "0.9.1", "@univerjs/protocol": "0.1.47-alpha.0", "@univerjs/ui": "0.9.1", "pako": "^2.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/exchange-client/node_modules/@univerjs/protocol": {"version": "0.1.47-alpha.0", "resolved": "https://registry.npmmirror.com/@univerjs/protocol/-/protocol-0.1.47-alpha.0.tgz", "integrity": "sha512-ieybjEWT7CjM8XfLyMg23sEERte3fATnPy/CyILkD21ei4RPG0EnAIUWkYh2DBebrl7dGCM1JUjp98GYZz3BHw==", "engines": {"node": ">=18.0.0", "pnpm": ">=10.0.0"}, "peerDependencies": {"@grpc/grpc-js": ">=1", "rxjs": ">=7.8"}}, "node_modules/@univerjs-pro/license": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs-pro/license/-/license-0.9.1.tgz", "integrity": "sha512-LQ+wL3InBzywQphSF7SwvXCiJPUUrchuvzyO4jXr4T0/07EgE8yT6NUoESKVJqaPw6yq9WyQ/Pbw2mXqHmNlDg==", "dependencies": {"@noble/ed25519": "2.3.0", "@noble/hashes": "1.8.0", "@univerjs/core": "0.9.1", "@univerjs/engine-render": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/print": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs-pro/print/-/print-0.9.1.tgz", "integrity": "sha512-n9ZReHUhcQUhiSEQdBRd5nsXj/3E3oMLaXq4Cv+4QmQShIaBKnuVAuIdGbAqkxmDOuHbs0xh1A+xsBKkcRsoWA==", "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}}, "node_modules/@univerjs-pro/sheets-chart": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs-pro/sheets-chart/-/sheets-chart-0.9.1.tgz", "integrity": "sha512-b7LeSs5AZGquJ44LFzDBffZmdtA7jA7UjBtSgI6JvT4/UKLW7rNTjHxd+X2Khj5a1z5gCKvA1b/7U2xXD1rJ1w==", "dependencies": {"@univerjs-pro/engine-chart": "0.9.1", "@univerjs-pro/license": "0.9.1", "@univerjs/core": "0.9.1", "@univerjs/sheets": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/sheets-chart-ui": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs-pro/sheets-chart-ui/-/sheets-chart-ui-0.9.1.tgz", "integrity": "sha512-Gwnlteg/57rsSKEhKGeL7ZWP7r+5g67baRjE5jFeRdFwY+UMYYUhedoR+SL7+WmvCQ+rttD7BjhiIDt5cAk4Ig==", "dependencies": {"@univerjs-pro/engine-chart": "0.9.1", "@univerjs-pro/sheets-chart": "0.9.1", "@univerjs/core": "0.9.1", "@univerjs/design": "0.9.1", "@univerjs/drawing": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/icons": "^0.4.4", "@univerjs/sheets": "0.9.1", "@univerjs/sheets-drawing-ui": "0.9.1", "@univerjs/sheets-formula-ui": "0.9.1", "@univerjs/sheets-ui": "0.9.1", "@univerjs/ui": "0.9.1", "echarts": "^5.6.0", "echarts-wordcloud": "^2.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/sheets-exchange-client": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs-pro/sheets-exchange-client/-/sheets-exchange-client-0.9.1.tgz", "integrity": "sha512-cNPWcRy/57IfdDG6WK/Wthc5QqNbY0Y6vhrzvGkBEL+zYV47XzgR1hf34acVHSUbPhOwipa2uOPhqDJbnmyKCA==", "dependencies": {"@univerjs-pro/exchange-client": "0.9.1", "@univerjs/core": "0.9.1", "@univerjs/sheets": "0.9.1", "@univerjs/sheets-ui": "0.9.1", "@univerjs/ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}}, "node_modules/@univerjs-pro/sheets-pivot": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs-pro/sheets-pivot/-/sheets-pivot-0.9.1.tgz", "integrity": "sha512-MhjDr1Xh8TOr9lgJuMTLGxGBWD4mUBGafC7OfXbdjHkOi7+AdNmlUiRGJKMkyyIiX69PPZJYwXemsgl2JDHuFw==", "dependencies": {"@univerjs-pro/engine-pivot": "0.9.1", "@univerjs-pro/license": "0.9.1", "@univerjs/core": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/rpc": "0.9.1", "@univerjs/sheets": "0.9.1", "@univerjs/sheets-filter": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/sheets-pivot-ui": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs-pro/sheets-pivot-ui/-/sheets-pivot-ui-0.9.1.tgz", "integrity": "sha512-wlVpbW1n5gHGdRWj1MDdc20Zdn08+FaVVrOCj+FzAKnDFowBzU2Hrzd+rK4ITv+fGQannRycVJM1w2Vps5EDPA==", "dependencies": {"@univerjs-pro/engine-pivot": "0.9.1", "@univerjs-pro/sheets-pivot": "0.9.1", "@univerjs/core": "0.9.1", "@univerjs/design": "0.9.1", "@univerjs/docs-ui": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/icons": "^0.4.4", "@univerjs/sheets": "0.9.1", "@univerjs/sheets-formula-ui": "0.9.1", "@univerjs/sheets-ui": "0.9.1", "@univerjs/ui": "0.9.1", "react-beautiful-dnd": "^13.1.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/sheets-print": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs-pro/sheets-print/-/sheets-print-0.9.1.tgz", "integrity": "sha512-+0PG5vCRzZ6guh8SXS8yGpxGez5AtWNbEFEeiMB/UoyUsWcDaAIS1nIca4IRvENyo1njPNgGH+tIPDu+GWTjyg==", "dependencies": {"@univerjs-pro/license": "0.9.1", "@univerjs-pro/print": "0.9.1", "@univerjs/core": "0.9.1", "@univerjs/design": "0.9.1", "@univerjs/docs": "0.9.1", "@univerjs/docs-ui": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/icons": "^0.4.4", "@univerjs/network": "0.9.1", "@univerjs/sheets": "0.9.1", "@univerjs/sheets-ui": "0.9.1", "@univerjs/ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "optionalDependencies": {"@univerjs-pro/collaboration-client": "0.9.1"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/sheets-sparkline": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs-pro/sheets-sparkline/-/sheets-sparkline-0.9.1.tgz", "integrity": "sha512-gVvpY6MW01clmsxbbCUYPDLTDZEJr6r26torqUjdBkSPMQU7r6Pwc6sh9cOUmf1B2/M4PImg528CzekTzCTHbw==", "dependencies": {"@univerjs-pro/license": "0.9.1", "@univerjs/core": "0.9.1", "@univerjs/sheets": "0.9.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/sheets-sparkline-ui": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs-pro/sheets-sparkline-ui/-/sheets-sparkline-ui-0.9.1.tgz", "integrity": "sha512-v5KljYhsCwyx0yNhEp+47kcqXJDWPgxVde+zt7EnEq+BZPL7UMEknZQr/wnwGheDTHVIre7ryrykLiPTyL9+fg==", "dependencies": {"@univerjs-pro/sheets-sparkline": "0.9.1", "@univerjs/core": "0.9.1", "@univerjs/design": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/icons": "^0.4.4", "@univerjs/sheets": "0.9.1", "@univerjs/sheets-formula-ui": "0.9.1", "@univerjs/sheets-graphics": "0.9.1", "@univerjs/sheets-ui": "0.9.1", "@univerjs/ui": "0.9.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/thread-comment-datasource": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs-pro/thread-comment-datasource/-/thread-comment-datasource-0.9.1.tgz", "integrity": "sha512-mS3v7LeK7Kx8ZoKiPLsUAaehmhhEf9UNVYDBuwhpCMVy8I6dbQRanBwKLBeFm4yf1SH+70UIJqJUHRwOgnpAng==", "dependencies": {"@univerjs-pro/collaboration-client": "0.9.1", "@univerjs-pro/license": "0.9.1", "@univerjs/core": "0.9.1", "@univerjs/network": "0.9.1", "@univerjs/protocol": "0.1.47-alpha.0", "@univerjs/thread-comment": "0.9.1", "@univerjs/thread-comment-ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs-pro/thread-comment-datasource/node_modules/@univerjs/protocol": {"version": "0.1.47-alpha.0", "resolved": "https://registry.npmmirror.com/@univerjs/protocol/-/protocol-0.1.47-alpha.0.tgz", "integrity": "sha512-ieybjEWT7CjM8XfLyMg23sEERte3fATnPy/CyILkD21ei4RPG0EnAIUWkYh2DBebrl7dGCM1JUjp98GYZz3BHw==", "engines": {"node": ">=18.0.0", "pnpm": ">=10.0.0"}, "peerDependencies": {"@grpc/grpc-js": ">=1", "rxjs": ">=7.8"}}, "node_modules/@univerjs/core": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/core/-/core-0.9.1.tgz", "integrity": "sha512-sXcE19YJBAyhpKzy+2rBha77AJw+RQpcziK+WnEfYKLm+u+BTGGNT5QjfTzTFmCV/d7ENCNwnEM/sDmbQNFeHA==", "dependencies": {"@univerjs/protocol": "0.1.46", "@univerjs/themes": "0.9.1", "@wendellhu/redi": "0.18.3", "async-lock": "^1.4.1", "dayjs": "^1.11.13", "fast-diff": "1.3.0", "kdbush": "^4.0.2", "lodash-es": "^4.17.21", "nanoid": "5.1.5", "numfmt": "^3.2.3", "ot-json1": "^1.0.2", "rbush": "^4.0.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"@wendellhu/redi": "0.18.3", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/data-validation": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/data-validation/-/data-validation-0.9.1.tgz", "integrity": "sha512-JCb+BHBmdy+98JSxU2iZxFkL1gF2TjJHwLjHsqKkGxj+6kWr5LvxdSld1UboOKEnSuiU5WESKygaMjNNMN4txA==", "dependencies": {"@univerjs/core": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/design": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/design/-/design-0.9.1.tgz", "integrity": "sha512-fiC6xeXsJLay0KN7k5dMvfcl03zE/5RYPzW2zEwW8V2j3ELJDtipbWGFYHxmQYpyKcdaF6DQj1w+/i7UL0Vqhw==", "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@rc-component/trigger": "^2.2.6", "@univerjs/icons": "^0.4.4", "@univerjs/themes": "0.9.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "rc-dropdown": "^4.2.1", "rc-menu": "^9.16.0", "rc-picker": "^4.9.0", "rc-virtual-list": "^3.16.1", "react-grid-layout": "^1.5.1", "react-transition-group": "^4.4.5", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}}, "node_modules/@univerjs/docs": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/docs/-/docs-0.9.1.tgz", "integrity": "sha512-gi0VSYPNf99A4N/Z9NMe2pU7koQanGotbrzUoe6bZJgGVt2jVJlK+7qkW3FOhjTV2JZhufClRjTYp39D7/txmg==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/engine-render": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/docs-drawing": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/docs-drawing/-/docs-drawing-0.9.1.tgz", "integrity": "sha512-ADkqRhtFYy1QMh4uYcMAFm4qyA2YTyWAm/YZkgQ2rkPsr43kotAn33B9z8x03IDq8S1Xk/NUBic0Qik6yuxO9g==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/drawing": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}}, "node_modules/@univerjs/docs-drawing-ui": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/docs-drawing-ui/-/docs-drawing-ui-0.9.1.tgz", "integrity": "sha512-RYftKQmQ0qd1jNu6quiYP/QB9h3LR5B3Z0VNffaHA1NN9YS405yg/HzdeB+OjkD9UmWLEPrasxtTwbdbrdaEVg==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/design": "0.9.1", "@univerjs/docs": "0.9.1", "@univerjs/docs-drawing": "0.9.1", "@univerjs/docs-ui": "0.9.1", "@univerjs/drawing": "0.9.1", "@univerjs/drawing-ui": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/docs-hyper-link": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/docs-hyper-link/-/docs-hyper-link-0.9.1.tgz", "integrity": "sha512-pBt3ePqXewXJbsR1Ezy6tqTMErD5xuuT65JtOkYyobXt+J9r8H9L/awx6llBvlquO6LpqqQylxnlYwpsl5PpRg==", "dependencies": {"@univerjs/core": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}}, "node_modules/@univerjs/docs-hyper-link-ui": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/docs-hyper-link-ui/-/docs-hyper-link-ui-0.9.1.tgz", "integrity": "sha512-UPvK9X2+Xo3j2qjSD/n7puGMK7jtlS/qHAc1SZbVWYAQRFl+ztnxYcgYvKLWq8L53lcGw63AyFLqwLIsciobnQ==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/design": "0.9.1", "@univerjs/docs": "0.9.1", "@univerjs/docs-hyper-link": "0.9.1", "@univerjs/docs-ui": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/icons": "^0.4.4", "@univerjs/ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/docs-thread-comment-ui": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/docs-thread-comment-ui/-/docs-thread-comment-ui-0.9.1.tgz", "integrity": "sha512-56ZR5aG7cDv/z0CLBc/6k7Mw1MILWvp2ugXIEX7YBEEVQPEeEs20iEWRViXrbMEkBnro1/zG75Y8K4ZEJnB+Zg==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/docs": "0.9.1", "@univerjs/docs-ui": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/icons": "^0.4.4", "@univerjs/thread-comment": "0.9.1", "@univerjs/thread-comment-ui": "0.9.1", "@univerjs/ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/docs-ui": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/docs-ui/-/docs-ui-0.9.1.tgz", "integrity": "sha512-1AS0wUdiAWmCix97naVMpOMOS8YQA7/PtCpU1zEb/9E1cMPtuld5X/vS6J7cpQdjzaTHrhxv5lf7fvYZln3AGQ==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/design": "0.9.1", "@univerjs/docs": "0.9.1", "@univerjs/drawing": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/icons": "^0.4.4", "@univerjs/ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/drawing": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/drawing/-/drawing-0.9.1.tgz", "integrity": "sha512-PIu/xiKeZaNWgr5kSI+uLOs8Y3lc/RpwbV2zUP6PvAzxaVtXZ/p9x7QNNsy11Xs7qSpK/X19eoJW16qhs87JAw==", "dependencies": {"@univerjs/core": "0.9.1", "ot-json1": "^1.0.2"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/drawing-ui": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/drawing-ui/-/drawing-ui-0.9.1.tgz", "integrity": "sha512-l9CQBMFHqj8/8/GHvqtTuOZ67nECp7tn+udCB6WQvJubg7H2v2w3fOmgtOfmJZiOtbb/FP3sBK6O4Y8XiY+HLQ==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/design": "0.9.1", "@univerjs/drawing": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/icons": "^0.4.4", "@univerjs/ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/engine-formula": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/engine-formula/-/engine-formula-0.9.1.tgz", "integrity": "sha512-Gy0PmK/ht6Ou+LxDEBVy1pcJUOj+N2QjEOcOMmmXF/24S+sEp3qpjn65y1Qr1gm7ZOlv3lphxkxUD+4VuRpnbA==", "dependencies": {"@flatten-js/interval-tree": "^1.1.3", "@univerjs/core": "0.9.1", "@univerjs/engine-numfmt": "0.9.1", "@univerjs/rpc": "0.9.1", "decimal.js": "^10.5.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/engine-numfmt": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/engine-numfmt/-/engine-numfmt-0.9.1.tgz", "integrity": "sha512-YUo30nJudeOG5CNfDQaLgQ2uVX1xY6sNM9abRmcDl6cGLcI6nBhaajyPiGSzwrwl1i9HSDWGZd4li75dCgupTw==", "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}}, "node_modules/@univerjs/engine-render": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/engine-render/-/engine-render-0.9.1.tgz", "integrity": "sha512-TJJnWuFyWComoL/XzUF6IXZCLoXWSDpyzS12Yg9u0kvx+no8NeG4zLKCYJF7gMceTjFiQ+4qvBq3cbRNwRYeew==", "dependencies": {"@floating-ui/dom": "^1.7.2", "@floating-ui/utils": "^0.2.10", "@univerjs/core": "0.9.1", "cjk-regex": "^3.3.0", "franc-min": "^6.2.0", "opentype.js": "^1.3.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/find-replace": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/find-replace/-/find-replace-0.9.1.tgz", "integrity": "sha512-lZJSOSMh0bgw/hU+LPPxTHrqlOT2ke3YyLUHt+ApmCb5DKhtr4eTkgkVAlKKL3ZwGsF7M5BL58PF8uyT9MzvhQ==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/design": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/icons": "^0.4.4", "@univerjs/ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/icons": {"version": "0.4.5", "resolved": "https://registry.npmmirror.com/@univerjs/icons/-/icons-0.4.5.tgz", "integrity": "sha512-nNwiy3K8llVS4WPOLytOOhjOyelaktLy9SMx5+0HzFv0I8/SX5t6WicUzbB9WPpUIKSK7xffCNwo7kMC+1puGg==", "peerDependencies": {"react": "*", "react-dom": "*"}}, "node_modules/@univerjs/network": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/network/-/network-0.9.1.tgz", "integrity": "sha512-4wnCmaCAS6hQg8o6/G4ygpjsiXcyoyn1HtpmQlZwSSu4QEZAIrpmoVnlr6Gd2lApSwo3Xkf14wZx3mip1PoUaA==", "dependencies": {"@univerjs/core": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-docs-advanced": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/preset-docs-advanced/-/preset-docs-advanced-0.9.1.tgz", "integrity": "sha512-WjfSpVs7vP26dMrKDSgo9sDNFSgIpXJ1Wrn1cXQEmMepKBdzJCYQp3HgKROdf02C/nv/87l7YFl0WZIuYHH/hA==", "dependencies": {"@univerjs-pro/docs-exchange-client": "0.9.1", "@univerjs-pro/docs-print": "0.9.1", "@univerjs-pro/exchange-client": "0.9.1", "@univerjs-pro/license": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-docs-collaboration": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/preset-docs-collaboration/-/preset-docs-collaboration-0.9.1.tgz", "integrity": "sha512-pO+HLoaYTZhYcGPLXu/2OjbCu7Kj8zrRK8QwHlw3hlvEBHn/tSXRdd0OdmgAp3VOFoPyXcdHZwJ3leHyo6ZKSg==", "dependencies": {"@univerjs-pro/collaboration": "0.9.1", "@univerjs-pro/collaboration-client": "0.9.1", "@univerjs-pro/collaboration-client-ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-docs-core": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/preset-docs-core/-/preset-docs-core-0.9.1.tgz", "integrity": "sha512-T+G3IBdmAlco9IXxrDF2Pkk++9rKpz4saW8ZWeh2UWsUW4JBFCLMoxaU7h6xg7cz8pgbs+HEZwfsPD1Jlmc1KA==", "dependencies": {"@univerjs/design": "0.9.1", "@univerjs/docs": "0.9.1", "@univerjs/docs-ui": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/network": "0.9.1", "@univerjs/ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-docs-drawing": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/preset-docs-drawing/-/preset-docs-drawing-0.9.1.tgz", "integrity": "sha512-zWVysj6ds4FK+HLmG4/QaeHhIwq+o06wlm56ImA2869J2UEj6HZC4Wtkt1Th2slQCJtEzBWdh8a98CwDHSAquA==", "dependencies": {"@univerjs/docs-drawing": "0.9.1", "@univerjs/docs-drawing-ui": "0.9.1", "@univerjs/drawing": "0.9.1", "@univerjs/drawing-ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-docs-hyper-link": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/preset-docs-hyper-link/-/preset-docs-hyper-link-0.9.1.tgz", "integrity": "sha512-fSYaISqLL2ro0/cqQtSOn0UxZHW3lHutPUQonbzwsxqyC+IgPaPj5RlQWpjXBtjcS/drunC6/bRapDpjPUlj5A==", "dependencies": {"@univerjs/docs-hyper-link": "0.9.1", "@univerjs/docs-hyper-link-ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-docs-node-core": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/preset-docs-node-core/-/preset-docs-node-core-0.9.1.tgz", "integrity": "sha512-OTMQoTllgMmFo2iasFNdNxEywzR3ts+80dzG60Km0PExEnRkoaw5ChWlfw8tKCtcE3BOPzOuKpkIh3J4qJC41A==", "dependencies": {"@univerjs/docs": "0.9.1", "@univerjs/docs-drawing": "0.9.1", "@univerjs/docs-hyper-link": "0.9.1", "@univerjs/drawing": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/rpc-node": "0.9.1", "@univerjs/thread-comment": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-docs-thread-comment": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/preset-docs-thread-comment/-/preset-docs-thread-comment-0.9.1.tgz", "integrity": "sha512-7gaerTLpZnbaBAY8JcvdXxF+cl3oOJGhoCQLU36dNpB0kTki/h/3+Z+TyfZBx5Vz605OHfExsgkeTw3vj2MzRg==", "dependencies": {"@univerjs/docs-thread-comment-ui": "0.9.1", "@univerjs/thread-comment": "0.9.1", "@univerjs/thread-comment-ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-sheets-advanced": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/preset-sheets-advanced/-/preset-sheets-advanced-0.9.1.tgz", "integrity": "sha512-z4zFJB4zpgmdMXfCPxbghMe0F7zPooNMqNf1B8hvtClLWiH1gz0Mg7hleEio72T6OKc3L76uoiVhcb0S4nZ7jg==", "dependencies": {"@univerjs-pro/engine-chart": "0.9.1", "@univerjs-pro/engine-formula": "0.9.1", "@univerjs-pro/exchange-client": "0.9.1", "@univerjs-pro/license": "0.9.1", "@univerjs-pro/sheets-chart": "0.9.1", "@univerjs-pro/sheets-chart-ui": "0.9.1", "@univerjs-pro/sheets-exchange-client": "0.9.1", "@univerjs-pro/sheets-pivot": "0.9.1", "@univerjs-pro/sheets-pivot-ui": "0.9.1", "@univerjs-pro/sheets-print": "0.9.1", "@univerjs-pro/sheets-sparkline": "0.9.1", "@univerjs-pro/sheets-sparkline-ui": "0.9.1", "@univerjs/sheets-graphics": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-sheets-collaboration": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/preset-sheets-collaboration/-/preset-sheets-collaboration-0.9.1.tgz", "integrity": "sha512-/U3PAO1s8MoV00VW996HvS2aHoriqHRDENBAoK58qC/CJe9nRnoZM9RfNEpFxEaVsjpTlc9oi2yNFNY5w9Nufg==", "dependencies": {"@univerjs-pro/collaboration": "0.9.1", "@univerjs-pro/collaboration-client": "0.9.1", "@univerjs-pro/collaboration-client-ui": "0.9.1", "@univerjs-pro/edit-history-loader": "0.9.1", "@univerjs-pro/edit-history-viewer": "0.9.1", "@univerjs-pro/thread-comment-datasource": "0.9.1", "@univerjs/preset-sheets-advanced": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-sheets-conditional-formatting": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/preset-sheets-conditional-formatting/-/preset-sheets-conditional-formatting-0.9.1.tgz", "integrity": "sha512-lrW7tUzkaGTheac5AcR9gylrYBN+/S0fYage9ZtWmiLJqZs/lqhUaACekawZ1aYpcx8HwCQRNRTBO1aMCcSvVA==", "dependencies": {"@univerjs/sheets-conditional-formatting": "0.9.1", "@univerjs/sheets-conditional-formatting-ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-sheets-core": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/preset-sheets-core/-/preset-sheets-core-0.9.1.tgz", "integrity": "sha512-+kfztwY6SaKgfkYMD91I4Mr8G6OgPMvAujRi+C3qsA5zlOMgaR3CnaZ2uAAe5/qPi9ed8xhE22IsEP+hBUoDTg==", "dependencies": {"@univerjs/design": "0.9.1", "@univerjs/docs": "0.9.1", "@univerjs/docs-ui": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/engine-numfmt": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/network": "0.9.1", "@univerjs/rpc": "0.9.1", "@univerjs/sheets": "0.9.1", "@univerjs/sheets-formula": "0.9.1", "@univerjs/sheets-formula-ui": "0.9.1", "@univerjs/sheets-numfmt": "0.9.1", "@univerjs/sheets-numfmt-ui": "0.9.1", "@univerjs/sheets-ui": "0.9.1", "@univerjs/ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-sheets-data-validation": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/preset-sheets-data-validation/-/preset-sheets-data-validation-0.9.1.tgz", "integrity": "sha512-rdjuB7gzxRXyJ7iX377r3Qk66KDSKkvaMomIaO7wf4ZTf9WMH9EFFfYyPziOc09gZoAXib0zFGAXlLByOcADqQ==", "dependencies": {"@univerjs/data-validation": "0.9.1", "@univerjs/sheets-data-validation": "0.9.1", "@univerjs/sheets-data-validation-ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-sheets-drawing": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/preset-sheets-drawing/-/preset-sheets-drawing-0.9.1.tgz", "integrity": "sha512-MBXnQDLw6imaRTYQENp2zH+2hJNNaxd5mzV54XZSIQK991Ks4TdtVOiQpLxS8K368/YdO85JycU8ipjEZvwaRA==", "dependencies": {"@univerjs/docs-drawing": "0.9.1", "@univerjs/drawing": "0.9.1", "@univerjs/drawing-ui": "0.9.1", "@univerjs/sheets-drawing": "0.9.1", "@univerjs/sheets-drawing-ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-sheets-filter": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/preset-sheets-filter/-/preset-sheets-filter-0.9.1.tgz", "integrity": "sha512-7JyzpPyEkDuJYMlUpV60RqlHtAGwW5ewwh5ScdFYoSX15aeK3n2teGPxmDaUEBzR6A8VcXxlI+LfPtEYazPj/A==", "dependencies": {"@univerjs/sheets-filter": "0.9.1", "@univerjs/sheets-filter-ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-sheets-find-replace": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/preset-sheets-find-replace/-/preset-sheets-find-replace-0.9.1.tgz", "integrity": "sha512-trMKYl6vB+BA6xfnN67iFxOAfA5yPimyWCLgmwtDbyQAI2XQ8VzVNyORjqJxup8KPb10X22GtMgllT/ngd6JYw==", "dependencies": {"@univerjs/find-replace": "0.9.1", "@univerjs/sheets-find-replace": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-sheets-hyper-link": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/preset-sheets-hyper-link/-/preset-sheets-hyper-link-0.9.1.tgz", "integrity": "sha512-ioYgWyilg+P99uwMIvpM/chDRMEPZbXiOh+jX0qF8y+7paMVrRTVE+ehiPKjbTqeVrVzY2p/jcZCmsw187VHjQ==", "dependencies": {"@univerjs/sheets-hyper-link": "0.9.1", "@univerjs/sheets-hyper-link-ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-sheets-node-core": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/preset-sheets-node-core/-/preset-sheets-node-core-0.9.1.tgz", "integrity": "sha512-avSuv6TNQ6flEkP0OjzDC95q4k0uN84YIVuPunDNZ/Tx8ClppPmF22fjLuoI35xQzPS5kVraMAOyDJLq9TEB4w==", "dependencies": {"@univerjs/docs": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/rpc-node": "0.9.1", "@univerjs/sheets": "0.9.1", "@univerjs/sheets-data-validation": "0.9.1", "@univerjs/sheets-drawing": "0.9.1", "@univerjs/sheets-filter": "0.9.1", "@univerjs/sheets-formula": "0.9.1", "@univerjs/sheets-hyper-link": "0.9.1", "@univerjs/sheets-numfmt": "0.9.1", "@univerjs/sheets-sort": "0.9.1", "@univerjs/sheets-thread-comment": "0.9.1", "@univerjs/thread-comment": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-sheets-note": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/preset-sheets-note/-/preset-sheets-note-0.9.1.tgz", "integrity": "sha512-pH9vvH7Ef268mxknqsLN3WE4OvqU60RxQZkXbIeXZjWkwoPhUOqoQmAdIeeQYmzQCs8i2wuwbitipYhuMaC84A==", "dependencies": {"@univerjs/sheets-note": "0.9.1", "@univerjs/sheets-note-ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-sheets-sort": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/preset-sheets-sort/-/preset-sheets-sort-0.9.1.tgz", "integrity": "sha512-s0Ylpe8rBs0ZgiRVp8SWS3o+I6sTMl6IjBE62bxNc9QW7BO53+zQuUTS5jkK044CRoVhiXiMEeX56bSeQWUd8A==", "dependencies": {"@univerjs/sheets-sort": "0.9.1", "@univerjs/sheets-sort-ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-sheets-table": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/preset-sheets-table/-/preset-sheets-table-0.9.1.tgz", "integrity": "sha512-hgtLuI0fyOhNPbkPLu34eiE8bXb7BKIShSI9slpGNJzi5Fg77iCr5eRZnYnCXOXcKr9decpOCgGO2XjyqsSXzQ==", "dependencies": {"@univerjs/sheets-table": "0.9.1", "@univerjs/sheets-table-ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/preset-sheets-thread-comment": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/preset-sheets-thread-comment/-/preset-sheets-thread-comment-0.9.1.tgz", "integrity": "sha512-1nll1GTkkAUYN13B9Foku+BNLRzFkn4mVS354L+CyWijJa3STnxe8mUICWiWoA0Sx6Tj0BdyK3YLZ28qo57Nbg==", "dependencies": {"@univerjs/sheets-thread-comment": "0.9.1", "@univerjs/sheets-thread-comment-ui": "0.9.1", "@univerjs/thread-comment": "0.9.1", "@univerjs/thread-comment-ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/presets": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/presets/-/presets-0.9.1.tgz", "integrity": "sha512-lkMOXB7AkWSjeoJu1S7N4Bk8yKeNYi4KFyIgeUDUe7oL9ocbcsQEugGiigBeCZ12VsLRJSYCfD9aEz7vKM6QGQ==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/design": "0.9.1", "@univerjs/preset-docs-advanced": "0.9.1", "@univerjs/preset-docs-collaboration": "0.9.1", "@univerjs/preset-docs-core": "0.9.1", "@univerjs/preset-docs-drawing": "0.9.1", "@univerjs/preset-docs-hyper-link": "0.9.1", "@univerjs/preset-docs-node-core": "0.9.1", "@univerjs/preset-docs-thread-comment": "0.9.1", "@univerjs/preset-sheets-advanced": "0.9.1", "@univerjs/preset-sheets-collaboration": "0.9.1", "@univerjs/preset-sheets-conditional-formatting": "0.9.1", "@univerjs/preset-sheets-core": "0.9.1", "@univerjs/preset-sheets-data-validation": "0.9.1", "@univerjs/preset-sheets-drawing": "0.9.1", "@univerjs/preset-sheets-filter": "0.9.1", "@univerjs/preset-sheets-find-replace": "0.9.1", "@univerjs/preset-sheets-hyper-link": "0.9.1", "@univerjs/preset-sheets-node-core": "0.9.1", "@univerjs/preset-sheets-note": "0.9.1", "@univerjs/preset-sheets-sort": "0.9.1", "@univerjs/preset-sheets-table": "0.9.1", "@univerjs/preset-sheets-thread-comment": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/protocol": {"version": "0.1.46", "resolved": "https://registry.npmmirror.com/@univerjs/protocol/-/protocol-0.1.46.tgz", "integrity": "sha512-nTkNocMt1XItBierjz7J4bz0Ye+SlUz7SDOyfqJB4FPpvDnBVGQH9NXbZbTrKrvskUzOayb7hGOkKNZyHaSQOw==", "engines": {"node": ">=18.0.0", "pnpm": ">=10.0.0"}, "peerDependencies": {"@grpc/grpc-js": ">=1", "rxjs": ">=7.8"}}, "node_modules/@univerjs/rpc": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/rpc/-/rpc-0.9.1.tgz", "integrity": "sha512-b2UMdaNslgJ87m99AL33bL6oYMxWpbYTJ09yAD7TFocPZkvESsUm3FMHWvSLqNzpjx6Kyty7Sv6vhcvbKdX55A==", "dependencies": {"@univerjs/core": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/rpc-node": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/rpc-node/-/rpc-node-0.9.1.tgz", "integrity": "sha512-1Fj0vWDsmPa5tg+5EBmhU+8mWnM//F3ObiVbgrYzAk8A7piSnSj55bw9Fwnpdq/wXWcGus1hScH0IL4lEcp5rA==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/rpc": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/sheets/-/sheets-0.9.1.tgz", "integrity": "sha512-FQrN0cM4cwa6ND1UdboJS5xdALkuUFoVxC4RGUqKfa7hpKu9gaLKv7TmIOxXpqL1LlKtqzWazJtcx8e57OJ4AQ==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/engine-numfmt": "0.9.1", "@univerjs/protocol": "0.1.46", "@univerjs/rpc": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-conditional-formatting": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/sheets-conditional-formatting/-/sheets-conditional-formatting-0.9.1.tgz", "integrity": "sha512-K9HA7gro39AnxCuDV2n/bZY+JIV8Tkb9FqO8qXxXwQ20G7cf0aR2keCxXX7Iy53RBxGTo3idZbkInewVLP3hHA==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/sheets": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-conditional-formatting-ui": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/sheets-conditional-formatting-ui/-/sheets-conditional-formatting-ui-0.9.1.tgz", "integrity": "sha512-1Zo1Sgpd3LY8GzeTDDp7OXXngxCoNGARMDiYYZQOH0uUbW94wigGIkbbbBVqbKE4NwEtjeaiOJanu1wJQA/OzA==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/design": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/icons": "^0.4.4", "@univerjs/sheets": "0.9.1", "@univerjs/sheets-conditional-formatting": "0.9.1", "@univerjs/sheets-formula-ui": "0.9.1", "@univerjs/sheets-ui": "0.9.1", "@univerjs/ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-data-validation": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/sheets-data-validation/-/sheets-data-validation-0.9.1.tgz", "integrity": "sha512-6AW/Z1VduG/F4jxry5ZNxj64wuzzuD6+scZJZxPRWTL4fl5m7ZrT4/VCzrs7QEfLVWUQnVRvyFZPo5G6ZU0ebw==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/data-validation": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/protocol": "0.1.46", "@univerjs/sheets": "0.9.1", "@univerjs/sheets-formula": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-data-validation-ui": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/sheets-data-validation-ui/-/sheets-data-validation-ui-0.9.1.tgz", "integrity": "sha512-V66IKaUJ46A6BOjJpkk37W7gbZ8ywCGquRfYXEERGPaYpHHUQDkPU6zx9qrCTYk2iRfoMayvqLRLjWrGMR2thg==", "dependencies": {"@flatten-js/interval-tree": "^1.1.3", "@univerjs/core": "0.9.1", "@univerjs/data-validation": "0.9.1", "@univerjs/design": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/icons": "^0.4.4", "@univerjs/sheets": "0.9.1", "@univerjs/sheets-data-validation": "0.9.1", "@univerjs/sheets-formula-ui": "0.9.1", "@univerjs/sheets-numfmt": "0.9.1", "@univerjs/sheets-ui": "0.9.1", "@univerjs/ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-drawing": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/sheets-drawing/-/sheets-drawing-0.9.1.tgz", "integrity": "sha512-r+h8PZHin0FmNMOeT1MhTA9ovif0rVxkZXAXEYDt9HvpyAE3n2UFzwVfFbx4PFyWdxeV1FfNgueH/U6JYJPCDw==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/drawing": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}}, "node_modules/@univerjs/sheets-drawing-ui": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/sheets-drawing-ui/-/sheets-drawing-ui-0.9.1.tgz", "integrity": "sha512-Aqr8Xnaq38Rl9cZ3RQzj3a4Tq+UeCm+0J+juECfMhSXOr0TG8/E0kOzkH+e8zutzXs8QMJjBaoF1xZFxhi3J+Q==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/design": "0.9.1", "@univerjs/docs-drawing": "0.9.1", "@univerjs/docs-ui": "0.9.1", "@univerjs/drawing": "0.9.1", "@univerjs/drawing-ui": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/sheets": "0.9.1", "@univerjs/sheets-drawing": "0.9.1", "@univerjs/sheets-ui": "0.9.1", "@univerjs/ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-filter": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/sheets-filter/-/sheets-filter-0.9.1.tgz", "integrity": "sha512-DhygRouXS5EZzNDY4F4+wLdQManyUAnTPZbtvEPhU5kgJ9aQpsyIAGaq2U+PnJHMtZtsD+o/ozUyhRQqtU3LBw==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/rpc": "0.9.1", "@univerjs/sheets": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-filter-ui": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/sheets-filter-ui/-/sheets-filter-ui-0.9.1.tgz", "integrity": "sha512-nUH/fVD7aQctMixf//6K/g43ksIp6vcwIoZqchnNeogyIPhiwGNBz/qCp+jISHSVEOwlHLo+TX6sT/Wnf/L7cA==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/design": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/icons": "^0.4.4", "@univerjs/rpc": "0.9.1", "@univerjs/sheets": "0.9.1", "@univerjs/sheets-filter": "0.9.1", "@univerjs/sheets-ui": "0.9.1", "@univerjs/ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-find-replace": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/sheets-find-replace/-/sheets-find-replace-0.9.1.tgz", "integrity": "sha512-W0el15hMkWZjAHdK1gwktNHtPwV2emLTH1kqq4+ZSfz1dHJnKnwuqdraOC1aKW/IEkUcqgYHLPpX4mjk0TOEjA==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/find-replace": "0.9.1", "@univerjs/sheets": "0.9.1", "@univerjs/sheets-ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-formula": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/sheets-formula/-/sheets-formula-0.9.1.tgz", "integrity": "sha512-zHOrvqYS+TGPqaXAB+oBK8zw1zxmcYKn1J8mNO+AfmLhDpVUqXFzU4PL6xKks9UrSVf7M+8wBzDRVwoPnV9LFg==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/rpc": "0.9.1", "@univerjs/sheets": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-formula-ui": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/sheets-formula-ui/-/sheets-formula-ui-0.9.1.tgz", "integrity": "sha512-mNQ9FGJxz5yFH5BTTUYMW+SFDWyVE+/TPTuhQVDTzgNxtxUi8XNwDfxjP+h3kIcBgRvVM1DraZmF+4C61L9bMg==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/design": "0.9.1", "@univerjs/docs": "0.9.1", "@univerjs/docs-ui": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/icons": "^0.4.4", "@univerjs/sheets": "0.9.1", "@univerjs/sheets-formula": "0.9.1", "@univerjs/sheets-ui": "0.9.1", "@univerjs/ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-graphics": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/sheets-graphics/-/sheets-graphics-0.9.1.tgz", "integrity": "sha512-hktlthGZf8oO/Lf0OOr7qKXNhT93niJlQd78RiKav1+eUnIw3Z9embwl6gIvfg5rY84JSmpCMikbUoEKZGnfZQ==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/sheets-ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}}, "node_modules/@univerjs/sheets-hyper-link": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/sheets-hyper-link/-/sheets-hyper-link-0.9.1.tgz", "integrity": "sha512-Or4JCRZF30skvg0yKq8h0OoVmQ7DZCthm/PrH4s3JzQVtbCuHIDSkNoLAphQSOq7YDbdJXvPOKjKNaec6xbDQg==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/docs": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/sheets": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-hyper-link-ui": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/sheets-hyper-link-ui/-/sheets-hyper-link-ui-0.9.1.tgz", "integrity": "sha512-3Sx2gXjIyoEA3At+H6gFa601nksvQ8Uu77I+L90mt9232eDCoHgnW2cJBvWyGTG+zWGLvtgE46ZptWMIHDV8BQ==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/design": "0.9.1", "@univerjs/docs": "0.9.1", "@univerjs/docs-ui": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/icons": "^0.4.4", "@univerjs/sheets": "0.9.1", "@univerjs/sheets-data-validation": "0.9.1", "@univerjs/sheets-formula-ui": "0.9.1", "@univerjs/sheets-hyper-link": "0.9.1", "@univerjs/sheets-ui": "0.9.1", "@univerjs/ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-note": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/sheets-note/-/sheets-note-0.9.1.tgz", "integrity": "sha512-KQdIj+zHGRFWfa6b2dGNarFbXvdXax1bcGcPSCIFMrCm+q8AH2Ybzv2pQAR0OefgRSYVMpJuxEwXkAkh0swiUA==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/sheets": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-note-ui": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/sheets-note-ui/-/sheets-note-ui-0.9.1.tgz", "integrity": "sha512-IzSPKWLlRteNs1S4msEBLsDf3lsaVIPHWmTgGEkKBgNp6bBJUpxVRdN0zAsKM9AV523XeeDOTLyUFhqBr6Vsug==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/design": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/icons": "^0.4.4", "@univerjs/sheets": "0.9.1", "@univerjs/sheets-note": "0.9.1", "@univerjs/sheets-ui": "0.9.1", "@univerjs/ui": "0.9.1", "rxjs": "^7.8.2"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-numfmt": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/sheets-numfmt/-/sheets-numfmt-0.9.1.tgz", "integrity": "sha512-EaD4W++WijeYinTpWOjxv5P3M7N3PiaOwFw09dzUAPV9PpOEvnSdC7B5dRpSCQIsRqxNi8HJbD/E91gDGl62eQ==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/engine-numfmt": "0.9.1", "@univerjs/sheets": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-numfmt-ui": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/sheets-numfmt-ui/-/sheets-numfmt-ui-0.9.1.tgz", "integrity": "sha512-lgxvYMqX9UsefPqGfzrfqdp6XaX3uXEQEWbEcNmplGBVx55d+l6IqMGFyx6iNV8Nu+q2wvwZFCjNC6AU7ilOGA==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/design": "0.9.1", "@univerjs/engine-numfmt": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/icons": "^0.4.4", "@univerjs/sheets": "0.9.1", "@univerjs/sheets-numfmt": "0.9.1", "@univerjs/sheets-ui": "0.9.1", "@univerjs/ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-sort": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/sheets-sort/-/sheets-sort-0.9.1.tgz", "integrity": "sha512-aZC9p6jRsO2JBiRY1cBLtbCyccTywb+vB/L+aRPhNIjPWnzYg9QHHja/lt8le8TUlkw+lJAp3BEvbaS/ehGrlg==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/sheets": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}}, "node_modules/@univerjs/sheets-sort-ui": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/sheets-sort-ui/-/sheets-sort-ui-0.9.1.tgz", "integrity": "sha512-FjESv+Zil9jfrvgf5+pupiuUZPQFv8zwc4v/EaCelKKvwlTU9z4adRBF3fpLFmIfoy/ebW2ZnJQpYvBAHZ19QA==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/design": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/icons": "^0.4.4", "@univerjs/sheets": "0.9.1", "@univerjs/sheets-sort": "0.9.1", "@univerjs/sheets-ui": "0.9.1", "@univerjs/ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-table": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/sheets-table/-/sheets-table-0.9.1.tgz", "integrity": "sha512-QgR8urMINXMkut//8gEJK6H39+iDqBbKEo1RsqWHj5z6xb+yP8KsF1bpxM2Eumb+W+hnXN+olrQa/d8IxXH+bA==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/sheets": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-table-ui": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/sheets-table-ui/-/sheets-table-ui-0.9.1.tgz", "integrity": "sha512-vo/9/Fg0IdTk0ur1gJEotJPQluR0h9a5D86cpVG2N4f0jajdgxQy/dAk3BqvRIcWqe/1kdM2yXsL41N/oVH1Ow==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/design": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/icons": "^0.4.4", "@univerjs/sheets": "0.9.1", "@univerjs/sheets-formula-ui": "0.9.1", "@univerjs/sheets-sort": "0.9.1", "@univerjs/sheets-table": "0.9.1", "@univerjs/sheets-ui": "0.9.1", "@univerjs/ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-thread-comment": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/sheets-thread-comment/-/sheets-thread-comment-0.9.1.tgz", "integrity": "sha512-M/Cd+SABevBxgDtvNJxsVLoHt034VlW+QqM7nxZMmt1vEYvkwKDBC4tDImT6AtIGF5mD9ybYM2Hz2HpQQbgnPg==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/sheets": "0.9.1", "@univerjs/thread-comment": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-thread-comment-ui": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/sheets-thread-comment-ui/-/sheets-thread-comment-ui-0.9.1.tgz", "integrity": "sha512-Xk9tMfiJRu3PoCbHjAuwbg8br0sfjcI1SY+RCO8uRESuSLtVKif/zfEVqVCDvkKLm0wQi1qRu+EnCtwyFiqQUQ==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/icons": "^0.4.4", "@univerjs/sheets": "0.9.1", "@univerjs/sheets-thread-comment": "0.9.1", "@univerjs/sheets-ui": "0.9.1", "@univerjs/thread-comment": "0.9.1", "@univerjs/thread-comment-ui": "0.9.1", "@univerjs/ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/sheets-ui": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/sheets-ui/-/sheets-ui-0.9.1.tgz", "integrity": "sha512-o/K7npBS1mwmSHMIRvEBUK+j0/2vQBIC4swXEGVwvTStdp1PjCOYBxKF22Dz880jOcnci7wKHYGmKNSEdGTixw==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/design": "0.9.1", "@univerjs/docs": "0.9.1", "@univerjs/docs-ui": "0.9.1", "@univerjs/engine-formula": "0.9.1", "@univerjs/engine-numfmt": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/icons": "^0.4.4", "@univerjs/protocol": "0.1.46", "@univerjs/sheets": "0.9.1", "@univerjs/telemetry": "0.9.1", "@univerjs/ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/telemetry": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/telemetry/-/telemetry-0.9.1.tgz", "integrity": "sha512-RI3mN9cRplkhUPgPZTIJIGhCZeMbBqlBy/Ly/Hkj55c+PVb5tFb4yL1uPO523qXiqfraocj6yMJfP5AFSQJOOw==", "dependencies": {"@univerjs/core": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}}, "node_modules/@univerjs/themes": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/themes/-/themes-0.9.1.tgz", "integrity": "sha512-AQYcdPXUeng4ozug6K8wvKc9X12NiwLkNPe0w/2Y/bfmGx+NshbqsC/TxrjJ392fQtoASmx1+1/TvTabUKixJA==", "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}}, "node_modules/@univerjs/thread-comment": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/thread-comment/-/thread-comment-0.9.1.tgz", "integrity": "sha512-mmzMyawiOHIg33dUNHk7GPc/z4OnnSChOBnLVnwTaZZ6nBsc4sWHawtMlol0BjyfNlwCmVLp1ljqLQL7T7IX3g==", "dependencies": {"@univerjs/core": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"rxjs": ">=7.0.0"}}, "node_modules/@univerjs/thread-comment-ui": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/thread-comment-ui/-/thread-comment-ui-0.9.1.tgz", "integrity": "sha512-VYiTidUzNWGsL4sgdLt1Eoq5BKJ9T+edP2ISzY1A/nRQYSHyqsgEORVOu8jDhCcvnnxTnHIJlHUeovHQX2aedA==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/design": "0.9.1", "@univerjs/docs-ui": "0.9.1", "@univerjs/icons": "^0.4.4", "@univerjs/thread-comment": "0.9.1", "@univerjs/ui": "0.9.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/ui": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/@univerjs/ui/-/ui-0.9.1.tgz", "integrity": "sha512-x5sUcXuCk4N/PcD/KHLmrBLpx/fdqzbFESAhP+eEqnL3C7gObjTidzPIpH20Lj3aMCFJCCV0g3e1SBXREni9cw==", "dependencies": {"@univerjs/core": "0.9.1", "@univerjs/design": "0.9.1", "@univerjs/engine-render": "0.9.1", "@univerjs/icons": "^0.4.4", "@wendellhu/redi": "0.18.3", "localforage": "^1.10.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/univer"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "rxjs": ">=7.0.0"}}, "node_modules/@univerjs/vite-plugin": {"version": "0.5.1", "resolved": "https://registry.npmmirror.com/@univerjs/vite-plugin/-/vite-plugin-0.5.1.tgz", "integrity": "sha512-WzDeW//YLLOACoAX3p98QJa3GoZQigMUpNxRCn0ONszm9Sep8ilYIItopjVa13i21DjoQC3RhY/3in5YSzL1mQ==", "dev": true}, "node_modules/@wendellhu/redi": {"version": "0.18.3", "resolved": "https://registry.npmmirror.com/@wendellhu/redi/-/redi-0.18.3.tgz", "integrity": "sha512-0o57fGpzid62p4UsXv/vAWnkKW+vqkUjsjFDkFt68yZrIVCFPmcR2761YIfrzUqmUjkrySURd8Qu1CQ2NPDkBw=="}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "peer": true, "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/aria-hidden": {"version": "1.2.6", "resolved": "https://registry.npmmirror.com/aria-hidden/-/aria-hidden-1.2.6.tgz", "integrity": "sha512-ik3ZgC9dY/lYVVM++OISsaYDeg1tb0VtP5uL3ouh1koGOaUMDPpbFIei4JkFimWUFPn90sbMNMXQAIVOlnYKJA==", "dependencies": {"tslib": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/async-lock": {"version": "1.4.1", "resolved": "https://registry.npmmirror.com/async-lock/-/async-lock-1.4.1.tgz", "integrity": "sha512-Az2ZTpuytrtqENulXwO3GGv1Bztugx6TT37NIo7imr/Qo0gsYiGtSdBa2B6fsXhTpVZDNfu1Qn3pk531e3q+nQ=="}, "node_modules/cjk-regex": {"version": "3.3.0", "resolved": "https://registry.npmmirror.com/cjk-regex/-/cjk-regex-3.3.0.tgz", "integrity": "sha512-o9QeA4DIiljRGO3mXzkQXBttzE6XRGZG99V9F8uqrdqKo5RHTFe8w+pk1aOMB/wxQ7qQ8J7WoTagabTabPgl8A==", "dependencies": {"regexp-util": "^2.0.1", "unicode-regex": "^4.1.0"}, "engines": {"node": ">=16"}}, "node_modules/class-variance-authority": {"version": "0.7.1", "resolved": "https://registry.npmmirror.com/class-variance-authority/-/class-variance-authority-0.7.1.tgz", "integrity": "sha512-Ka+9Trutv7G8M6WT6SeiRWz792K5qEqIGEGzXKhAE6xOWAY6pPH8U+9IY3oCMv6kqTmLsv7Xh/2w2RigkePMsg==", "dependencies": {"clsx": "^2.1.1"}, "funding": {"url": "https://polar.sh/cva"}}, "node_modules/classnames": {"version": "2.5.1", "resolved": "https://registry.npmmirror.com/classnames/-/classnames-2.5.1.tgz", "integrity": "sha512-saHYOzhIQs6wy2sVxTM6bUDsQO4F50V9RQ22qBpEdCW+I+/Wmke2HOl6lS6dTpdxVhb88/I6+Hs+438c3lfUow=="}, "node_modules/cliui": {"version": "8.0.1", "resolved": "https://registry.npmmirror.com/cliui/-/cliui-8.0.1.tgz", "integrity": "sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==", "peer": true, "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/cliui/node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "peer": true, "engines": {"node": ">=8"}}, "node_modules/cliui/node_modules/emoji-regex": {"version": "8.0.0", "resolved": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==", "peer": true}, "node_modules/cliui/node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==", "peer": true, "engines": {"node": ">=8"}}, "node_modules/cliui/node_modules/string-width": {"version": "4.2.3", "resolved": "https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "peer": true, "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/cliui/node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "peer": true, "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/cliui/node_modules/wrap-ansi": {"version": "7.0.0", "resolved": "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==", "peer": true, "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/clsx": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/clsx/-/clsx-2.1.1.tgz", "integrity": "sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==", "engines": {"node": ">=6"}}, "node_modules/collapse-white-space": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/collapse-white-space/-/collapse-white-space-2.1.0.tgz", "integrity": "sha512-loKTxY1zCOuG4j9f6EPnuyyYkf58RnhhWTvRoZEokgB+WbdXehfjFviyOVYkqzEWz1Q5kRiZdBYS5SwxbQYwzw==", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "peer": true, "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://registry.npmmirror.com/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "peer": true}, "node_modules/crypto-js": {"version": "4.2.0", "resolved": "https://registry.npmmirror.com/crypto-js/-/crypto-js-4.2.0.tgz", "integrity": "sha512-KALDyEYgpY+Rlob/iriUtjV6d5Eq+Y191A5g4UqLAi8CyGP9N1+FdVbkc1SxKc2r4YAYqG8JzO2KGL+AizD70Q=="}, "node_modules/css-box-model": {"version": "1.2.1", "resolved": "https://registry.npmmirror.com/css-box-model/-/css-box-model-1.2.1.tgz", "integrity": "sha512-a7Vr4Q/kd/aw96bnJG332W9V9LkJO69JRcaCYDUqjp6/z0w6VcZjgAcTbgFxEPfBgdnAwlh3iwu+hLopa+flJw==", "dependencies": {"tiny-invariant": "^1.0.6"}}, "node_modules/csstype": {"version": "3.1.3", "resolved": "https://registry.npmmirror.com/csstype/-/csstype-3.1.3.tgz", "integrity": "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw=="}, "node_modules/dayjs": {"version": "1.11.13", "resolved": "https://registry.npmmirror.com/dayjs/-/dayjs-1.11.13.tgz", "integrity": "sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg=="}, "node_modules/decimal.js": {"version": "10.6.0", "resolved": "https://registry.npmmirror.com/decimal.js/-/decimal.js-10.6.0.tgz", "integrity": "sha512-YpgQiITW3JXGntzdUmyUR1V812Hn8T1YVXhCu+wO3OpS4eU9l4YdD3qjyiKdV6mvV29zapkMeD390UVEf2lkUg=="}, "node_modules/detect-node-es": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/detect-node-es/-/detect-node-es-1.1.0.tgz", "integrity": "sha512-ypdmJU/TbBby2Dxibuv7ZLW3Bs1QEmM7nHjEANfohJLvE0XVujisn1qPJcZxg+qDucsr+bP6fLD1rPS3AhJ7EQ=="}, "node_modules/dom-helpers": {"version": "5.2.1", "resolved": "https://registry.npmmirror.com/dom-helpers/-/dom-helpers-5.2.1.tgz", "integrity": "sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==", "dependencies": {"@babel/runtime": "^7.8.7", "csstype": "^3.0.2"}}, "node_modules/echarts": {"version": "5.6.0", "resolved": "https://registry.npmmirror.com/echarts/-/echarts-5.6.0.tgz", "integrity": "sha512-oTbVTsXfKuEhxftHqL5xprgLoc0k7uScAwtryCgWF6hPYFLRwOUHiFmHGCBKP5NPFNkDVopOieyUqYGH8Fa3kA==", "dependencies": {"tslib": "2.3.0", "zrender": "5.6.1"}}, "node_modules/echarts-wordcloud": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/echarts-wordcloud/-/echarts-wordcloud-2.1.0.tgz", "integrity": "sha512-Kt1JmbcROgb+3IMI48KZECK2AP5lG6bSsOEs+AsuwaWJxQom31RTNd6NFYI01E/YaI1PFZeueaupjlmzSQasjQ==", "peerDependencies": {"echarts": "^5.0.1"}}, "node_modules/echarts/node_modules/tslib": {"version": "2.3.0", "resolved": "https://registry.npmmirror.com/tslib/-/tslib-2.3.0.tgz", "integrity": "sha512-N82ooyxVNm6h1riLCoyS9e3fuJ3AMG2zIZs2Gd1ATcSFjSA23Q0fzjjZeh0jbJvWVDZ0cJT8yaNNaaXHzueNjg=="}, "node_modules/esbuild": {"version": "0.25.5", "resolved": "https://registry.npmmirror.com/esbuild/-/esbuild-0.25.5.tgz", "integrity": "sha512-P8OtKZRv/5J5hhz0cUAdu/cLuPIKXpQl1R9pZtvmHWQvrAUVd0UNIPT4IB4W3rNOqVO0rlqHmCIbSwxh/c9yUQ==", "dev": true, "hasInstallScript": true, "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=18"}, "optionalDependencies": {"@esbuild/aix-ppc64": "0.25.5", "@esbuild/android-arm": "0.25.5", "@esbuild/android-arm64": "0.25.5", "@esbuild/android-x64": "0.25.5", "@esbuild/darwin-arm64": "0.25.5", "@esbuild/darwin-x64": "0.25.5", "@esbuild/freebsd-arm64": "0.25.5", "@esbuild/freebsd-x64": "0.25.5", "@esbuild/linux-arm": "0.25.5", "@esbuild/linux-arm64": "0.25.5", "@esbuild/linux-ia32": "0.25.5", "@esbuild/linux-loong64": "0.25.5", "@esbuild/linux-mips64el": "0.25.5", "@esbuild/linux-ppc64": "0.25.5", "@esbuild/linux-riscv64": "0.25.5", "@esbuild/linux-s390x": "0.25.5", "@esbuild/linux-x64": "0.25.5", "@esbuild/netbsd-arm64": "0.25.5", "@esbuild/netbsd-x64": "0.25.5", "@esbuild/openbsd-arm64": "0.25.5", "@esbuild/openbsd-x64": "0.25.5", "@esbuild/sunos-x64": "0.25.5", "@esbuild/win32-arm64": "0.25.5", "@esbuild/win32-ia32": "0.25.5", "@esbuild/win32-x64": "0.25.5"}}, "node_modules/escalade": {"version": "3.2.0", "resolved": "https://registry.npmmirror.com/escalade/-/escalade-3.2.0.tgz", "integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==", "peer": true, "engines": {"node": ">=6"}}, "node_modules/fast-diff": {"version": "1.3.0", "resolved": "https://registry.npmmirror.com/fast-diff/-/fast-diff-1.3.0.tgz", "integrity": "sha512-VxPP4NqbUjj6MaAOafWeUn2cXWLcCtljklUtZf0Ind4XQ+QPtmA0b18zZy0jIQx+ExRVCR/ZQpBmik5lXshNsw=="}, "node_modules/fast-equals": {"version": "4.0.3", "resolved": "https://registry.npmmirror.com/fast-equals/-/fast-equals-4.0.3.tgz", "integrity": "sha512-G3BSX9cfKttjr+2o1O22tYMLq0DPluZnYtq1rXumE1SpL/F/SLIfHx08WYQoWSIpeMYf8sRbJ8++71+v6Pnxfg=="}, "node_modules/fdir": {"version": "6.4.6", "resolved": "https://registry.npmmirror.com/fdir/-/fdir-6.4.6.tgz", "integrity": "sha512-hiFoqpyZcfNm1yc4u8oWCf9A2c4D3QjCrks3zmoVKVxpQRzmPNar1hUJcBG2RQHvEVGDN+Jm81ZheVLAQMK6+w==", "dev": true, "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}}, "node_modules/franc-min": {"version": "6.2.0", "resolved": "https://registry.npmmirror.com/franc-min/-/franc-min-6.2.0.tgz", "integrity": "sha512-1uDIEUSlUZgvJa2AKYR/dmJC66v/PvGQ9mWfI9nOr/kPpMFyvswK0gPXOwpYJYiYD008PpHLkGfG58SPjQJFxw==", "dependencies": {"trigram-utils": "^2.0.0"}, "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/get-caller-file": {"version": "2.0.5", "resolved": "https://registry.npmmirror.com/get-caller-file/-/get-caller-file-2.0.5.tgz", "integrity": "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==", "peer": true, "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-nonce": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/get-nonce/-/get-nonce-1.0.1.tgz", "integrity": "sha512-FJhYRoDaiatfEkUK8HKlicmu/3SGFD51q3itKDGoSTysQJBnfOcxU5GxnhE1E6soB76MbT0MBtnKJuXyAx+96Q==", "engines": {"node": ">=6"}}, "node_modules/hoist-non-react-statics": {"version": "3.3.2", "resolved": "https://registry.npmmirror.com/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz", "integrity": "sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==", "dependencies": {"react-is": "^16.7.0"}}, "node_modules/hoist-non-react-statics/node_modules/react-is": {"version": "16.13.1", "resolved": "https://registry.npmmirror.com/react-is/-/react-is-16.13.1.tgz", "integrity": "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ=="}, "node_modules/immediate": {"version": "3.0.6", "resolved": "https://registry.npmmirror.com/immediate/-/immediate-3.0.6.tgz", "integrity": "sha512-XXOFtyqDjNDAQxVfYxuF7g9Il/IbWmmlQg2MYKOH8ExIT1qg6xc4zyS3HaEEATgs1btfzxq15ciUiY7gjSXRGQ=="}, "node_modules/js-tokens": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="}, "node_modules/kdbush": {"version": "4.0.2", "resolved": "https://registry.npmmirror.com/kdbush/-/kdbush-4.0.2.tgz", "integrity": "sha512-WbCVYJ27Sz8zi9Q7Q0xHC+05iwkm3Znipc2XTlrnJbsHMYktW4hPhXUE8Ys1engBrvffoSCqbil1JQAa7clRpA=="}, "node_modules/lie": {"version": "3.1.1", "resolved": "https://registry.npmmirror.com/lie/-/lie-3.1.1.tgz", "integrity": "sha512-RiNhHysUjhrDQntfYSfY4MU24coXXdEOgw9WGcKHNeEwffDYbF//u87M1EWaMGzuFoSbqW0C9C6lEEhDOAswfw==", "dependencies": {"immediate": "~3.0.5"}}, "node_modules/localforage": {"version": "1.10.0", "resolved": "https://registry.npmmirror.com/localforage/-/localforage-1.10.0.tgz", "integrity": "sha512-14/H1aX7hzBBmmh7sGPd+AOMkkIrHM3Z1PAyGgZigA1H1p5O5ANnMyWzvpAETtG68/dC4pC0ncy3+PPGzXZHPg==", "dependencies": {"lie": "3.1.1"}}, "node_modules/lodash-es": {"version": "4.17.21", "resolved": "https://registry.npmmirror.com/lodash-es/-/lodash-es-4.17.21.tgz", "integrity": "sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw=="}, "node_modules/lodash.camelcase": {"version": "4.3.0", "resolved": "https://registry.npmmirror.com/lodash.camelcase/-/lodash.camelcase-4.3.0.tgz", "integrity": "sha512-TwuEnCnxbc3rAvhf/LbG7tJUDzhqXyFnv3dtzLOPgCG/hODL7WFnsbwktkD7yUV0RrreP/l1PALq/YSg6VvjlA==", "peer": true}, "node_modules/long": {"version": "5.3.2", "resolved": "https://registry.npmmirror.com/long/-/long-5.3.2.tgz", "integrity": "sha512-mNAgZ1GmyNhD7AuqnTG3/VQ26o760+ZYBPKjPvugO8+nLbYfX6TVpJPseBvopbdY+qpZ/lKUnmEc1LeZYS3QAA==", "peer": true}, "node_modules/loose-envify": {"version": "1.4.0", "resolved": "https://registry.npmmirror.com/loose-envify/-/loose-envify-1.4.0.tgz", "integrity": "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==", "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "bin": {"loose-envify": "cli.js"}}, "node_modules/memoize-one": {"version": "5.2.1", "resolved": "https://registry.npmmirror.com/memoize-one/-/memoize-one-5.2.1.tgz", "integrity": "sha512-zYiwtZUcYyXKo/np96AGZAckk+FWWsUdJ3cHGGmld7+AhvcWmQyGCYUh1hc4Q/pkOhb65dQR/pqCyK0cOaHz4Q=="}, "node_modules/n-gram": {"version": "2.0.2", "resolved": "https://registry.npmmirror.com/n-gram/-/n-gram-2.0.2.tgz", "integrity": "sha512-S24aGsn+HLBxUGVAUFOwGpKs7LBcG4RudKU//eWzt/mQ97/NMKQxDWHyHx63UNWk/OOdihgmzoETn1tf5nQDzQ==", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/nanoid": {"version": "5.1.5", "resolved": "https://registry.npmmirror.com/nanoid/-/nanoid-5.1.5.tgz", "integrity": "sha512-Ir/+ZpE9fDsNH0hQ3C68uyThDXzYcim2EqcZ8zn8Chtt1iylPT9xXJB0kPCnqzgcEGikO9RxSrh63MsmVCU7Fw==", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "bin": {"nanoid": "bin/nanoid.js"}, "engines": {"node": "^18 || >=20"}}, "node_modules/numfmt": {"version": "3.2.3", "resolved": "https://registry.npmmirror.com/numfmt/-/numfmt-3.2.3.tgz", "integrity": "sha512-q5vjJSiuomxYNNVhB/TWqjtctZz+fnscUchvwonutXZ/neY2XLw6z4q3DS4ijLDrP5Y/tgrVeP1/7PjgHRoZuw=="}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "https://registry.npmmirror.com/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==", "engines": {"node": ">=0.10.0"}}, "node_modules/opentype.js": {"version": "1.3.4", "resolved": "https://registry.npmmirror.com/opentype.js/-/opentype.js-1.3.4.tgz", "integrity": "sha512-d2JE9RP/6uagpQAVtJoF0pJJA/fgai89Cc50Yp0EJHk+eLp6QQ7gBoblsnubRULNY132I0J1QKMJ+JTbMqz4sw==", "dependencies": {"string.prototype.codepointat": "^0.2.1", "tiny-inflate": "^1.0.3"}, "bin": {"ot": "bin/ot"}, "engines": {"node": ">= 8.0.0"}}, "node_modules/ot-json1": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/ot-json1/-/ot-json1-1.0.2.tgz", "integrity": "sha512-IhxkqVWQqlkWULoi/Q2AdzKk0N5vQRbUMUwubFXFCPcY4TsOZjmp2YKrk0/z1TeiECPadWEK060sdFdQ3Grokg==", "dependencies": {"ot-text-unicode": "4"}}, "node_modules/ot-text-unicode": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/ot-text-unicode/-/ot-text-unicode-4.0.0.tgz", "integrity": "sha512-W7ZLU8QXesY2wagYFv47zErXud3E93FGImmSGJsQnBzE+idcPPyo2u2KMilIrTwBh4pbCizy71qRjmmV6aDhcQ==", "dependencies": {"unicount": "1.1"}}, "node_modules/pako": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/pako/-/pako-2.1.0.tgz", "integrity": "sha512-w+eufiZ1WuJYgPXbV/PO3NCMEc3xqylkKHzp8bxp1uW4qaSNQUkwmLLEc3kKsfz8lpV1F8Ht3U1Cm+9Srog2ug=="}, "node_modules/picocolors": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/picocolors/-/picocolors-1.1.1.tgz", "integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==", "dev": true}, "node_modules/picomatch": {"version": "4.0.2", "resolved": "https://registry.npmmirror.com/picomatch/-/picomatch-4.0.2.tgz", "integrity": "sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==", "dev": true, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/postcss": {"version": "8.5.6", "resolved": "https://registry.npmmirror.com/postcss/-/postcss-8.5.6.tgz", "integrity": "sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "dependencies": {"nanoid": "^3.3.11", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/postcss/node_modules/nanoid": {"version": "3.3.11", "resolved": "https://registry.npmmirror.com/nanoid/-/nanoid-3.3.11.tgz", "integrity": "sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/prop-types": {"version": "15.8.1", "resolved": "https://registry.npmmirror.com/prop-types/-/prop-types-15.8.1.tgz", "integrity": "sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==", "dependencies": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1"}}, "node_modules/prop-types/node_modules/react-is": {"version": "16.13.1", "resolved": "https://registry.npmmirror.com/react-is/-/react-is-16.13.1.tgz", "integrity": "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ=="}, "node_modules/protobufjs": {"version": "7.5.3", "resolved": "https://registry.npmmirror.com/protobufjs/-/protobufjs-7.5.3.tgz", "integrity": "sha512-sildjKwVqOI2kmFDiXQ6aEB0fjYTafpEvIBs8tOR8qI4spuL9OPROLVu2qZqi/xgCfsHIwVqlaF8JBjWFHnKbw==", "hasInstallScript": true, "peer": true, "dependencies": {"@protobufjs/aspromise": "^1.1.2", "@protobufjs/base64": "^1.1.2", "@protobufjs/codegen": "^2.0.4", "@protobufjs/eventemitter": "^1.1.0", "@protobufjs/fetch": "^1.1.0", "@protobufjs/float": "^1.0.2", "@protobufjs/inquire": "^1.1.0", "@protobufjs/path": "^1.1.2", "@protobufjs/pool": "^1.1.0", "@protobufjs/utf8": "^1.1.0", "@types/node": ">=13.7.0", "long": "^5.0.0"}, "engines": {"node": ">=12.0.0"}}, "node_modules/quickselect": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/quickselect/-/quickselect-3.0.0.tgz", "integrity": "sha512-XdjUArbK4Bm5fLLvlm5KpTFOiOThgfWWI4axAZDWg4E/0mKdZyI9tNEfds27qCi1ze/vwTR16kvmmGhRra3c2g=="}, "node_modules/raf-schd": {"version": "4.0.3", "resolved": "https://registry.npmmirror.com/raf-schd/-/raf-schd-4.0.3.tgz", "integrity": "sha512-tQkJl2GRWh83ui2DiPTJz9wEiMN20syf+5oKfB03yYP7ioZcJwsIK8FjrtLwH1m7C7e+Tt2yYBlrOpdT+dyeIQ=="}, "node_modules/rbush": {"version": "4.0.1", "resolved": "https://registry.npmmirror.com/rbush/-/rbush-4.0.1.tgz", "integrity": "sha512-IP0UpfeWQujYC8Jg162rMNc01Rf0gWMMAb2Uxus/Q0qOFw4lCcq6ZnQEZwUoJqWyUGJ9th7JjwI4yIWo+uvoAQ==", "dependencies": {"quickselect": "^3.0.0"}}, "node_modules/rc-dropdown": {"version": "4.2.1", "resolved": "https://registry.npmmirror.com/rc-dropdown/-/rc-dropdown-4.2.1.tgz", "integrity": "sha512-YDAlXsPv3I1n42dv1JpdM7wJ+gSUBfeyPK59ZpBD9jQhK9jVuxpjj3NmWQHOBceA1zEPVX84T2wbdb2SD0UjmA==", "dependencies": {"@babel/runtime": "^7.18.3", "@rc-component/trigger": "^2.0.0", "classnames": "^2.2.6", "rc-util": "^5.44.1"}, "peerDependencies": {"react": ">=16.11.0", "react-dom": ">=16.11.0"}}, "node_modules/rc-menu": {"version": "9.16.1", "resolved": "https://registry.npmmirror.com/rc-menu/-/rc-menu-9.16.1.tgz", "integrity": "sha512-ghHx6/6Dvp+fw8CJhDUHFHDJ84hJE3BXNCzSgLdmNiFErWSOaZNsihDAsKq9ByTALo/xkNIwtDFGIl6r+RPXBg==", "dependencies": {"@babel/runtime": "^7.10.1", "@rc-component/trigger": "^2.0.0", "classnames": "2.x", "rc-motion": "^2.4.3", "rc-overflow": "^1.3.1", "rc-util": "^5.27.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-motion": {"version": "2.9.5", "resolved": "https://registry.npmmirror.com/rc-motion/-/rc-motion-2.9.5.tgz", "integrity": "sha512-w+XTUrfh7ArbYEd2582uDrEhmBHwK1ZENJiSJVb7uRxdE7qJSYjbO2eksRXmndqyKqKoYPc9ClpPh5242mV1vA==", "dependencies": {"@babel/runtime": "^7.11.1", "classnames": "^2.2.1", "rc-util": "^5.44.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-overflow": {"version": "1.4.1", "resolved": "https://registry.npmmirror.com/rc-overflow/-/rc-overflow-1.4.1.tgz", "integrity": "sha512-3MoPQQPV1uKyOMVNd6SZfONi+f3st0r8PksexIdBTeIYbMX0Jr+k7pHEDvsXtR4BpCv90/Pv2MovVNhktKrwvw==", "dependencies": {"@babel/runtime": "^7.11.1", "classnames": "^2.2.1", "rc-resize-observer": "^1.0.0", "rc-util": "^5.37.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-picker": {"version": "4.11.3", "resolved": "https://registry.npmmirror.com/rc-picker/-/rc-picker-4.11.3.tgz", "integrity": "sha512-MJ5teb7FlNE0NFHTncxXQ62Y5lytq6sh5nUw0iH8OkHL/TjARSEvSHpr940pWgjGANpjCwyMdvsEV55l5tYNSg==", "dependencies": {"@babel/runtime": "^7.24.7", "@rc-component/trigger": "^2.0.0", "classnames": "^2.2.1", "rc-overflow": "^1.3.2", "rc-resize-observer": "^1.4.0", "rc-util": "^5.43.0"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"date-fns": ">= 2.x", "dayjs": ">= 1.x", "luxon": ">= 3.x", "moment": ">= 2.x", "react": ">=16.9.0", "react-dom": ">=16.9.0"}, "peerDependenciesMeta": {"date-fns": {"optional": true}, "dayjs": {"optional": true}, "luxon": {"optional": true}, "moment": {"optional": true}}}, "node_modules/rc-resize-observer": {"version": "1.4.3", "resolved": "https://registry.npmmirror.com/rc-resize-observer/-/rc-resize-observer-1.4.3.tgz", "integrity": "sha512-YZLjUbyIWox8E9i9C3Tm7ia+W7euPItNWSPX5sCcQTYbnwDb5uNpnLHQCG1f22oZWUhLw4Mv2tFmeWe68CDQRQ==", "dependencies": {"@babel/runtime": "^7.20.7", "classnames": "^2.2.1", "rc-util": "^5.44.1", "resize-observer-polyfill": "^1.5.1"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-util": {"version": "5.44.4", "resolved": "https://registry.npmmirror.com/rc-util/-/rc-util-5.44.4.tgz", "integrity": "sha512-resueRJzmHG9Q6rI/DfK6Kdv9/Lfls05vzMs1Sk3M2P+3cJa+MakaZyWY8IPfehVuhPJFKrIY1IK4GqbiaiY5w==", "dependencies": {"@babel/runtime": "^7.18.3", "react-is": "^18.2.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/rc-virtual-list": {"version": "3.19.1", "resolved": "https://registry.npmmirror.com/rc-virtual-list/-/rc-virtual-list-3.19.1.tgz", "integrity": "sha512-DCapO2oyPqmooGhxBuXHM4lFuX+sshQwWqqkuyFA+4rShLe//+GEPVwiDgO+jKtKHtbeYwZoNvetwfHdOf+iUQ==", "dependencies": {"@babel/runtime": "^7.20.0", "classnames": "^2.2.6", "rc-resize-observer": "^1.0.0", "rc-util": "^5.36.0"}, "engines": {"node": ">=8.x"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}, "node_modules/react": {"version": "18.3.1", "resolved": "https://registry.npmmirror.com/react/-/react-18.3.1.tgz", "integrity": "sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/react-beautiful-dnd": {"version": "13.1.1", "resolved": "https://registry.npmmirror.com/react-beautiful-dnd/-/react-beautiful-dnd-13.1.1.tgz", "integrity": "sha512-0Lvs4tq2VcrEjEgDXHjT98r+63drkKEgqyxdA7qD3mvKwga6a5SscbdLPO2IExotU1jW8L0Ksdl0Cj2AF67nPQ==", "deprecated": "react-beautiful-dnd is now deprecated. Context and options: https://github.com/atlassian/react-beautiful-dnd/issues/2672", "dependencies": {"@babel/runtime": "^7.9.2", "css-box-model": "^1.2.0", "memoize-one": "^5.1.1", "raf-schd": "^4.0.2", "react-redux": "^7.2.0", "redux": "^4.0.4", "use-memo-one": "^1.1.1"}, "peerDependencies": {"react": "^16.8.5 || ^17.0.0 || ^18.0.0", "react-dom": "^16.8.5 || ^17.0.0 || ^18.0.0"}}, "node_modules/react-dom": {"version": "18.3.1", "resolved": "https://registry.npmmirror.com/react-dom/-/react-dom-18.3.1.tgz", "integrity": "sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==", "peer": true, "dependencies": {"loose-envify": "^1.1.0", "scheduler": "^0.23.2"}, "peerDependencies": {"react": "^18.3.1"}}, "node_modules/react-draggable": {"version": "4.5.0", "resolved": "https://registry.npmmirror.com/react-draggable/-/react-draggable-4.5.0.tgz", "integrity": "sha512-VC+HBLEZ0XJxnOxVAZsdRi8rD04Iz3SiiKOoYzamjylUcju/hP9np/aZdLHf/7WOD268WMoNJMvYfB5yAK45cw==", "dependencies": {"clsx": "^2.1.1", "prop-types": "^15.8.1"}, "peerDependencies": {"react": ">= 16.3.0", "react-dom": ">= 16.3.0"}}, "node_modules/react-grid-layout": {"version": "1.5.2", "resolved": "https://registry.npmmirror.com/react-grid-layout/-/react-grid-layout-1.5.2.tgz", "integrity": "sha512-vT7xmQqszTT+sQw/LfisrEO4le1EPNnSEMVHy6sBZyzS3yGkMywdOd+5iEFFwQwt0NSaGkxuRmYwa1JsP6OJdw==", "dependencies": {"clsx": "^2.1.1", "fast-equals": "^4.0.3", "prop-types": "^15.8.1", "react-draggable": "^4.4.6", "react-resizable": "^3.0.5", "resize-observer-polyfill": "^1.5.1"}, "peerDependencies": {"react": ">= 16.3.0", "react-dom": ">= 16.3.0"}}, "node_modules/react-is": {"version": "18.3.1", "resolved": "https://registry.npmmirror.com/react-is/-/react-is-18.3.1.tgz", "integrity": "sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg=="}, "node_modules/react-redux": {"version": "7.2.9", "resolved": "https://registry.npmmirror.com/react-redux/-/react-redux-7.2.9.tgz", "integrity": "sha512-Gx4L3uM182jEEayZfRbI/G11ZpYdNAnBs70lFVMNdHJI76XYtR+7m0MN+eAs7UHBPhWXcnFPaS+9owSCJQHNpQ==", "dependencies": {"@babel/runtime": "^7.15.4", "@types/react-redux": "^7.1.20", "hoist-non-react-statics": "^3.3.2", "loose-envify": "^1.4.0", "prop-types": "^15.7.2", "react-is": "^17.0.2"}, "peerDependencies": {"react": "^16.8.3 || ^17 || ^18"}, "peerDependenciesMeta": {"react-dom": {"optional": true}, "react-native": {"optional": true}}}, "node_modules/react-redux/node_modules/react-is": {"version": "17.0.2", "resolved": "https://registry.npmmirror.com/react-is/-/react-is-17.0.2.tgz", "integrity": "sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w=="}, "node_modules/react-remove-scroll": {"version": "2.7.1", "resolved": "https://registry.npmmirror.com/react-remove-scroll/-/react-remove-scroll-2.7.1.tgz", "integrity": "sha512-HpMh8+oahmIdOuS5aFKKY6Pyog+FNaZV/XyJOq7b4YFwsFHe5yYfdbIalI4k3vU2nSDql7YskmUseHsRrJqIPA==", "dependencies": {"react-remove-scroll-bar": "^2.3.7", "react-style-singleton": "^2.2.3", "tslib": "^2.1.0", "use-callback-ref": "^1.3.3", "use-sidecar": "^1.1.3"}, "engines": {"node": ">=10"}, "peerDependencies": {"@types/react": "*", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/react-remove-scroll-bar": {"version": "2.3.8", "resolved": "https://registry.npmmirror.com/react-remove-scroll-bar/-/react-remove-scroll-bar-2.3.8.tgz", "integrity": "sha512-9r+yi9+mgU33AKcj6IbT9oRCO78WriSj6t/cF8DWBZJ9aOGPOTEDvdUDz1FwKim7QXWwmHqtdHnRJfhAxEG46Q==", "dependencies": {"react-style-singleton": "^2.2.2", "tslib": "^2.0.0"}, "engines": {"node": ">=10"}, "peerDependencies": {"@types/react": "*", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/react-resizable": {"version": "3.0.5", "resolved": "https://registry.npmmirror.com/react-resizable/-/react-resizable-3.0.5.tgz", "integrity": "sha512-vKpeHhI5OZvYn82kXOs1bC8aOXktGU5AmKAgaZS4F5JPburCtbmDPqE7Pzp+1kN4+Wb81LlF33VpGwWwtXem+w==", "dependencies": {"prop-types": "15.x", "react-draggable": "^4.0.3"}, "peerDependencies": {"react": ">= 16.3"}}, "node_modules/react-style-singleton": {"version": "2.2.3", "resolved": "https://registry.npmmirror.com/react-style-singleton/-/react-style-singleton-2.2.3.tgz", "integrity": "sha512-b6jSvxvVnyptAiLjbkWLE/lOnR4lfTtDAl+eUC7RZy+QQWc6wRzIV2CE6xBuMmDxc2qIihtDCZD5NPOFl7fRBQ==", "dependencies": {"get-nonce": "^1.0.0", "tslib": "^2.0.0"}, "engines": {"node": ">=10"}, "peerDependencies": {"@types/react": "*", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/react-transition-group": {"version": "4.4.5", "resolved": "https://registry.npmmirror.com/react-transition-group/-/react-transition-group-4.4.5.tgz", "integrity": "sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==", "dependencies": {"@babel/runtime": "^7.5.5", "dom-helpers": "^5.0.1", "loose-envify": "^1.4.0", "prop-types": "^15.6.2"}, "peerDependencies": {"react": ">=16.6.0", "react-dom": ">=16.6.0"}}, "node_modules/redux": {"version": "4.2.1", "resolved": "https://registry.npmmirror.com/redux/-/redux-4.2.1.tgz", "integrity": "sha512-LAUYz4lc+Do8/g7aeRa8JkyDErK6ekstQaqWQrNRW//MY1TvCEpMtpTWvlQ+FPbWCx+Xixu/6SHt5N0HR+SB4w==", "dependencies": {"@babel/runtime": "^7.9.2"}}, "node_modules/regexp-util": {"version": "2.0.3", "resolved": "https://registry.npmmirror.com/regexp-util/-/regexp-util-2.0.3.tgz", "integrity": "sha512-GP6h9OgJmhAZpb3dbNbXTfRWVnGcoMhWRZv/HxgM4/qCVqs1P9ukQdYxaUhjWBSAs9oJ/uPXUUvGT1VMe0Bs0Q==", "engines": {"node": ">=16"}}, "node_modules/require-directory": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/resize-observer-polyfill": {"version": "1.5.1", "resolved": "https://registry.npmmirror.com/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz", "integrity": "sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg=="}, "node_modules/rollup": {"version": "4.44.2", "resolved": "https://registry.npmmirror.com/rollup/-/rollup-4.44.2.tgz", "integrity": "sha512-PVoapzTwSEcelaWGth3uR66u7ZRo6qhPHc0f2uRO9fX6XDVNrIiGYS0Pj9+R8yIIYSD/mCx2b16Ws9itljKSPg==", "dev": true, "dependencies": {"@types/estree": "1.0.8"}, "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "optionalDependencies": {"@rollup/rollup-android-arm-eabi": "4.44.2", "@rollup/rollup-android-arm64": "4.44.2", "@rollup/rollup-darwin-arm64": "4.44.2", "@rollup/rollup-darwin-x64": "4.44.2", "@rollup/rollup-freebsd-arm64": "4.44.2", "@rollup/rollup-freebsd-x64": "4.44.2", "@rollup/rollup-linux-arm-gnueabihf": "4.44.2", "@rollup/rollup-linux-arm-musleabihf": "4.44.2", "@rollup/rollup-linux-arm64-gnu": "4.44.2", "@rollup/rollup-linux-arm64-musl": "4.44.2", "@rollup/rollup-linux-loongarch64-gnu": "4.44.2", "@rollup/rollup-linux-powerpc64le-gnu": "4.44.2", "@rollup/rollup-linux-riscv64-gnu": "4.44.2", "@rollup/rollup-linux-riscv64-musl": "4.44.2", "@rollup/rollup-linux-s390x-gnu": "4.44.2", "@rollup/rollup-linux-x64-gnu": "4.44.2", "@rollup/rollup-linux-x64-musl": "4.44.2", "@rollup/rollup-win32-arm64-msvc": "4.44.2", "@rollup/rollup-win32-ia32-msvc": "4.44.2", "@rollup/rollup-win32-x64-msvc": "4.44.2", "fsevents": "~2.3.2"}}, "node_modules/rxjs": {"version": "7.8.2", "resolved": "https://registry.npmmirror.com/rxjs/-/rxjs-7.8.2.tgz", "integrity": "sha512-dhKf903U/PQZY6boNNtAGdWbG85WAbjT/1xYoZIC7FAY0yWapOBQVsVrDl58W86//e1VpMNBtRV4MaXfdMySFA==", "dependencies": {"tslib": "^2.1.0"}}, "node_modules/scheduler": {"version": "0.23.2", "resolved": "https://registry.npmmirror.com/scheduler/-/scheduler-0.23.2.tgz", "integrity": "sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/sonner": {"version": "2.0.6", "resolved": "https://registry.npmmirror.com/sonner/-/sonner-2.0.6.tgz", "integrity": "sha512-yHFhk8T/DK3YxjFQXIrcHT1rGEeTLliVzWbO0xN8GberVun2RiBnxAjXAYpZrqwEVHBG9asI/Li8TAAhN9m59Q==", "peerDependencies": {"react": "^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^18.0.0 || ^19.0.0 || ^19.0.0-rc"}}, "node_modules/source-map-js": {"version": "1.2.1", "resolved": "https://registry.npmmirror.com/source-map-js/-/source-map-js-1.2.1.tgz", "integrity": "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/string.prototype.codepointat": {"version": "0.2.1", "resolved": "https://registry.npmmirror.com/string.prototype.codepointat/-/string.prototype.codepointat-0.2.1.tgz", "integrity": "sha512-2cBVCj6I4IOvEnjgO/hWqXjqBGsY+zwPmHl12Srk9IXSZ56Jwwmy+66XO5Iut/oQVR7t5ihYdLB0GMa4alEUcg=="}, "node_modules/tailwind-merge": {"version": "3.3.1", "resolved": "https://registry.npmmirror.com/tailwind-merge/-/tailwind-merge-3.3.1.tgz", "integrity": "sha512-gBXpgUm/3rp1lMZZrM/w7D8GKqshif0zAymAhbCyIt8KMe+0v9DQ7cdYLR4FHH/cKpdTXb+A/tKKU3eolfsI+g==", "funding": {"type": "github", "url": "https://github.com/sponsors/dcastil"}}, "node_modules/tiny-inflate": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/tiny-inflate/-/tiny-inflate-1.0.3.tgz", "integrity": "sha512-pkY1fj1cKHb2seWDy0B16HeWyczlJA9/WW3u3c4z/NiWDsO3DOU5D7nhTLE9CF0yXv/QZFY7sEJmj24dK+Rrqw=="}, "node_modules/tiny-invariant": {"version": "1.3.3", "resolved": "https://registry.npmmirror.com/tiny-invariant/-/tiny-invariant-1.3.3.tgz", "integrity": "sha512-+FbBPE1o9QAYvviau/qC5SE3caw21q3xkvWKBtja5vgqOWIHHJ3ioaq1VPfn/Szqctz2bU/oYeKd9/z5BL+PVg=="}, "node_modules/tinyglobby": {"version": "0.2.14", "resolved": "https://registry.npmmirror.com/tinyglobby/-/tinyglobby-0.2.14.tgz", "integrity": "sha512-tX5e7OM1HnYr2+a2C/4V0htOcSQcoSTH9KgJnVvNm5zm/cyEWKJ7j7YutsH9CxMdtOkkLFy2AHrMci9IM8IPZQ==", "dev": true, "dependencies": {"fdir": "^6.4.4", "picomatch": "^4.0.2"}, "engines": {"node": ">=12.0.0"}, "funding": {"url": "https://github.com/sponsors/SuperchupuDev"}}, "node_modules/trigram-utils": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/trigram-utils/-/trigram-utils-2.0.1.tgz", "integrity": "sha512-nfWIXHEaB+HdyslAfMxSqWKDdmqY9I32jS7GnqpdWQnLH89r6A5sdk3fDVYqGAZ0CrT8ovAFSAo6HRiWcWNIGQ==", "dependencies": {"collapse-white-space": "^2.0.0", "n-gram": "^2.0.0"}, "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmmirror.com/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w=="}, "node_modules/typescript": {"version": "5.8.3", "resolved": "https://registry.npmmirror.com/typescript/-/typescript-5.8.3.tgz", "integrity": "sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==", "dev": true, "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/undici-types": {"version": "7.8.0", "resolved": "https://registry.npmmirror.com/undici-types/-/undici-types-7.8.0.tgz", "integrity": "sha512-9UJ2xGDvQ43tYyVMpuHlsgApydB8ZKfVYTsLDhXkFL/6gfkp+U8xTGdh8pMJv1SpZna0zxG1DwsKZsreLbXBxw==", "peer": true}, "node_modules/unicode-regex": {"version": "4.1.2", "resolved": "https://registry.npmmirror.com/unicode-regex/-/unicode-regex-4.1.2.tgz", "integrity": "sha512-30Y3tQ8OUxceQjsEJHzNh20lLYZX6ZwQyUOHBUdN1UPKQWH3AvH20aUADWa1gEz2lQPTSQ/l2ZqdM4FjFNMJsQ==", "dependencies": {"regexp-util": "^2.0.1"}, "engines": {"node": ">=16"}}, "node_modules/unicount": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/unicount/-/unicount-1.1.0.tgz", "integrity": "sha512-RlwWt1ywVW4WErPGAVHw/rIuJ2+MxvTME0siJ6lk9zBhpDfExDbspe6SRlWT3qU6AucNjotPl9qAJRVjP7guCQ=="}, "node_modules/use-callback-ref": {"version": "1.3.3", "resolved": "https://registry.npmmirror.com/use-callback-ref/-/use-callback-ref-1.3.3.tgz", "integrity": "sha512-jQL3lRnocaFtu3V00JToYz/4QkNWswxijDaCVNZRiRTO3HQDLsdu1ZtmIUvV4yPp+rvWm5j0y0TG/S61cuijTg==", "dependencies": {"tslib": "^2.0.0"}, "engines": {"node": ">=10"}, "peerDependencies": {"@types/react": "*", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/use-memo-one": {"version": "1.1.3", "resolved": "https://registry.npmmirror.com/use-memo-one/-/use-memo-one-1.1.3.tgz", "integrity": "sha512-g66/K7ZQGYrI6dy8GLpVcMsBp4s17xNkYJVSMvTEevGy3nDxHOfE6z8BVE22+5G5x7t3+bhzrlTDB7ObrEE0cQ==", "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0"}}, "node_modules/use-sidecar": {"version": "1.1.3", "resolved": "https://registry.npmmirror.com/use-sidecar/-/use-sidecar-1.1.3.tgz", "integrity": "sha512-Fedw0aZvkhynoPYlA5WXrMCAMm+nSWdZt6lzJQ7Ok8S6Q+VsHmHpRWndVRJ8Be0ZbkfPc5LRYH+5XrzXcEeLRQ==", "dependencies": {"detect-node-es": "^1.1.0", "tslib": "^2.0.0"}, "engines": {"node": ">=10"}, "peerDependencies": {"@types/react": "*", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/uuid": {"version": "11.1.0", "resolved": "https://registry.npmmirror.com/uuid/-/uuid-11.1.0.tgz", "integrity": "sha512-0/A9rDy9P7cJ+8w1c9WD9V//9Wj15Ce2MPz8Ri6032usz+NfePxx5AcN3bN+r6ZL6jEo066/yNYB3tn4pQEx+A==", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "bin": {"uuid": "dist/esm/bin/uuid"}}, "node_modules/vite": {"version": "6.3.5", "resolved": "https://registry.npmmirror.com/vite/-/vite-6.3.5.tgz", "integrity": "sha512-cZn6NDFE7wdTpINgs++ZJ4N49W2vRp8LCKrn3Ob1kYNtOo21vfDoaV5GzBfLU4MovSAB8uNRm4jgzVQZ+mBzPQ==", "dev": true, "dependencies": {"esbuild": "^0.25.0", "fdir": "^6.4.4", "picomatch": "^4.0.2", "postcss": "^8.5.3", "rollup": "^4.34.9", "tinyglobby": "^0.2.13"}, "bin": {"vite": "bin/vite.js"}, "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": {"url": "https://github.com/vitejs/vite?sponsor=1"}, "optionalDependencies": {"fsevents": "~2.3.3"}, "peerDependencies": {"@types/node": "^18.0.0 || ^20.0.0 || >=22.0.0", "jiti": ">=1.21.0", "less": "*", "lightningcss": "^1.21.0", "sass": "*", "sass-embedded": "*", "stylus": "*", "sugarss": "*", "terser": "^5.16.0", "tsx": "^4.8.1", "yaml": "^2.4.2"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "jiti": {"optional": true}, "less": {"optional": true}, "lightningcss": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}, "tsx": {"optional": true}, "yaml": {"optional": true}}}, "node_modules/y18n": {"version": "5.0.8", "resolved": "https://registry.npmmirror.com/y18n/-/y18n-5.0.8.tgz", "integrity": "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==", "peer": true, "engines": {"node": ">=10"}}, "node_modules/yaml": {"version": "2.8.0", "resolved": "https://registry.npmmirror.com/yaml/-/yaml-2.8.0.tgz", "integrity": "sha512-4lLa/EcQCB0cJkyts+FpIRx5G/llPxfP6VQU5KByHEhLxY3IJCH0f0Hy1MHI8sClTvsIb8qwRJ6R/ZdlDJ/leQ==", "dev": true, "optional": true, "peer": true, "bin": {"yaml": "bin.mjs"}, "engines": {"node": ">= 14.6"}}, "node_modules/yargs": {"version": "17.7.2", "resolved": "https://registry.npmmirror.com/yargs/-/yargs-17.7.2.tgz", "integrity": "sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==", "peer": true, "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/yargs-parser": {"version": "21.1.1", "resolved": "https://registry.npmmirror.com/yargs-parser/-/yargs-parser-21.1.1.tgz", "integrity": "sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==", "peer": true, "engines": {"node": ">=12"}}, "node_modules/yargs/node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "peer": true, "engines": {"node": ">=8"}}, "node_modules/yargs/node_modules/emoji-regex": {"version": "8.0.0", "resolved": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==", "peer": true}, "node_modules/yargs/node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==", "peer": true, "engines": {"node": ">=8"}}, "node_modules/yargs/node_modules/string-width": {"version": "4.2.3", "resolved": "https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "peer": true, "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/yargs/node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "peer": true, "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/zrender": {"version": "5.6.1", "resolved": "https://registry.npmmirror.com/zrender/-/zrender-5.6.1.tgz", "integrity": "sha512-OFXkDJKcrlx5su2XbzJvj/34Q3m6PvyCZkVPHGYpcCJ52ek4U/ymZyfuV1nKE23AyBJ51E/6Yr0mhZ7xGTO4ag==", "dependencies": {"tslib": "2.3.0"}}, "node_modules/zrender/node_modules/tslib": {"version": "2.3.0", "resolved": "https://registry.npmmirror.com/tslib/-/tslib-2.3.0.tgz", "integrity": "sha512-N82ooyxVNm6h1riLCoyS9e3fuJ3AMG2zIZs2Gd1ATcSFjSA23Q0fzjjZeh0jbJvWVDZ0cJT8yaNNaaXHzueNjg=="}}}