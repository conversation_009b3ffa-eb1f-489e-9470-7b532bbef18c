import { IDataValidationRuleOptions } from '@univerjs/core';
export interface IListRenderModeInputProps {
    value: IDataValidationRuleOptions;
    onChange: (value: IDataValidationRuleOptions) => void;
}
export declare function ListRenderModeInput(props: IListRenderModeInputProps): import("react/jsx-runtime").JSX.Element;
export declare namespace ListRenderModeInput {
    var componentKey: string;
}
