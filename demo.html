<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Univer Sheet 演示</title>
    <!-- <script src="https://statics-1253756937.cos.ap-chengdu.myqcloud.com/public_plugins/univer-table/index.js"></script> -->
    <script src="./dist/index.js"></script>
    <link rel="stylesheet" href="./dist/index.css" />
    <script src="./business/jobTableUniver.js"></script>
    <script src="./data/data.js"></script>
</head>

<style>
    html,
    body {
        margin: 0;
        padding: 0;
        height: 100%;
        overflow: hidden;
    }

    #app {
        height: 100vh;
        width: 100%;
    }

    #univer {
        height: 100%;
        width: 100%;
        border: none;
    }

    .univer-menu-item-group {
        border: none !important;
    }

    .univer-menu-item-group+.univer-menu-item-group {
        display: none !important;
    }

    .univer-inline-flex.univer-h-4.univer-w-fit.univer-items-center.univer-overflow-hidden.univer-truncate.univer-whitespace-nowrap.univer-rounded-full.univer-text-xs.univer-text-gray-900 {
        background: transparent !important;
    }

    .univer-box-border.univer-px-2.univer-pt-1.univer-border-gray-200.univer-border-t.univer-border-0 {
        display: none !important;
    }

    .univer-box-border.univer-flex.univer-w-full.univer-resize.univer-rounded-md.univer-bg-transparent.univer-p-2.univer-text-base.univer-text-gray-900 {
        min-height: 38px !important;
    }
</style>

<body>
    <div id="app">
        <div id="univer"></div>
    </div>

    <script>
        const SHEET_CALL_EVENT = {
            activateCellEdit(data) {
                console.log(data);
            },
        };
    </script>
    <script>
        // 等待模块加载完成后初始化 JobTableUniver
        document.addEventListener("DOMContentLoaded", function () {
            initUniverApp();
        });

        function initUniverApp() {
            // 创建 Univer 实例
            const { univerAPI, univer } = window.createUniver();



            window.univerAPI = univerAPI
            // 创建 JobTableUniver 实例
            window.jobTableUniver = new JobTableUniver({ univerAPI, univer });
            jobTableUniver.initUniverApp({
                headerDictionaryIndex: 0,
                headers: titles,
                cells: lists,
                sheetCallEvent: SHEET_CALL_EVENT,
            });
        }
    </script>
</body>

</html>