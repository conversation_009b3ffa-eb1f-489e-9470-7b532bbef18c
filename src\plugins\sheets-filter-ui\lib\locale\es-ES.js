const e = {
  "sheets-filter": {
    toolbar: {
      "smart-toggle-filter-tooltip": "Alternar filtro",
      "clear-filter-criteria": "Borrar condiciones de filtro",
      "re-calc-filter-conditions": "Recalcular condiciones de filtro"
    },
    command: {
      "not-valid-filter-range": "El rango seleccionado solo tiene una fila y no es válido para filtrar."
    },
    shortcut: {
      "smart-toggle-filter": "Alternar filtro"
    },
    panel: {
      "clear-filter": "Borrar filtro",
      cancel: "Cancelar",
      confirm: "Confirmar",
      "by-values": "Por valores",
      "by-colors": "Por colores",
      "filter-by-cell-fill-color": "Filtrar por color de relleno de celda",
      "filter-by-cell-text-color": "Filtrar por color de texto de celda",
      "filter-by-color-none": "La columna contiene solo un color",
      "by-conditions": "Por condiciones",
      "filter-only": "Solo filtrar",
      "search-placeholder": "Usa espacio para separar palabras clave",
      "select-all": "Seleccionar todo",
      "input-values-placeholder": "Introducir valores",
      and: "Y",
      or: "O",
      empty: "(vacío)",
      "?": "Usa “?” para representar un solo carácter.",
      "*": "Usa “*” para representar varios caracteres."
    },
    conditions: {
      none: "Ninguno",
      empty: "Está vacío",
      "not-empty": "No está vacío",
      "text-contains": "El texto contiene",
      "does-not-contain": "El texto no contiene",
      "starts-with": "El texto comienza con",
      "ends-with": "El texto termina con",
      equals: "El texto es igual a",
      "greater-than": "Mayor que",
      "greater-than-or-equal": "Mayor o igual que",
      "less-than": "Menor que",
      "less-than-or-equal": "Menor o igual que",
      equal: "Igual",
      "not-equal": "No igual",
      between: "Entre",
      "not-between": "No entre",
      custom: "Personalizado"
    },
    msg: {
      "filter-header-forbidden": "No puedes mover la fila de encabezado de un filtro."
    },
    date: {
      1: "Enero",
      2: "Febrero",
      3: "Marzo",
      4: "Abril",
      5: "Mayo",
      6: "Junio",
      7: "Julio",
      8: "Agosto",
      9: "Septiembre",
      10: "Octubre",
      11: "Noviembre",
      12: "Diciembre"
    }
  }
};
export {
  e as default
};
