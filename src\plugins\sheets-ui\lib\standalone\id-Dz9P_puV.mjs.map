{"version": 3, "file": "id-Dz9P_puV.mjs", "sources": ["../../../engine-render/src/components/docs/layout/hyphenation/patterns/id.ts"], "sourcesContent": ["/**\n * Copyright 2023-present DreamNum Co., Ltd.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const Id = [\n    '01,21,201,232,0023,00203,2,021,02,10102,3,0202,00304',\n    '{\"a\":[{\"i\":[{\"r\":8}],\"n\":[{\".\":10}]},0],\"e\":0,\"i\":[{\"o\":[{\"n\":7}]},0],\"o\":0,\"u\":0,\"b\":[{\"d\":1,\"j\":1,\"k\":1,\"n\":1,\"s\":1,\"t\":1,\"a\":[{\"g\":[{\"a\":[{\"i\":9}]}],\"n\":[{\".\":1}]}]}],\"c\":[{\"k\":1,\"n\":1,\"a\":[{\"n\":[{\".\":1}]}]}],\"d\":[{\"k\":1,\"n\":1,\"p\":1,\"a\":[{\"n\":[{\".\":1}]}]}],\"f\":[{\"d\":1,\"k\":1,\"n\":1,\"t\":1,\"a\":[{\"n\":[{\".\":1}]}]}],\"g\":[{\"g\":1,\"k\":1,\"n\":1,\"a\":[{\"n\":[{\".\":1}]}]}],\"h\":[{\"k\":1,\"l\":1,\"m\":1,\"n\":1,\"w\":1,\"a\":[{\"n\":[{\".\":1}]}]}],\"j\":[{\"k\":1,\"n\":1,\"a\":[{\"n\":[{\".\":1}]}]}],\"k\":[{\"b\":1,\"k\":1,\"m\":1,\"n\":1,\"r\":1,\"s\":1,\"t\":1,\"a\":[{\"n\":[{\".\":1}]}]}],\"l\":[{\"b\":1,\"f\":1,\"g\":1,\"h\":1,\"k\":1,\"m\":1,\"n\":1,\"s\":1,\"t\":1,\"q\":1,\"a\":[{\"n\":[{\".\":1}]}]}],\"m\":[{\"b\":1,\"k\":1,\"l\":1,\"m\":1,\"n\":1,\"p\":1,\"r\":1,\"s\":1,\"a\":[{\"n\":[{\".\":1}]}]}],\"n\":[{\"c\":1,\"d\":1,\"f\":1,\"j\":1,\"k\":1,\"n\":1,\"p\":1,\"s\":[{\"t\":3},1],\"t\":1,\"v\":1,\"g\":[{\"g\":2,\"h\":2,\"k\":2,\"n\":2,\"s\":2,\".\":6,\"a\":[{\"n\":[{\".\":2}]}]}],\"y\":[{\".\":6}],\"a\":[{\"n\":[{\".\":1}]}]}],\"p\":[{\"k\":1,\"n\":1,\"p\":1,\"r\":1,\"t\":1,\"a\":[{\"n\":[{\".\":1}]}]}],\"r\":[{\"b\":1,\"c\":1,\"f\":1,\"g\":1,\"h\":1,\"j\":1,\"k\":1,\"l\":1,\"m\":1,\"n\":1,\"p\":1,\"r\":1,\"s\":1,\"t\":1,\"w\":1,\"y\":1,\"a\":[{\"n\":[{\".\":1}]}]}],\"s\":[{\"b\":1,\"k\":1,\"l\":1,\"m\":1,\"n\":1,\"p\":1,\"r\":1,\"s\":1,\"t\":1,\"w\":1,\"a\":[{\"n\":[{\".\":1}]}]}],\"t\":[{\"k\":1,\"l\":1,\"n\":1,\"t\":1,\"a\":[{\"n\":[{\".\":1}]}]}],\"w\":[{\"t\":1}],\".\":[{\"b\":[{\"e\":[{\"r\":4}]}],\"t\":[{\"e\":[{\"r\":4}],\"a\":[{\"n\":[{\"g\":[{\"a\":[{\"n\":[{\".\":12}]}]}]}]}]}],\"m\":[{\"e\":[{\"n\":[{\"g\":5}]}],\"a\":[{\"n\":[{\"g\":[{\"a\":[{\"n\":[{\".\":12}]}]}]}]}]}],\"p\":[{\"e\":[{\"r\":4}],\"a\":[{\"n\":[{\"g\":[{\"a\":[{\"n\":[{\".\":12}]}]}]}]}]}],\"a\":[{\"t\":[{\"a\":[{\"u\":11}]}]}],\"l\":[{\"e\":[{\"n\":[{\"g\":[{\"a\":[{\"n\":[{\".\":12}]}]}]}]}]}],\"j\":[{\"a\":[{\"n\":[{\"g\":[{\"a\":[{\"n\":[{\".\":12}]}]}]}]}]}],\"r\":[{\"i\":[{\"n\":[{\"g\":[{\"a\":[{\"n\":[{\".\":12}]}]}]}]}]}],\"d\":[{\"e\":[{\"n\":[{\"g\":[{\"a\":[{\"n\":[{\".\":12}]}]}]}]}]}]}],\"v\":[{\"a\":[{\"n\":[{\".\":1}]}]}],\"z\":[{\"a\":[{\"n\":[{\".\":1}]}]}]}',\n    [\n        'be-ra-be be-ra-hi be-rak be-ran-da be-ran-dal be-rang',\n        'be-ra-ngas-an',\n        'be-rang-sang',\n        'be-ra-ngus',\n        'be-ra-ni',\n        'be-ran-tak-an',\n        'be-ran-tam',\n        'be-ran-tas',\n        'be-ra-pa',\n        'be-ras',\n        'be-ren-deng',\n        'be-re-ngut',\n        'be-re-rot',\n        'be-res',\n        'be-re-wok',\n        'be-ri',\n        'be-ri-ngas',\n        'be-ri-sik',\n        'be-ri-ta',\n        'be-rok',\n        'be-ron-dong',\n        'be-ron-tak',\n        'be-ru-du',\n        'be-ruk',\n        'be-run-tun',\n        'peng-eks-por',\n        'peng-im-por',\n        'te-ra',\n        'te-rang',\n        'te-ras',\n        'te-ra-si',\n        'te-ra-tai',\n        'te-ra-wang',\n        'te-ra-weh',\n        'te-ri-ak',\n        'te-ri-gu',\n        'te-rik',\n        'te-ri-ma',\n        'te-ri-pang',\n        'te-ro-bos',\n        'te-ro-bos-an',\n        'te-ro-mol',\n        'te-rom-pah',\n        'te-rom-pet',\n        'te-ro-pong',\n        '             te-ro-wong-an te-ru-buk te-ru-na te-rus te-ru-si',\n    ],\n];\n"], "names": [], "mappings": "AAgBO,MAAM,KAAK;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,IACI;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAAA;AAER;"}