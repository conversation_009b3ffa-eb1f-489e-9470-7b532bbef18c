{"version": 3, "file": "gu-wLi0mXrk.mjs", "sources": ["../../../engine-render/src/components/docs/layout/hyphenation/patterns/gu.ts"], "sourcesContent": ["/**\n * Copyright 2023-present DreamNum Co., Ltd.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const Gu = [\n    '22,11,01,1,21',\n    '{\"‍\":0,\"‌\":1,\"અ\":2,\"આ\":2,\"ઇ\":2,\"ઈ\":2,\"ઉ\":2,\"ઊ\":2,\"ઋ\":2,\"ૠ\":2,\"એ\":2,\"ઐ\":2,\"ઓ\":2,\"ઔ\":2,\"ા\":2,\"િ\":2,\"ી\":2,\"ુ\":2,\"ૂ\":2,\"ૃ\":2,\"ૄ\":2,\"ૢ\":2,\"ૣ\":2,\"ે\":2,\"ૈ\":2,\"ો\":2,\"ૌ\":2,\"ક\":3,\"ખ\":3,\"ગ\":3,\"ઘ\":3,\"ઙ\":3,\"ચ\":3,\"છ\":3,\"જ\":3,\"ઝ\":3,\"ઞ\":3,\"ટ\":3,\"ઠ\":3,\"ડ\":3,\"ઢ\":3,\"ણ\":3,\"ત\":3,\"થ\":3,\"દ\":3,\"ધ\":3,\"ન\":3,\"પ\":3,\"ફ\":3,\"બ\":3,\"ભ\":3,\"મ\":3,\"ય\":3,\"ર\":3,\"લ\":3,\"ળ\":3,\"વ\":3,\"શ\":3,\"ષ\":3,\"સ\":3,\"હ\":3,\"ઁ\":4,\"ઃ\":4,\"ઽ\":4,\"્\":0,\"ં\":0}',\n    [],\n];\n"], "names": [], "mappings": "AAgBO,MAAM,KAAK;AAAA,EACd;AAAA,EACA;AAAA,EACA,CAAA;AACJ;"}