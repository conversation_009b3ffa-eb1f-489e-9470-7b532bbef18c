.univer-pointer-events-none {
    pointer-events: none
}
.univer-absolute {
    position: absolute
}
.univer-relative {
    position: relative
}
.univer-inset-0 {
    top: 0px;
    right: 0px;
    bottom: 0px;
    left: 0px
}
.-univer-bottom-0\.5 {
    bottom: -0.125rem
}
.-univer-left-0\.5 {
    left: -0.125rem
}
.-univer-right-0\.5 {
    right: -0.125rem
}
.-univer-top-0\.5 {
    top: -0.125rem
}
.univer-left-0 {
    left: 0px
}
.univer-left-1 {
    left: 0.25rem
}
.univer-right-0 {
    right: 0px
}
.univer-right-5 {
    right: 1.25rem
}
.univer-right-\[60px\] {
    right: 60px
}
.univer-top-0 {
    top: 0px
}
.univer-top-0\.5 {
    top: 0.125rem
}
.univer-top-1\/2 {
    top: 50%
}
.univer-z-10 {
    z-index: 10
}
.univer-z-\[1001\] {
    z-index: 1001
}
.univer-z-\[100\] {
    z-index: 100
}
.univer-m-0 {
    margin: 0px
}
.univer-mx-1 {
    margin-left: 0.25rem;
    margin-right: 0.25rem
}
.univer-my-1 {
    margin-top: 0.25rem;
    margin-bottom: 0.25rem
}
.univer-my-1\.5 {
    margin-top: 0.375rem;
    margin-bottom: 0.375rem
}
.univer-my-2 {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem
}
.univer-mb-1\.5 {
    margin-bottom: 0.375rem
}
.univer-mb-2 {
    margin-bottom: 0.5rem
}
.univer-mb-3 {
    margin-bottom: 0.75rem
}
.univer-mb-4 {
    margin-bottom: 1rem
}
.univer-ml-1 {
    margin-left: 0.25rem
}
.univer-ml-1\.5 {
    margin-left: 0.375rem
}
.univer-ml-3 {
    margin-left: 0.75rem
}
.univer-ml-px {
    margin-left: 1px
}
.univer-mr-1\.5 {
    margin-right: 0.375rem
}
.univer-mr-2 {
    margin-right: 0.5rem
}
.univer-mr-5 {
    margin-right: 1.25rem
}
.univer-mt-1 {
    margin-top: 0.25rem
}
.univer-mt-2 {
    margin-top: 0.5rem
}
.univer-mt-3 {
    margin-top: 0.75rem
}
.univer-mt-4 {
    margin-top: 1rem
}
.univer-mt-auto {
    margin-top: auto
}
.univer-box-border {
    box-sizing: border-box
}
.univer-block {
    display: block
}
.univer-flex {
    display: flex
}
.univer-inline-flex {
    display: inline-flex
}
.univer-grid {
    display: grid
}
.univer-hidden {
    display: none
}
.univer-size-0 {
    width: 0px;
    height: 0px
}
.univer-size-10 {
    width: 2.5rem;
    height: 2.5rem
}
.univer-size-4 {
    width: 1rem;
    height: 1rem
}
.univer-size-6 {
    width: 1.5rem;
    height: 1.5rem
}
.univer-h-0\.5 {
    height: 0.125rem
}
.univer-h-20 {
    height: 5rem
}
.univer-h-4 {
    height: 1rem
}
.univer-h-5 {
    height: 1.25rem
}
.univer-h-6 {
    height: 1.5rem
}
.univer-h-60 {
    height: 15rem
}
.univer-h-7 {
    height: 1.75rem
}
.univer-h-8 {
    height: 2rem
}
.univer-h-9 {
    height: 2.25rem
}
.univer-h-\[270px\] {
    height: 270px
}
.univer-h-\[30px\] {
    height: 30px
}
.univer-h-\[calc\(100\%-16px\)\] {
    height: calc(100% - 16px)
}
.univer-h-\[calc\(100\%-8px\)\] {
    height: calc(100% - 8px)
}
.univer-h-full {
    height: 100%
}
.univer-h-px {
    height: 1px
}
.univer-max-h-52 {
    max-height: 13rem
}
.univer-max-h-\[100px\] {
    max-height: 100px
}
.univer-max-h-\[360px\] {
    max-height: 360px
}
.\!univer-w-\[90px\] {
    width: 90px !important
}
.univer-w-16 {
    width: 4rem
}
.univer-w-20 {
    width: 5rem
}
.univer-w-24 {
    width: 6rem
}
.univer-w-4 {
    width: 1rem
}
.univer-w-5 {
    width: 1.25rem
}
.univer-w-6 {
    width: 1.5rem
}
.univer-w-60 {
    width: 15rem
}
.univer-w-\[100px\] {
    width: 100px
}
.univer-w-\[130px\] {
    width: 130px
}
.univer-w-\[156px\] {
    width: 156px
}
.univer-w-\[300px\] {
    width: 300px
}
.univer-w-\[50\%\] {
    width: 50%
}
.univer-w-fit {
    width: -moz-fit-content;
    width: fit-content
}
.univer-w-full {
    width: 100%
}
.univer-min-w-0 {
    min-width: 0px
}
.univer-min-w-12 {
    min-width: 3rem
}
.univer-max-w-24 {
    max-width: 6rem
}
.univer-max-w-32 {
    max-width: 8rem
}
.univer-max-w-64 {
    max-width: 16rem
}
.univer-max-w-80 {
    max-width: 20rem
}
.univer-max-w-\[120px\] {
    max-width: 120px
}
.univer-max-w-\[190px\] {
    max-width: 190px
}
.univer-max-w-\[200px\] {
    max-width: 200px
}
.univer-max-w-\[calc\(100\%-112px\)\] {
    max-width: calc(100% - 112px)
}
.univer-flex-1 {
    flex: 1 1 0%
}
.univer-flex-shrink-0 {
    flex-shrink: 0
}
.univer-shrink-0 {
    flex-shrink: 0
}
.univer-flex-grow {
    flex-grow: 1
}
.univer-flex-grow-0 {
    flex-grow: 0
}
.-univer-translate-y-1\/2 {
    --univer-tw-translate-y: -50%;
    transform: translate(var(--univer-tw-translate-x), -50%) rotate(var(--univer-tw-rotate)) skewX(var(--univer-tw-skew-x)) skewY(var(--univer-tw-skew-y)) scaleX(var(--univer-tw-scale-x)) scaleY(var(--univer-tw-scale-y));
    transform: translate(var(--univer-tw-translate-x), var(--univer-tw-translate-y)) rotate(var(--univer-tw-rotate)) skewX(var(--univer-tw-skew-x)) skewY(var(--univer-tw-skew-y)) scaleX(var(--univer-tw-scale-x)) scaleY(var(--univer-tw-scale-y))
}
.univer-rotate-180 {
    --univer-tw-rotate: 180deg;
    transform: translate(var(--univer-tw-translate-x), var(--univer-tw-translate-y)) rotate(180deg) skewX(var(--univer-tw-skew-x)) skewY(var(--univer-tw-skew-y)) scaleX(var(--univer-tw-scale-x)) scaleY(var(--univer-tw-scale-y));
    transform: translate(var(--univer-tw-translate-x), var(--univer-tw-translate-y)) rotate(var(--univer-tw-rotate)) skewX(var(--univer-tw-skew-x)) skewY(var(--univer-tw-skew-y)) scaleX(var(--univer-tw-scale-x)) scaleY(var(--univer-tw-scale-y))
}
@keyframes univer-spin {
    to {
        transform: rotate(360deg)
    }
}
.univer-animate-spin {
    animation: univer-spin 1s linear infinite
}
.univer-cursor-default {
    cursor: default
}
.univer-cursor-not-allowed {
    cursor: not-allowed
}
.univer-cursor-pointer {
    cursor: pointer
}
.univer-select-none {
    -webkit-user-select: none;
       -moz-user-select: none;
            user-select: none
}
.univer-list-none {
    list-style-type: none
}
.univer-appearance-none {
    -webkit-appearance: none;
       -moz-appearance: none;
            appearance: none
}
.univer-grid-flow-col {
    grid-auto-flow: column
}
.univer-grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr))
}
.univer-grid-cols-\[1fr\,auto\,auto\,auto\] {
    grid-template-columns: 1fr auto auto auto
}
.univer-flex-row {
    flex-direction: row
}
.univer-flex-row-reverse {
    flex-direction: row-reverse
}
.univer-flex-col {
    flex-direction: column
}
.univer-flex-nowrap {
    flex-wrap: nowrap
}
.univer-items-center {
    align-items: center
}
.univer-justify-end {
    justify-content: flex-end
}
.univer-justify-center {
    justify-content: center
}
.univer-justify-between {
    justify-content: space-between
}
.univer-gap-1 {
    gap: 0.25rem
}
.univer-gap-2 {
    gap: 0.5rem
}
.univer-gap-x-2 {
    -moz-column-gap: 0.5rem;
         column-gap: 0.5rem
}
.univer-space-y-2 > :not([hidden]) ~ :not([hidden]) {
    --univer-tw-space-y-reverse: 0;
    margin-top: calc(0.5rem * (1 - 0));
    margin-top: calc(0.5rem * (1 - var(--univer-tw-space-y-reverse)));
    margin-top: calc(0.5rem * calc(1 - 0));
    margin-top: calc(0.5rem * calc(1 - var(--univer-tw-space-y-reverse)));
    margin-bottom: calc(0.5rem * 0);
    margin-bottom: calc(0.5rem * var(--univer-tw-space-y-reverse))
}
.univer-divide-x-0 > :not([hidden]) ~ :not([hidden]) {
    --univer-tw-divide-x-reverse: 0;
    border-right-width: calc(0px * 0);
    border-right-width: calc(0px * var(--univer-tw-divide-x-reverse));
    border-left-width: calc(0px * (1 - 0));
    border-left-width: calc(0px * (1 - var(--univer-tw-divide-x-reverse)));
    border-left-width: calc(0px * calc(1 - 0));
    border-left-width: calc(0px * calc(1 - var(--univer-tw-divide-x-reverse)))
}
.univer-divide-y > :not([hidden]) ~ :not([hidden]) {
    --univer-tw-divide-y-reverse: 0;
    border-top-width: calc(1px * (1 - 0));
    border-top-width: calc(1px * (1 - var(--univer-tw-divide-y-reverse)));
    border-top-width: calc(1px * calc(1 - 0));
    border-top-width: calc(1px * calc(1 - var(--univer-tw-divide-y-reverse)));
    border-bottom-width: calc(1px * 0);
    border-bottom-width: calc(1px * var(--univer-tw-divide-y-reverse))
}
.univer-divide-solid > :not([hidden]) ~ :not([hidden]) {
    border-style: solid
}
.univer-divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
    border-color: var(--univer-gray-200)
}
.univer-justify-self-center {
    justify-self: center
}
.univer-overflow-hidden {
    overflow: hidden
}
.univer-overflow-y-auto {
    overflow-y: auto
}
.univer-overflow-x-scroll {
    overflow-x: scroll
}
.univer-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}
.univer-text-ellipsis {
    text-overflow: ellipsis
}
.univer-whitespace-nowrap {
    white-space: nowrap
}
.univer-break-words {
    word-wrap: break-word
}
.univer-rounded {
    border-radius: 0.25rem
}
.univer-rounded-full {
    border-radius: 9999px
}
.univer-rounded-lg {
    border-radius: 0.5rem
}
.univer-rounded-md {
    border-radius: 0.375rem
}
.univer-rounded-sm {
    border-radius: 0.125rem
}
.univer-border-2 {
    border-width: 2px
}
.univer-border-4 {
    border-width: 4px
}
.univer-border-solid {
    border-style: solid
}
.univer-border-none {
    border-style: none
}
.univer-border-gray-100 {
    border-color: var(--univer-gray-100)
}
.univer-border-transparent {
    border-color: transparent
}
.univer-border-r-gray-200 {
    border-right-color: var(--univer-gray-200)
}
.univer-border-t-primary-500 {
    border-top-color: var(--univer-primary-500)
}
.\!univer-bg-gray-600 {
    background-color: var(--univer-gray-600) !important
}
.\!univer-bg-gray-700 {
    background-color: var(--univer-gray-700) !important
}
.\!univer-bg-gray-800 {
    background-color: var(--univer-gray-800) !important
}
.\!univer-bg-gray-900 {
    background-color: var(--univer-gray-900) !important
}
.\!univer-bg-slate-600 {
    --univer-tw-bg-opacity: 1 !important;
    background-color: rgba(71, 85, 105, 1) !important;
    background-color: rgba(71, 85, 105, var(--univer-tw-bg-opacity, 1)) !important
}
.univer-bg-blue-500 {
    background-color: var(--univer-blue-500)
}
.univer-bg-gray-100 {
    background-color: var(--univer-gray-100)
}
.univer-bg-gray-200 {
    background-color: var(--univer-gray-200)
}
.univer-bg-transparent {
    background-color: transparent
}
.univer-bg-white {
    background-color: var(--univer-white)
}
.univer-fill-gray-900 {
    fill: var(--univer-gray-900)
}
.univer-fill-primary-600 {
    fill: var(--univer-primary-600)
}
.univer-p-0 {
    padding: 0px
}
.univer-p-1 {
    padding: 0.25rem
}
.univer-p-1\.5 {
    padding: 0.375rem
}
.univer-p-2 {
    padding: 0.5rem
}
.univer-p-3 {
    padding: 0.75rem
}
.univer-p-4 {
    padding: 1rem
}
.univer-px-1 {
    padding-left: 0.25rem;
    padding-right: 0.25rem
}
.univer-px-1\.5 {
    padding-left: 0.375rem;
    padding-right: 0.375rem
}
.univer-px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem
}
.univer-px-3\.5 {
    padding-left: 0.875rem;
    padding-right: 0.875rem
}
.univer-px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem
}
.univer-py-0\.5 {
    padding-top: 0.125rem;
    padding-bottom: 0.125rem
}
.univer-py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem
}
.univer-py-1\.5 {
    padding-top: 0.375rem;
    padding-bottom: 0.375rem
}
.univer-py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem
}
.univer-py-5 {
    padding-top: 1.25rem;
    padding-bottom: 1.25rem
}
.univer-pb-1 {
    padding-bottom: 0.25rem
}
.univer-pl-3 {
    padding-left: 0.75rem
}
.univer-pl-6 {
    padding-left: 1.5rem
}
.univer-pt-1 {
    padding-top: 0.25rem
}
.univer-pt-2 {
    padding-top: 0.5rem
}
.univer-text-center {
    text-align: center
}
.univer-text-base {
    font-size: 1rem;
    line-height: 1.5rem
}
.univer-text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem
}
.univer-text-xs {
    font-size: 0.75rem;
    line-height: 1rem
}
.univer-font-bold {
    font-weight: 700
}
.univer-font-medium {
    font-weight: 500
}
.univer-font-semibold {
    font-weight: 600
}
.univer-leading-5 {
    line-height: 1.25rem
}
.univer-leading-6 {
    line-height: 1.5rem
}
.univer-leading-7 {
    line-height: 1.75rem
}
.\!univer-text-gray-700 {
    color: var(--univer-gray-700) !important
}
.\!univer-text-white {
    color: var(--univer-white) !important
}
.univer-text-blue-500 {
    color: var(--univer-blue-500)
}
.univer-text-gray-200 {
    color: var(--univer-gray-200)
}
.univer-text-gray-300 {
    color: var(--univer-gray-300)
}
.univer-text-gray-400 {
    color: var(--univer-gray-400)
}
.univer-text-gray-500 {
    color: var(--univer-gray-500)
}
.univer-text-gray-600 {
    color: var(--univer-gray-600)
}
.univer-text-gray-700 {
    color: var(--univer-gray-700)
}
.univer-text-gray-900 {
    color: var(--univer-gray-900)
}
.univer-text-green-600 {
    color: var(--univer-green-600)
}
.univer-text-primary-500 {
    color: var(--univer-primary-500)
}
.univer-text-primary-600 {
    color: var(--univer-primary-600)
}
.univer-text-primary-700 {
    color: var(--univer-primary-700)
}
.univer-text-red-500 {
    color: var(--univer-red-500)
}
.univer-text-red-600 {
    color: var(--univer-red-600)
}
.univer-text-white {
    color: var(--univer-white)
}
.univer-text-yellow-500 {
    color: var(--univer-yellow-500)
}
.univer-shadow {
    --univer-tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    --univer-tw-shadow-colored: 0 1px 3px 0 var(--univer-tw-shadow-color), 0 1px 2px -1px var(--univer-tw-shadow-color);
    box-shadow: 0 0 rgba(0,0,0,0), 0 0 rgba(0,0,0,0), 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    box-shadow: var(--univer-tw-ring-offset-shadow, 0 0 rgba(0,0,0,0)), var(--univer-tw-ring-shadow, 0 0 rgba(0,0,0,0)), var(--univer-tw-shadow)
}
.univer-shadow-lg {
    --univer-tw-shadow: 0px 4px 6px 0px rgba(30, 40, 77, 0.05), 0px 10px 15px -3px rgba(30, 40, 77, 0.10);
    --univer-tw-shadow-colored: 0px 4px 6px 0px var(--univer-tw-shadow-color), 0px 10px 15px -3px var(--univer-tw-shadow-color);
    box-shadow: 0 0 rgba(0,0,0,0), 0 0 rgba(0,0,0,0), 0px 4px 6px 0px rgba(30, 40, 77, 0.05), 0px 10px 15px -3px rgba(30, 40, 77, 0.10);
    box-shadow: var(--univer-tw-ring-offset-shadow, 0 0 rgba(0,0,0,0)), var(--univer-tw-ring-shadow, 0 0 rgba(0,0,0,0)), var(--univer-tw-shadow)
}
.univer-outline-none {
    outline: 2px solid transparent;
    outline-offset: 2px
}
.univer-blur-sm {
    --univer-tw-blur: blur(4px);
    filter: blur(4px) var(--univer-tw-brightness) var(--univer-tw-contrast) var(--univer-tw-grayscale) var(--univer-tw-hue-rotate) var(--univer-tw-invert) var(--univer-tw-saturate) var(--univer-tw-sepia) var(--univer-tw-drop-shadow);
    filter: var(--univer-tw-blur) var(--univer-tw-brightness) var(--univer-tw-contrast) var(--univer-tw-grayscale) var(--univer-tw-hue-rotate) var(--univer-tw-invert) var(--univer-tw-saturate) var(--univer-tw-sepia) var(--univer-tw-drop-shadow)
}
.univer-backdrop-blur {
    --univer-tw-backdrop-blur: blur(8px);
    -webkit-backdrop-filter: blur(8px) var(--univer-tw-backdrop-brightness) var(--univer-tw-backdrop-contrast) var(--univer-tw-backdrop-grayscale) var(--univer-tw-backdrop-hue-rotate) var(--univer-tw-backdrop-invert) var(--univer-tw-backdrop-opacity) var(--univer-tw-backdrop-saturate) var(--univer-tw-backdrop-sepia);
    -webkit-backdrop-filter: var(--univer-tw-backdrop-blur) var(--univer-tw-backdrop-brightness) var(--univer-tw-backdrop-contrast) var(--univer-tw-backdrop-grayscale) var(--univer-tw-backdrop-hue-rotate) var(--univer-tw-backdrop-invert) var(--univer-tw-backdrop-opacity) var(--univer-tw-backdrop-saturate) var(--univer-tw-backdrop-sepia);
    backdrop-filter: blur(8px) var(--univer-tw-backdrop-brightness) var(--univer-tw-backdrop-contrast) var(--univer-tw-backdrop-grayscale) var(--univer-tw-backdrop-hue-rotate) var(--univer-tw-backdrop-invert) var(--univer-tw-backdrop-opacity) var(--univer-tw-backdrop-saturate) var(--univer-tw-backdrop-sepia);
    backdrop-filter: var(--univer-tw-backdrop-blur) var(--univer-tw-backdrop-brightness) var(--univer-tw-backdrop-contrast) var(--univer-tw-backdrop-grayscale) var(--univer-tw-backdrop-hue-rotate) var(--univer-tw-backdrop-invert) var(--univer-tw-backdrop-opacity) var(--univer-tw-backdrop-saturate) var(--univer-tw-backdrop-sepia)
}
.univer-transition-\[colors\,box-shadow\] {
    transition-property: colors,box-shadow;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms
}
.univer-transition-\[height\] {
    transition-property: height;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms
}
.univer-transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms
}
.univer-transition-colors {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms
}
.univer-duration-200 {
    transition-duration: 200ms
}
.univer-ease-linear {
    transition-timing-function: linear
}
.after\:univer-absolute::after {
    content: var(--univer-tw-content);
    position: absolute
}
.after\:univer-right-0::after {
    content: var(--univer-tw-content);
    right: 0px
}
.after\:univer-top-1\/2::after {
    content: var(--univer-tw-content);
    top: 50%
}
.after\:univer-block::after {
    content: var(--univer-tw-content);
    display: block
}
.after\:univer-h-4::after {
    content: var(--univer-tw-content);
    height: 1rem
}
.after\:univer-w-px::after {
    content: var(--univer-tw-content);
    width: 1px
}
.after\:-univer-translate-y-1\/2::after {
    content: var(--univer-tw-content);
    --univer-tw-translate-y: -50%;
    transform: translate(var(--univer-tw-translate-x), -50%) rotate(var(--univer-tw-rotate)) skewX(var(--univer-tw-skew-x)) skewY(var(--univer-tw-skew-y)) scaleX(var(--univer-tw-scale-x)) scaleY(var(--univer-tw-scale-y));
    transform: translate(var(--univer-tw-translate-x), var(--univer-tw-translate-y)) rotate(var(--univer-tw-rotate)) skewX(var(--univer-tw-skew-x)) skewY(var(--univer-tw-skew-y)) scaleX(var(--univer-tw-scale-x)) scaleY(var(--univer-tw-scale-y))
}
.after\:univer-bg-gray-200::after {
    content: var(--univer-tw-content);
    background-color: var(--univer-gray-200)
}
.after\:univer-content-\[\"\"\]::after {
    --univer-tw-content: "";
    content: "";
    content: var(--univer-tw-content)
}
.last\:univer-mb-0:last-child {
    margin-bottom: 0px
}
.hover\:univer-border-primary-600:hover {
    border-color: var(--univer-primary-600)
}
.hover\:univer-bg-gray-100:hover {
    background-color: var(--univer-gray-100)
}
.hover\:univer-bg-gray-200:hover {
    background-color: var(--univer-gray-200)
}
.hover\:univer-bg-gray-50:hover {
    background-color: var(--univer-gray-50)
}
.hover\:univer-bg-transparent:hover {
    background-color: transparent
}
.focus\:univer-outline-none:focus {
    outline: 2px solid transparent;
    outline-offset: 2px
}
.univer-group:hover .group-hover\:univer-flex {
    display: flex
}
.dark\:\!univer-divide-gray-600:where(.univer-dark, .univer-dark *) > :not([hidden]) ~ :not([hidden]) {
    border-color: var(--univer-gray-600) !important
}
.dark\:\!univer-border-r-gray-700:where(.univer-dark, .univer-dark *) {
    border-right-color: var(--univer-gray-700) !important
}
.dark\:\!univer-bg-black:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-black) !important
}
.dark\:\!univer-bg-gray-600:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-600) !important
}
.dark\:\!univer-bg-gray-700:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-700) !important
}
.dark\:\!univer-bg-gray-800:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-800) !important
}
.dark\:\!univer-bg-gray-900:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-900) !important
}
.dark\:\!univer-bg-slate-600:where(.univer-dark, .univer-dark *) {
    --univer-tw-bg-opacity: 1 !important;
    background-color: rgba(71, 85, 105, 1) !important;
    background-color: rgba(71, 85, 105, var(--univer-tw-bg-opacity, 1)) !important
}
.dark\:\!univer-fill-white:where(.univer-dark, .univer-dark *) {
    fill: var(--univer-white) !important
}
.dark\:\!univer-text-gray-200:where(.univer-dark, .univer-dark *) {
    color: var(--univer-gray-200) !important
}
.dark\:\!univer-text-gray-700:where(.univer-dark, .univer-dark *) {
    color: var(--univer-gray-700) !important
}
.dark\:\!univer-text-green-400:where(.univer-dark, .univer-dark *) {
    color: var(--univer-green-400) !important
}
.dark\:\!univer-text-red-400:where(.univer-dark, .univer-dark *) {
    color: var(--univer-red-400) !important
}
.dark\:\!univer-text-white:where(.univer-dark, .univer-dark *) {
    color: var(--univer-white) !important
}
.dark\:hover\:\!univer-bg-gray-600:hover:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-600) !important
}
.dark\:hover\:\!univer-bg-gray-700:hover:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-700) !important
}
.dark\:hover\:\!univer-bg-gray-800:hover:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-800) !important
}
.\[\&\>div\:first-child\]\:univer-px-2\.5>div:first-child {
    padding-left: 0.625rem;
    padding-right: 0.625rem
}
.\[\&\>div\]\:univer-h-5>div {
    height: 1.25rem
}
.\[\&\>div\]\:univer-ring-transparent>div {
    --univer-tw-ring-color: transparent
}
.\[\&_canvas\]\:univer-absolute canvas {
    position: absolute
}
.univer-pointer-events-none {
    pointer-events: none
}
.univer-absolute {
    position: absolute
}
.univer-relative {
    position: relative
}
.univer-inset-0 {
    top: 0px;
    right: 0px;
    bottom: 0px;
    left: 0px
}
.-univer-bottom-0\.5 {
    bottom: -0.125rem
}
.-univer-left-0\.5 {
    left: -0.125rem
}
.-univer-right-0\.5 {
    right: -0.125rem
}
.-univer-top-0\.5 {
    top: -0.125rem
}
.univer-left-0 {
    left: 0px
}
.univer-left-1 {
    left: 0.25rem
}
.univer-right-0 {
    right: 0px
}
.univer-right-5 {
    right: 1.25rem
}
.univer-right-\[60px\] {
    right: 60px
}
.univer-top-0 {
    top: 0px
}
.univer-top-0\.5 {
    top: 0.125rem
}
.univer-top-1\/2 {
    top: 50%
}
.univer-z-10 {
    z-index: 10
}
.univer-z-\[1001\] {
    z-index: 1001
}
.univer-z-\[100\] {
    z-index: 100
}
.univer-m-0 {
    margin: 0px
}
.univer-mx-1 {
    margin-left: 0.25rem;
    margin-right: 0.25rem
}
.univer-my-1 {
    margin-top: 0.25rem;
    margin-bottom: 0.25rem
}
.univer-my-1\.5 {
    margin-top: 0.375rem;
    margin-bottom: 0.375rem
}
.univer-my-2 {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem
}
.univer-mb-1\.5 {
    margin-bottom: 0.375rem
}
.univer-mb-2 {
    margin-bottom: 0.5rem
}
.univer-mb-3 {
    margin-bottom: 0.75rem
}
.univer-mb-4 {
    margin-bottom: 1rem
}
.univer-ml-1 {
    margin-left: 0.25rem
}
.univer-ml-1\.5 {
    margin-left: 0.375rem
}
.univer-ml-3 {
    margin-left: 0.75rem
}
.univer-ml-px {
    margin-left: 1px
}
.univer-mr-1\.5 {
    margin-right: 0.375rem
}
.univer-mr-2 {
    margin-right: 0.5rem
}
.univer-mr-5 {
    margin-right: 1.25rem
}
.univer-mt-1 {
    margin-top: 0.25rem
}
.univer-mt-2 {
    margin-top: 0.5rem
}
.univer-mt-3 {
    margin-top: 0.75rem
}
.univer-mt-4 {
    margin-top: 1rem
}
.univer-mt-auto {
    margin-top: auto
}
.univer-box-border {
    box-sizing: border-box
}
.univer-block {
    display: block
}
.univer-flex {
    display: flex
}
.univer-inline-flex {
    display: inline-flex
}
.univer-grid {
    display: grid
}
.univer-hidden {
    display: none
}
.univer-size-0 {
    width: 0px;
    height: 0px
}
.univer-size-10 {
    width: 2.5rem;
    height: 2.5rem
}
.univer-size-4 {
    width: 1rem;
    height: 1rem
}
.univer-size-6 {
    width: 1.5rem;
    height: 1.5rem
}
.univer-h-0\.5 {
    height: 0.125rem
}
.univer-h-20 {
    height: 5rem
}
.univer-h-4 {
    height: 1rem
}
.univer-h-5 {
    height: 1.25rem
}
.univer-h-6 {
    height: 1.5rem
}
.univer-h-60 {
    height: 15rem
}
.univer-h-7 {
    height: 1.75rem
}
.univer-h-8 {
    height: 2rem
}
.univer-h-9 {
    height: 2.25rem
}
.univer-h-\[270px\] {
    height: 270px
}
.univer-h-\[30px\] {
    height: 30px
}
.univer-h-\[calc\(100\%-16px\)\] {
    height: calc(100% - 16px)
}
.univer-h-\[calc\(100\%-8px\)\] {
    height: calc(100% - 8px)
}
.univer-h-full {
    height: 100%
}
.univer-h-px {
    height: 1px
}
.univer-max-h-52 {
    max-height: 13rem
}
.univer-max-h-\[100px\] {
    max-height: 100px
}
.univer-max-h-\[360px\] {
    max-height: 360px
}
.\!univer-w-\[90px\] {
    width: 90px !important
}
.univer-w-16 {
    width: 4rem
}
.univer-w-20 {
    width: 5rem
}
.univer-w-24 {
    width: 6rem
}
.univer-w-4 {
    width: 1rem
}
.univer-w-5 {
    width: 1.25rem
}
.univer-w-6 {
    width: 1.5rem
}
.univer-w-60 {
    width: 15rem
}
.univer-w-\[100px\] {
    width: 100px
}
.univer-w-\[130px\] {
    width: 130px
}
.univer-w-\[156px\] {
    width: 156px
}
.univer-w-\[300px\] {
    width: 300px
}
.univer-w-\[50\%\] {
    width: 50%
}
.univer-w-fit {
    width: -moz-fit-content;
    width: fit-content
}
.univer-w-full {
    width: 100%
}
.univer-min-w-0 {
    min-width: 0px
}
.univer-min-w-12 {
    min-width: 3rem
}
.univer-max-w-24 {
    max-width: 6rem
}
.univer-max-w-32 {
    max-width: 8rem
}
.univer-max-w-64 {
    max-width: 16rem
}
.univer-max-w-80 {
    max-width: 20rem
}
.univer-max-w-\[120px\] {
    max-width: 120px
}
.univer-max-w-\[190px\] {
    max-width: 190px
}
.univer-max-w-\[200px\] {
    max-width: 200px
}
.univer-max-w-\[calc\(100\%-112px\)\] {
    max-width: calc(100% - 112px)
}
.univer-flex-1 {
    flex: 1 1 0%
}
.univer-flex-shrink-0 {
    flex-shrink: 0
}
.univer-shrink-0 {
    flex-shrink: 0
}
.univer-flex-grow {
    flex-grow: 1
}
.univer-flex-grow-0 {
    flex-grow: 0
}
.-univer-translate-y-1\/2 {
    --univer-tw-translate-y: -50%;
    transform: translate(var(--univer-tw-translate-x), -50%) rotate(var(--univer-tw-rotate)) skewX(var(--univer-tw-skew-x)) skewY(var(--univer-tw-skew-y)) scaleX(var(--univer-tw-scale-x)) scaleY(var(--univer-tw-scale-y));
    transform: translate(var(--univer-tw-translate-x), var(--univer-tw-translate-y)) rotate(var(--univer-tw-rotate)) skewX(var(--univer-tw-skew-x)) skewY(var(--univer-tw-skew-y)) scaleX(var(--univer-tw-scale-x)) scaleY(var(--univer-tw-scale-y))
}
.univer-rotate-180 {
    --univer-tw-rotate: 180deg;
    transform: translate(var(--univer-tw-translate-x), var(--univer-tw-translate-y)) rotate(180deg) skewX(var(--univer-tw-skew-x)) skewY(var(--univer-tw-skew-y)) scaleX(var(--univer-tw-scale-x)) scaleY(var(--univer-tw-scale-y));
    transform: translate(var(--univer-tw-translate-x), var(--univer-tw-translate-y)) rotate(var(--univer-tw-rotate)) skewX(var(--univer-tw-skew-x)) skewY(var(--univer-tw-skew-y)) scaleX(var(--univer-tw-scale-x)) scaleY(var(--univer-tw-scale-y))
}
@keyframes univer-spin {
    to {
        transform: rotate(360deg)
    }
}
.univer-animate-spin {
    animation: univer-spin 1s linear infinite
}
.univer-cursor-default {
    cursor: default
}
.univer-cursor-not-allowed {
    cursor: not-allowed
}
.univer-cursor-pointer {
    cursor: pointer
}
.univer-select-none {
    -webkit-user-select: none;
       -moz-user-select: none;
            user-select: none
}
.univer-list-none {
    list-style-type: none
}
.univer-appearance-none {
    -webkit-appearance: none;
       -moz-appearance: none;
            appearance: none
}
.univer-grid-flow-col {
    grid-auto-flow: column
}
.univer-grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr))
}
.univer-grid-cols-\[1fr\,auto\,auto\,auto\] {
    grid-template-columns: 1fr auto auto auto
}
.univer-flex-row {
    flex-direction: row
}
.univer-flex-row-reverse {
    flex-direction: row-reverse
}
.univer-flex-col {
    flex-direction: column
}
.univer-flex-nowrap {
    flex-wrap: nowrap
}
.univer-items-center {
    align-items: center
}
.univer-justify-end {
    justify-content: flex-end
}
.univer-justify-center {
    justify-content: center
}
.univer-justify-between {
    justify-content: space-between
}
.univer-gap-1 {
    gap: 0.25rem
}
.univer-gap-2 {
    gap: 0.5rem
}
.univer-gap-x-2 {
    -moz-column-gap: 0.5rem;
         column-gap: 0.5rem
}
.univer-space-y-2 > :not([hidden]) ~ :not([hidden]) {
    --univer-tw-space-y-reverse: 0;
    margin-top: calc(0.5rem * (1 - 0));
    margin-top: calc(0.5rem * (1 - var(--univer-tw-space-y-reverse)));
    margin-top: calc(0.5rem * calc(1 - 0));
    margin-top: calc(0.5rem * calc(1 - var(--univer-tw-space-y-reverse)));
    margin-bottom: calc(0.5rem * 0);
    margin-bottom: calc(0.5rem * var(--univer-tw-space-y-reverse))
}
.univer-divide-x-0 > :not([hidden]) ~ :not([hidden]) {
    --univer-tw-divide-x-reverse: 0;
    border-right-width: calc(0px * 0);
    border-right-width: calc(0px * var(--univer-tw-divide-x-reverse));
    border-left-width: calc(0px * (1 - 0));
    border-left-width: calc(0px * (1 - var(--univer-tw-divide-x-reverse)));
    border-left-width: calc(0px * calc(1 - 0));
    border-left-width: calc(0px * calc(1 - var(--univer-tw-divide-x-reverse)))
}
.univer-divide-y > :not([hidden]) ~ :not([hidden]) {
    --univer-tw-divide-y-reverse: 0;
    border-top-width: calc(1px * (1 - 0));
    border-top-width: calc(1px * (1 - var(--univer-tw-divide-y-reverse)));
    border-top-width: calc(1px * calc(1 - 0));
    border-top-width: calc(1px * calc(1 - var(--univer-tw-divide-y-reverse)));
    border-bottom-width: calc(1px * 0);
    border-bottom-width: calc(1px * var(--univer-tw-divide-y-reverse))
}
.univer-divide-solid > :not([hidden]) ~ :not([hidden]) {
    border-style: solid
}
.univer-divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
    border-color: var(--univer-gray-200)
}
.univer-justify-self-center {
    justify-self: center
}
.univer-overflow-hidden {
    overflow: hidden
}
.univer-overflow-y-auto {
    overflow-y: auto
}
.univer-overflow-x-scroll {
    overflow-x: scroll
}
.univer-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}
.univer-text-ellipsis {
    text-overflow: ellipsis
}
.univer-whitespace-nowrap {
    white-space: nowrap
}
.univer-break-words {
    word-wrap: break-word
}
.univer-rounded {
    border-radius: 0.25rem
}
.univer-rounded-full {
    border-radius: 9999px
}
.univer-rounded-lg {
    border-radius: 0.5rem
}
.univer-rounded-md {
    border-radius: 0.375rem
}
.univer-rounded-sm {
    border-radius: 0.125rem
}
.univer-border-2 {
    border-width: 2px
}
.univer-border-4 {
    border-width: 4px
}
.univer-border-solid {
    border-style: solid
}
.univer-border-none {
    border-style: none
}
.univer-border-gray-100 {
    border-color: var(--univer-gray-100)
}
.univer-border-transparent {
    border-color: transparent
}
.univer-border-r-gray-200 {
    border-right-color: var(--univer-gray-200)
}
.univer-border-t-primary-500 {
    border-top-color: var(--univer-primary-500)
}
.\!univer-bg-gray-600 {
    background-color: var(--univer-gray-600) !important
}
.\!univer-bg-gray-700 {
    background-color: var(--univer-gray-700) !important
}
.\!univer-bg-gray-800 {
    background-color: var(--univer-gray-800) !important
}
.\!univer-bg-gray-900 {
    background-color: var(--univer-gray-900) !important
}
.\!univer-bg-slate-600 {
    --univer-tw-bg-opacity: 1 !important;
    background-color: rgba(71, 85, 105, 1) !important;
    background-color: rgba(71, 85, 105, var(--univer-tw-bg-opacity, 1)) !important
}
.univer-bg-blue-500 {
    background-color: var(--univer-blue-500)
}
.univer-bg-gray-100 {
    background-color: var(--univer-gray-100)
}
.univer-bg-gray-200 {
    background-color: var(--univer-gray-200)
}
.univer-bg-transparent {
    background-color: transparent
}
.univer-bg-white {
    background-color: var(--univer-white)
}
.univer-fill-gray-900 {
    fill: var(--univer-gray-900)
}
.univer-fill-primary-600 {
    fill: var(--univer-primary-600)
}
.univer-p-0 {
    padding: 0px
}
.univer-p-1 {
    padding: 0.25rem
}
.univer-p-1\.5 {
    padding: 0.375rem
}
.univer-p-2 {
    padding: 0.5rem
}
.univer-p-3 {
    padding: 0.75rem
}
.univer-p-4 {
    padding: 1rem
}
.univer-px-1 {
    padding-left: 0.25rem;
    padding-right: 0.25rem
}
.univer-px-1\.5 {
    padding-left: 0.375rem;
    padding-right: 0.375rem
}
.univer-px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem
}
.univer-px-3\.5 {
    padding-left: 0.875rem;
    padding-right: 0.875rem
}
.univer-px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem
}
.univer-py-0\.5 {
    padding-top: 0.125rem;
    padding-bottom: 0.125rem
}
.univer-py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem
}
.univer-py-1\.5 {
    padding-top: 0.375rem;
    padding-bottom: 0.375rem
}
.univer-py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem
}
.univer-py-5 {
    padding-top: 1.25rem;
    padding-bottom: 1.25rem
}
.univer-pb-1 {
    padding-bottom: 0.25rem
}
.univer-pl-3 {
    padding-left: 0.75rem
}
.univer-pl-6 {
    padding-left: 1.5rem
}
.univer-pt-1 {
    padding-top: 0.25rem
}
.univer-pt-2 {
    padding-top: 0.5rem
}
.univer-text-center {
    text-align: center
}
.univer-text-base {
    font-size: 1rem;
    line-height: 1.5rem
}
.univer-text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem
}
.univer-text-xs {
    font-size: 0.75rem;
    line-height: 1rem
}
.univer-font-bold {
    font-weight: 700
}
.univer-font-medium {
    font-weight: 500
}
.univer-font-semibold {
    font-weight: 600
}
.univer-leading-5 {
    line-height: 1.25rem
}
.univer-leading-6 {
    line-height: 1.5rem
}
.univer-leading-7 {
    line-height: 1.75rem
}
.\!univer-text-gray-700 {
    color: var(--univer-gray-700) !important
}
.\!univer-text-white {
    color: var(--univer-white) !important
}
.univer-text-blue-500 {
    color: var(--univer-blue-500)
}
.univer-text-gray-200 {
    color: var(--univer-gray-200)
}
.univer-text-gray-300 {
    color: var(--univer-gray-300)
}
.univer-text-gray-400 {
    color: var(--univer-gray-400)
}
.univer-text-gray-500 {
    color: var(--univer-gray-500)
}
.univer-text-gray-600 {
    color: var(--univer-gray-600)
}
.univer-text-gray-700 {
    color: var(--univer-gray-700)
}
.univer-text-gray-900 {
    color: var(--univer-gray-900)
}
.univer-text-green-600 {
    color: var(--univer-green-600)
}
.univer-text-primary-500 {
    color: var(--univer-primary-500)
}
.univer-text-primary-600 {
    color: var(--univer-primary-600)
}
.univer-text-primary-700 {
    color: var(--univer-primary-700)
}
.univer-text-red-500 {
    color: var(--univer-red-500)
}
.univer-text-red-600 {
    color: var(--univer-red-600)
}
.univer-text-white {
    color: var(--univer-white)
}
.univer-text-yellow-500 {
    color: var(--univer-yellow-500)
}
.univer-shadow {
    --univer-tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    --univer-tw-shadow-colored: 0 1px 3px 0 var(--univer-tw-shadow-color), 0 1px 2px -1px var(--univer-tw-shadow-color);
    box-shadow: 0 0 rgba(0,0,0,0), 0 0 rgba(0,0,0,0), 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    box-shadow: var(--univer-tw-ring-offset-shadow, 0 0 rgba(0,0,0,0)), var(--univer-tw-ring-shadow, 0 0 rgba(0,0,0,0)), var(--univer-tw-shadow)
}
.univer-shadow-lg {
    --univer-tw-shadow: 0px 4px 6px 0px rgba(30, 40, 77, 0.05), 0px 10px 15px -3px rgba(30, 40, 77, 0.10);
    --univer-tw-shadow-colored: 0px 4px 6px 0px var(--univer-tw-shadow-color), 0px 10px 15px -3px var(--univer-tw-shadow-color);
    box-shadow: 0 0 rgba(0,0,0,0), 0 0 rgba(0,0,0,0), 0px 4px 6px 0px rgba(30, 40, 77, 0.05), 0px 10px 15px -3px rgba(30, 40, 77, 0.10);
    box-shadow: var(--univer-tw-ring-offset-shadow, 0 0 rgba(0,0,0,0)), var(--univer-tw-ring-shadow, 0 0 rgba(0,0,0,0)), var(--univer-tw-shadow)
}
.univer-outline-none {
    outline: 2px solid transparent;
    outline-offset: 2px
}
.univer-blur-sm {
    --univer-tw-blur: blur(4px);
    filter: blur(4px) var(--univer-tw-brightness) var(--univer-tw-contrast) var(--univer-tw-grayscale) var(--univer-tw-hue-rotate) var(--univer-tw-invert) var(--univer-tw-saturate) var(--univer-tw-sepia) var(--univer-tw-drop-shadow);
    filter: var(--univer-tw-blur) var(--univer-tw-brightness) var(--univer-tw-contrast) var(--univer-tw-grayscale) var(--univer-tw-hue-rotate) var(--univer-tw-invert) var(--univer-tw-saturate) var(--univer-tw-sepia) var(--univer-tw-drop-shadow)
}
.univer-backdrop-blur {
    --univer-tw-backdrop-blur: blur(8px);
    -webkit-backdrop-filter: blur(8px) var(--univer-tw-backdrop-brightness) var(--univer-tw-backdrop-contrast) var(--univer-tw-backdrop-grayscale) var(--univer-tw-backdrop-hue-rotate) var(--univer-tw-backdrop-invert) var(--univer-tw-backdrop-opacity) var(--univer-tw-backdrop-saturate) var(--univer-tw-backdrop-sepia);
    -webkit-backdrop-filter: var(--univer-tw-backdrop-blur) var(--univer-tw-backdrop-brightness) var(--univer-tw-backdrop-contrast) var(--univer-tw-backdrop-grayscale) var(--univer-tw-backdrop-hue-rotate) var(--univer-tw-backdrop-invert) var(--univer-tw-backdrop-opacity) var(--univer-tw-backdrop-saturate) var(--univer-tw-backdrop-sepia);
    backdrop-filter: blur(8px) var(--univer-tw-backdrop-brightness) var(--univer-tw-backdrop-contrast) var(--univer-tw-backdrop-grayscale) var(--univer-tw-backdrop-hue-rotate) var(--univer-tw-backdrop-invert) var(--univer-tw-backdrop-opacity) var(--univer-tw-backdrop-saturate) var(--univer-tw-backdrop-sepia);
    backdrop-filter: var(--univer-tw-backdrop-blur) var(--univer-tw-backdrop-brightness) var(--univer-tw-backdrop-contrast) var(--univer-tw-backdrop-grayscale) var(--univer-tw-backdrop-hue-rotate) var(--univer-tw-backdrop-invert) var(--univer-tw-backdrop-opacity) var(--univer-tw-backdrop-saturate) var(--univer-tw-backdrop-sepia)
}
.univer-transition-\[colors\,box-shadow\] {
    transition-property: colors,box-shadow;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms
}
.univer-transition-\[height\] {
    transition-property: height;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms
}
.univer-transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms
}
.univer-transition-colors {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms
}
.univer-duration-200 {
    transition-duration: 200ms
}
.univer-ease-linear {
    transition-timing-function: linear
}
.after\:univer-absolute::after {
    content: var(--univer-tw-content);
    position: absolute
}
.after\:univer-right-0::after {
    content: var(--univer-tw-content);
    right: 0px
}
.after\:univer-top-1\/2::after {
    content: var(--univer-tw-content);
    top: 50%
}
.after\:univer-block::after {
    content: var(--univer-tw-content);
    display: block
}
.after\:univer-h-4::after {
    content: var(--univer-tw-content);
    height: 1rem
}
.after\:univer-w-px::after {
    content: var(--univer-tw-content);
    width: 1px
}
.after\:-univer-translate-y-1\/2::after {
    content: var(--univer-tw-content);
    --univer-tw-translate-y: -50%;
    transform: translate(var(--univer-tw-translate-x), -50%) rotate(var(--univer-tw-rotate)) skewX(var(--univer-tw-skew-x)) skewY(var(--univer-tw-skew-y)) scaleX(var(--univer-tw-scale-x)) scaleY(var(--univer-tw-scale-y));
    transform: translate(var(--univer-tw-translate-x), var(--univer-tw-translate-y)) rotate(var(--univer-tw-rotate)) skewX(var(--univer-tw-skew-x)) skewY(var(--univer-tw-skew-y)) scaleX(var(--univer-tw-scale-x)) scaleY(var(--univer-tw-scale-y))
}
.after\:univer-bg-gray-200::after {
    content: var(--univer-tw-content);
    background-color: var(--univer-gray-200)
}
.after\:univer-content-\[\"\"\]::after {
    --univer-tw-content: "";
    content: "";
    content: var(--univer-tw-content)
}
.last\:univer-mb-0:last-child {
    margin-bottom: 0px
}
.hover\:univer-border-primary-600:hover {
    border-color: var(--univer-primary-600)
}
.hover\:univer-bg-gray-100:hover {
    background-color: var(--univer-gray-100)
}
.hover\:univer-bg-gray-200:hover {
    background-color: var(--univer-gray-200)
}
.hover\:univer-bg-gray-50:hover {
    background-color: var(--univer-gray-50)
}
.hover\:univer-bg-transparent:hover {
    background-color: transparent
}
.focus\:univer-outline-none:focus {
    outline: 2px solid transparent;
    outline-offset: 2px
}
.univer-group:hover .group-hover\:univer-flex {
    display: flex
}
.dark\:\!univer-divide-gray-600:where(.univer-dark, .univer-dark *) > :not([hidden]) ~ :not([hidden]) {
    border-color: var(--univer-gray-600) !important
}
.dark\:\!univer-border-r-gray-700:where(.univer-dark, .univer-dark *) {
    border-right-color: var(--univer-gray-700) !important
}
.dark\:\!univer-bg-black:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-black) !important
}
.dark\:\!univer-bg-gray-600:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-600) !important
}
.dark\:\!univer-bg-gray-700:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-700) !important
}
.dark\:\!univer-bg-gray-800:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-800) !important
}
.dark\:\!univer-bg-gray-900:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-900) !important
}
.dark\:\!univer-bg-slate-600:where(.univer-dark, .univer-dark *) {
    --univer-tw-bg-opacity: 1 !important;
    background-color: rgba(71, 85, 105, 1) !important;
    background-color: rgba(71, 85, 105, var(--univer-tw-bg-opacity, 1)) !important
}
.dark\:\!univer-fill-white:where(.univer-dark, .univer-dark *) {
    fill: var(--univer-white) !important
}
.dark\:\!univer-text-gray-200:where(.univer-dark, .univer-dark *) {
    color: var(--univer-gray-200) !important
}
.dark\:\!univer-text-gray-700:where(.univer-dark, .univer-dark *) {
    color: var(--univer-gray-700) !important
}
.dark\:\!univer-text-green-400:where(.univer-dark, .univer-dark *) {
    color: var(--univer-green-400) !important
}
.dark\:\!univer-text-red-400:where(.univer-dark, .univer-dark *) {
    color: var(--univer-red-400) !important
}
.dark\:\!univer-text-white:where(.univer-dark, .univer-dark *) {
    color: var(--univer-white) !important
}
.dark\:hover\:\!univer-bg-gray-600:hover:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-600) !important
}
.dark\:hover\:\!univer-bg-gray-700:hover:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-700) !important
}
.dark\:hover\:\!univer-bg-gray-800:hover:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-800) !important
}
.\[\&\>div\:first-child\]\:univer-px-2\.5>div:first-child {
    padding-left: 0.625rem;
    padding-right: 0.625rem
}
.\[\&\>div\]\:univer-h-5>div {
    height: 1.25rem
}
.\[\&\>div\]\:univer-ring-transparent>div {
    --univer-tw-ring-color: transparent
}
.\[\&_canvas\]\:univer-absolute canvas {
    position: absolute
}
.univer-pointer-events-none {
    pointer-events: none
}
.univer-absolute {
    position: absolute
}
.univer-relative {
    position: relative
}
.univer-inset-0 {
    top: 0px;
    right: 0px;
    bottom: 0px;
    left: 0px
}
.-univer-bottom-0\.5 {
    bottom: -0.125rem
}
.-univer-left-0\.5 {
    left: -0.125rem
}
.-univer-right-0\.5 {
    right: -0.125rem
}
.-univer-top-0\.5 {
    top: -0.125rem
}
.univer-left-0 {
    left: 0px
}
.univer-left-1 {
    left: 0.25rem
}
.univer-right-0 {
    right: 0px
}
.univer-right-5 {
    right: 1.25rem
}
.univer-right-\[60px\] {
    right: 60px
}
.univer-top-0 {
    top: 0px
}
.univer-top-0\.5 {
    top: 0.125rem
}
.univer-top-1\/2 {
    top: 50%
}
.univer-z-10 {
    z-index: 10
}
.univer-z-\[1001\] {
    z-index: 1001
}
.univer-z-\[100\] {
    z-index: 100
}
.univer-m-0 {
    margin: 0px
}
.univer-mx-1 {
    margin-left: 0.25rem;
    margin-right: 0.25rem
}
.univer-my-1 {
    margin-top: 0.25rem;
    margin-bottom: 0.25rem
}
.univer-my-1\.5 {
    margin-top: 0.375rem;
    margin-bottom: 0.375rem
}
.univer-my-2 {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem
}
.univer-mb-1\.5 {
    margin-bottom: 0.375rem
}
.univer-mb-2 {
    margin-bottom: 0.5rem
}
.univer-mb-3 {
    margin-bottom: 0.75rem
}
.univer-mb-4 {
    margin-bottom: 1rem
}
.univer-ml-1 {
    margin-left: 0.25rem
}
.univer-ml-1\.5 {
    margin-left: 0.375rem
}
.univer-ml-3 {
    margin-left: 0.75rem
}
.univer-ml-px {
    margin-left: 1px
}
.univer-mr-1\.5 {
    margin-right: 0.375rem
}
.univer-mr-2 {
    margin-right: 0.5rem
}
.univer-mr-5 {
    margin-right: 1.25rem
}
.univer-mt-1 {
    margin-top: 0.25rem
}
.univer-mt-2 {
    margin-top: 0.5rem
}
.univer-mt-3 {
    margin-top: 0.75rem
}
.univer-mt-4 {
    margin-top: 1rem
}
.univer-mt-auto {
    margin-top: auto
}
.univer-box-border {
    box-sizing: border-box
}
.univer-block {
    display: block
}
.univer-flex {
    display: flex
}
.univer-inline-flex {
    display: inline-flex
}
.univer-grid {
    display: grid
}
.univer-hidden {
    display: none
}
.univer-size-0 {
    width: 0px;
    height: 0px
}
.univer-size-10 {
    width: 2.5rem;
    height: 2.5rem
}
.univer-size-4 {
    width: 1rem;
    height: 1rem
}
.univer-size-6 {
    width: 1.5rem;
    height: 1.5rem
}
.univer-h-0\.5 {
    height: 0.125rem
}
.univer-h-20 {
    height: 5rem
}
.univer-h-4 {
    height: 1rem
}
.univer-h-5 {
    height: 1.25rem
}
.univer-h-6 {
    height: 1.5rem
}
.univer-h-60 {
    height: 15rem
}
.univer-h-7 {
    height: 1.75rem
}
.univer-h-8 {
    height: 2rem
}
.univer-h-9 {
    height: 2.25rem
}
.univer-h-\[270px\] {
    height: 270px
}
.univer-h-\[30px\] {
    height: 30px
}
.univer-h-\[calc\(100\%-16px\)\] {
    height: calc(100% - 16px)
}
.univer-h-\[calc\(100\%-8px\)\] {
    height: calc(100% - 8px)
}
.univer-h-full {
    height: 100%
}
.univer-h-px {
    height: 1px
}
.univer-max-h-52 {
    max-height: 13rem
}
.univer-max-h-\[100px\] {
    max-height: 100px
}
.univer-max-h-\[360px\] {
    max-height: 360px
}
.\!univer-w-\[90px\] {
    width: 90px !important
}
.univer-w-16 {
    width: 4rem
}
.univer-w-20 {
    width: 5rem
}
.univer-w-24 {
    width: 6rem
}
.univer-w-4 {
    width: 1rem
}
.univer-w-5 {
    width: 1.25rem
}
.univer-w-6 {
    width: 1.5rem
}
.univer-w-60 {
    width: 15rem
}
.univer-w-\[100px\] {
    width: 100px
}
.univer-w-\[130px\] {
    width: 130px
}
.univer-w-\[156px\] {
    width: 156px
}
.univer-w-\[300px\] {
    width: 300px
}
.univer-w-\[50\%\] {
    width: 50%
}
.univer-w-fit {
    width: -moz-fit-content;
    width: fit-content
}
.univer-w-full {
    width: 100%
}
.univer-min-w-0 {
    min-width: 0px
}
.univer-min-w-12 {
    min-width: 3rem
}
.univer-max-w-24 {
    max-width: 6rem
}
.univer-max-w-32 {
    max-width: 8rem
}
.univer-max-w-64 {
    max-width: 16rem
}
.univer-max-w-80 {
    max-width: 20rem
}
.univer-max-w-\[120px\] {
    max-width: 120px
}
.univer-max-w-\[190px\] {
    max-width: 190px
}
.univer-max-w-\[200px\] {
    max-width: 200px
}
.univer-max-w-\[calc\(100\%-112px\)\] {
    max-width: calc(100% - 112px)
}
.univer-flex-1 {
    flex: 1 1 0%
}
.univer-flex-shrink-0 {
    flex-shrink: 0
}
.univer-shrink-0 {
    flex-shrink: 0
}
.univer-flex-grow {
    flex-grow: 1
}
.univer-flex-grow-0 {
    flex-grow: 0
}
.-univer-translate-y-1\/2 {
    --univer-tw-translate-y: -50%;
    transform: translate(var(--univer-tw-translate-x), -50%) rotate(var(--univer-tw-rotate)) skewX(var(--univer-tw-skew-x)) skewY(var(--univer-tw-skew-y)) scaleX(var(--univer-tw-scale-x)) scaleY(var(--univer-tw-scale-y));
    transform: translate(var(--univer-tw-translate-x), var(--univer-tw-translate-y)) rotate(var(--univer-tw-rotate)) skewX(var(--univer-tw-skew-x)) skewY(var(--univer-tw-skew-y)) scaleX(var(--univer-tw-scale-x)) scaleY(var(--univer-tw-scale-y))
}
.univer-rotate-180 {
    --univer-tw-rotate: 180deg;
    transform: translate(var(--univer-tw-translate-x), var(--univer-tw-translate-y)) rotate(180deg) skewX(var(--univer-tw-skew-x)) skewY(var(--univer-tw-skew-y)) scaleX(var(--univer-tw-scale-x)) scaleY(var(--univer-tw-scale-y));
    transform: translate(var(--univer-tw-translate-x), var(--univer-tw-translate-y)) rotate(var(--univer-tw-rotate)) skewX(var(--univer-tw-skew-x)) skewY(var(--univer-tw-skew-y)) scaleX(var(--univer-tw-scale-x)) scaleY(var(--univer-tw-scale-y))
}
@keyframes univer-spin {
    to {
        transform: rotate(360deg)
    }
}
.univer-animate-spin {
    animation: univer-spin 1s linear infinite
}
.univer-cursor-default {
    cursor: default
}
.univer-cursor-not-allowed {
    cursor: not-allowed
}
.univer-cursor-pointer {
    cursor: pointer
}
.univer-select-none {
    -webkit-user-select: none;
       -moz-user-select: none;
            user-select: none
}
.univer-list-none {
    list-style-type: none
}
.univer-appearance-none {
    -webkit-appearance: none;
       -moz-appearance: none;
            appearance: none
}
.univer-grid-flow-col {
    grid-auto-flow: column
}
.univer-grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr))
}
.univer-grid-cols-\[1fr\,auto\,auto\,auto\] {
    grid-template-columns: 1fr auto auto auto
}
.univer-flex-row {
    flex-direction: row
}
.univer-flex-row-reverse {
    flex-direction: row-reverse
}
.univer-flex-col {
    flex-direction: column
}
.univer-flex-nowrap {
    flex-wrap: nowrap
}
.univer-items-center {
    align-items: center
}
.univer-justify-end {
    justify-content: flex-end
}
.univer-justify-center {
    justify-content: center
}
.univer-justify-between {
    justify-content: space-between
}
.univer-gap-1 {
    gap: 0.25rem
}
.univer-gap-2 {
    gap: 0.5rem
}
.univer-gap-x-2 {
    -moz-column-gap: 0.5rem;
         column-gap: 0.5rem
}
.univer-space-y-2 > :not([hidden]) ~ :not([hidden]) {
    --univer-tw-space-y-reverse: 0;
    margin-top: calc(0.5rem * (1 - 0));
    margin-top: calc(0.5rem * (1 - var(--univer-tw-space-y-reverse)));
    margin-top: calc(0.5rem * calc(1 - 0));
    margin-top: calc(0.5rem * calc(1 - var(--univer-tw-space-y-reverse)));
    margin-bottom: calc(0.5rem * 0);
    margin-bottom: calc(0.5rem * var(--univer-tw-space-y-reverse))
}
.univer-divide-x-0 > :not([hidden]) ~ :not([hidden]) {
    --univer-tw-divide-x-reverse: 0;
    border-right-width: calc(0px * 0);
    border-right-width: calc(0px * var(--univer-tw-divide-x-reverse));
    border-left-width: calc(0px * (1 - 0));
    border-left-width: calc(0px * (1 - var(--univer-tw-divide-x-reverse)));
    border-left-width: calc(0px * calc(1 - 0));
    border-left-width: calc(0px * calc(1 - var(--univer-tw-divide-x-reverse)))
}
.univer-divide-y > :not([hidden]) ~ :not([hidden]) {
    --univer-tw-divide-y-reverse: 0;
    border-top-width: calc(1px * (1 - 0));
    border-top-width: calc(1px * (1 - var(--univer-tw-divide-y-reverse)));
    border-top-width: calc(1px * calc(1 - 0));
    border-top-width: calc(1px * calc(1 - var(--univer-tw-divide-y-reverse)));
    border-bottom-width: calc(1px * 0);
    border-bottom-width: calc(1px * var(--univer-tw-divide-y-reverse))
}
.univer-divide-solid > :not([hidden]) ~ :not([hidden]) {
    border-style: solid
}
.univer-divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
    border-color: var(--univer-gray-200)
}
.univer-justify-self-center {
    justify-self: center
}
.univer-overflow-hidden {
    overflow: hidden
}
.univer-overflow-y-auto {
    overflow-y: auto
}
.univer-overflow-x-scroll {
    overflow-x: scroll
}
.univer-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}
.univer-text-ellipsis {
    text-overflow: ellipsis
}
.univer-whitespace-nowrap {
    white-space: nowrap
}
.univer-break-words {
    word-wrap: break-word
}
.univer-rounded {
    border-radius: 0.25rem
}
.univer-rounded-full {
    border-radius: 9999px
}
.univer-rounded-lg {
    border-radius: 0.5rem
}
.univer-rounded-md {
    border-radius: 0.375rem
}
.univer-rounded-sm {
    border-radius: 0.125rem
}
.univer-border-2 {
    border-width: 2px
}
.univer-border-4 {
    border-width: 4px
}
.univer-border-solid {
    border-style: solid
}
.univer-border-none {
    border-style: none
}
.univer-border-gray-100 {
    border-color: var(--univer-gray-100)
}
.univer-border-transparent {
    border-color: transparent
}
.univer-border-r-gray-200 {
    border-right-color: var(--univer-gray-200)
}
.univer-border-t-primary-500 {
    border-top-color: var(--univer-primary-500)
}
.\!univer-bg-gray-600 {
    background-color: var(--univer-gray-600) !important
}
.\!univer-bg-gray-700 {
    background-color: var(--univer-gray-700) !important
}
.\!univer-bg-gray-800 {
    background-color: var(--univer-gray-800) !important
}
.\!univer-bg-gray-900 {
    background-color: var(--univer-gray-900) !important
}
.\!univer-bg-slate-600 {
    --univer-tw-bg-opacity: 1 !important;
    background-color: rgba(71, 85, 105, 1) !important;
    background-color: rgba(71, 85, 105, var(--univer-tw-bg-opacity, 1)) !important
}
.univer-bg-blue-500 {
    background-color: var(--univer-blue-500)
}
.univer-bg-gray-100 {
    background-color: var(--univer-gray-100)
}
.univer-bg-gray-200 {
    background-color: var(--univer-gray-200)
}
.univer-bg-transparent {
    background-color: transparent
}
.univer-bg-white {
    background-color: var(--univer-white)
}
.univer-fill-gray-900 {
    fill: var(--univer-gray-900)
}
.univer-fill-primary-600 {
    fill: var(--univer-primary-600)
}
.univer-p-0 {
    padding: 0px
}
.univer-p-1 {
    padding: 0.25rem
}
.univer-p-1\.5 {
    padding: 0.375rem
}
.univer-p-2 {
    padding: 0.5rem
}
.univer-p-3 {
    padding: 0.75rem
}
.univer-p-4 {
    padding: 1rem
}
.univer-px-1 {
    padding-left: 0.25rem;
    padding-right: 0.25rem
}
.univer-px-1\.5 {
    padding-left: 0.375rem;
    padding-right: 0.375rem
}
.univer-px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem
}
.univer-px-3\.5 {
    padding-left: 0.875rem;
    padding-right: 0.875rem
}
.univer-px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem
}
.univer-py-0\.5 {
    padding-top: 0.125rem;
    padding-bottom: 0.125rem
}
.univer-py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem
}
.univer-py-1\.5 {
    padding-top: 0.375rem;
    padding-bottom: 0.375rem
}
.univer-py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem
}
.univer-py-5 {
    padding-top: 1.25rem;
    padding-bottom: 1.25rem
}
.univer-pb-1 {
    padding-bottom: 0.25rem
}
.univer-pl-3 {
    padding-left: 0.75rem
}
.univer-pl-6 {
    padding-left: 1.5rem
}
.univer-pt-1 {
    padding-top: 0.25rem
}
.univer-pt-2 {
    padding-top: 0.5rem
}
.univer-text-center {
    text-align: center
}
.univer-text-base {
    font-size: 1rem;
    line-height: 1.5rem
}
.univer-text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem
}
.univer-text-xs {
    font-size: 0.75rem;
    line-height: 1rem
}
.univer-font-bold {
    font-weight: 700
}
.univer-font-medium {
    font-weight: 500
}
.univer-font-semibold {
    font-weight: 600
}
.univer-leading-5 {
    line-height: 1.25rem
}
.univer-leading-6 {
    line-height: 1.5rem
}
.univer-leading-7 {
    line-height: 1.75rem
}
.\!univer-text-gray-700 {
    color: var(--univer-gray-700) !important
}
.\!univer-text-white {
    color: var(--univer-white) !important
}
.univer-text-blue-500 {
    color: var(--univer-blue-500)
}
.univer-text-gray-200 {
    color: var(--univer-gray-200)
}
.univer-text-gray-300 {
    color: var(--univer-gray-300)
}
.univer-text-gray-400 {
    color: var(--univer-gray-400)
}
.univer-text-gray-500 {
    color: var(--univer-gray-500)
}
.univer-text-gray-600 {
    color: var(--univer-gray-600)
}
.univer-text-gray-700 {
    color: var(--univer-gray-700)
}
.univer-text-gray-900 {
    color: var(--univer-gray-900)
}
.univer-text-green-600 {
    color: var(--univer-green-600)
}
.univer-text-primary-500 {
    color: var(--univer-primary-500)
}
.univer-text-primary-600 {
    color: var(--univer-primary-600)
}
.univer-text-primary-700 {
    color: var(--univer-primary-700)
}
.univer-text-red-500 {
    color: var(--univer-red-500)
}
.univer-text-red-600 {
    color: var(--univer-red-600)
}
.univer-text-white {
    color: var(--univer-white)
}
.univer-text-yellow-500 {
    color: var(--univer-yellow-500)
}
.univer-shadow {
    --univer-tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    --univer-tw-shadow-colored: 0 1px 3px 0 var(--univer-tw-shadow-color), 0 1px 2px -1px var(--univer-tw-shadow-color);
    box-shadow: 0 0 rgba(0,0,0,0), 0 0 rgba(0,0,0,0), 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    box-shadow: var(--univer-tw-ring-offset-shadow, 0 0 rgba(0,0,0,0)), var(--univer-tw-ring-shadow, 0 0 rgba(0,0,0,0)), var(--univer-tw-shadow)
}
.univer-shadow-lg {
    --univer-tw-shadow: 0px 4px 6px 0px rgba(30, 40, 77, 0.05), 0px 10px 15px -3px rgba(30, 40, 77, 0.10);
    --univer-tw-shadow-colored: 0px 4px 6px 0px var(--univer-tw-shadow-color), 0px 10px 15px -3px var(--univer-tw-shadow-color);
    box-shadow: 0 0 rgba(0,0,0,0), 0 0 rgba(0,0,0,0), 0px 4px 6px 0px rgba(30, 40, 77, 0.05), 0px 10px 15px -3px rgba(30, 40, 77, 0.10);
    box-shadow: var(--univer-tw-ring-offset-shadow, 0 0 rgba(0,0,0,0)), var(--univer-tw-ring-shadow, 0 0 rgba(0,0,0,0)), var(--univer-tw-shadow)
}
.univer-outline-none {
    outline: 2px solid transparent;
    outline-offset: 2px
}
.univer-blur-sm {
    --univer-tw-blur: blur(4px);
    filter: blur(4px) var(--univer-tw-brightness) var(--univer-tw-contrast) var(--univer-tw-grayscale) var(--univer-tw-hue-rotate) var(--univer-tw-invert) var(--univer-tw-saturate) var(--univer-tw-sepia) var(--univer-tw-drop-shadow);
    filter: var(--univer-tw-blur) var(--univer-tw-brightness) var(--univer-tw-contrast) var(--univer-tw-grayscale) var(--univer-tw-hue-rotate) var(--univer-tw-invert) var(--univer-tw-saturate) var(--univer-tw-sepia) var(--univer-tw-drop-shadow)
}
.univer-backdrop-blur {
    --univer-tw-backdrop-blur: blur(8px);
    -webkit-backdrop-filter: blur(8px) var(--univer-tw-backdrop-brightness) var(--univer-tw-backdrop-contrast) var(--univer-tw-backdrop-grayscale) var(--univer-tw-backdrop-hue-rotate) var(--univer-tw-backdrop-invert) var(--univer-tw-backdrop-opacity) var(--univer-tw-backdrop-saturate) var(--univer-tw-backdrop-sepia);
    -webkit-backdrop-filter: var(--univer-tw-backdrop-blur) var(--univer-tw-backdrop-brightness) var(--univer-tw-backdrop-contrast) var(--univer-tw-backdrop-grayscale) var(--univer-tw-backdrop-hue-rotate) var(--univer-tw-backdrop-invert) var(--univer-tw-backdrop-opacity) var(--univer-tw-backdrop-saturate) var(--univer-tw-backdrop-sepia);
    backdrop-filter: blur(8px) var(--univer-tw-backdrop-brightness) var(--univer-tw-backdrop-contrast) var(--univer-tw-backdrop-grayscale) var(--univer-tw-backdrop-hue-rotate) var(--univer-tw-backdrop-invert) var(--univer-tw-backdrop-opacity) var(--univer-tw-backdrop-saturate) var(--univer-tw-backdrop-sepia);
    backdrop-filter: var(--univer-tw-backdrop-blur) var(--univer-tw-backdrop-brightness) var(--univer-tw-backdrop-contrast) var(--univer-tw-backdrop-grayscale) var(--univer-tw-backdrop-hue-rotate) var(--univer-tw-backdrop-invert) var(--univer-tw-backdrop-opacity) var(--univer-tw-backdrop-saturate) var(--univer-tw-backdrop-sepia)
}
.univer-transition-\[colors\,box-shadow\] {
    transition-property: colors,box-shadow;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms
}
.univer-transition-\[height\] {
    transition-property: height;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms
}
.univer-transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms
}
.univer-transition-colors {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms
}
.univer-duration-200 {
    transition-duration: 200ms
}
.univer-ease-linear {
    transition-timing-function: linear
}
.after\:univer-absolute::after {
    content: var(--univer-tw-content);
    position: absolute
}
.after\:univer-right-0::after {
    content: var(--univer-tw-content);
    right: 0px
}
.after\:univer-top-1\/2::after {
    content: var(--univer-tw-content);
    top: 50%
}
.after\:univer-block::after {
    content: var(--univer-tw-content);
    display: block
}
.after\:univer-h-4::after {
    content: var(--univer-tw-content);
    height: 1rem
}
.after\:univer-w-px::after {
    content: var(--univer-tw-content);
    width: 1px
}
.after\:-univer-translate-y-1\/2::after {
    content: var(--univer-tw-content);
    --univer-tw-translate-y: -50%;
    transform: translate(var(--univer-tw-translate-x), -50%) rotate(var(--univer-tw-rotate)) skewX(var(--univer-tw-skew-x)) skewY(var(--univer-tw-skew-y)) scaleX(var(--univer-tw-scale-x)) scaleY(var(--univer-tw-scale-y));
    transform: translate(var(--univer-tw-translate-x), var(--univer-tw-translate-y)) rotate(var(--univer-tw-rotate)) skewX(var(--univer-tw-skew-x)) skewY(var(--univer-tw-skew-y)) scaleX(var(--univer-tw-scale-x)) scaleY(var(--univer-tw-scale-y))
}
.after\:univer-bg-gray-200::after {
    content: var(--univer-tw-content);
    background-color: var(--univer-gray-200)
}
.after\:univer-content-\[\"\"\]::after {
    --univer-tw-content: "";
    content: "";
    content: var(--univer-tw-content)
}
.last\:univer-mb-0:last-child {
    margin-bottom: 0px
}
.hover\:univer-border-primary-600:hover {
    border-color: var(--univer-primary-600)
}
.hover\:univer-bg-gray-100:hover {
    background-color: var(--univer-gray-100)
}
.hover\:univer-bg-gray-200:hover {
    background-color: var(--univer-gray-200)
}
.hover\:univer-bg-gray-50:hover {
    background-color: var(--univer-gray-50)
}
.hover\:univer-bg-transparent:hover {
    background-color: transparent
}
.focus\:univer-outline-none:focus {
    outline: 2px solid transparent;
    outline-offset: 2px
}
.univer-group:hover .group-hover\:univer-flex {
    display: flex
}
.dark\:\!univer-divide-gray-600:where(.univer-dark, .univer-dark *) > :not([hidden]) ~ :not([hidden]) {
    border-color: var(--univer-gray-600) !important
}
.dark\:\!univer-border-r-gray-700:where(.univer-dark, .univer-dark *) {
    border-right-color: var(--univer-gray-700) !important
}
.dark\:\!univer-bg-black:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-black) !important
}
.dark\:\!univer-bg-gray-600:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-600) !important
}
.dark\:\!univer-bg-gray-700:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-700) !important
}
.dark\:\!univer-bg-gray-800:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-800) !important
}
.dark\:\!univer-bg-gray-900:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-900) !important
}
.dark\:\!univer-bg-slate-600:where(.univer-dark, .univer-dark *) {
    --univer-tw-bg-opacity: 1 !important;
    background-color: rgba(71, 85, 105, 1) !important;
    background-color: rgba(71, 85, 105, var(--univer-tw-bg-opacity, 1)) !important
}
.dark\:\!univer-fill-white:where(.univer-dark, .univer-dark *) {
    fill: var(--univer-white) !important
}
.dark\:\!univer-text-gray-200:where(.univer-dark, .univer-dark *) {
    color: var(--univer-gray-200) !important
}
.dark\:\!univer-text-gray-700:where(.univer-dark, .univer-dark *) {
    color: var(--univer-gray-700) !important
}
.dark\:\!univer-text-green-400:where(.univer-dark, .univer-dark *) {
    color: var(--univer-green-400) !important
}
.dark\:\!univer-text-red-400:where(.univer-dark, .univer-dark *) {
    color: var(--univer-red-400) !important
}
.dark\:\!univer-text-white:where(.univer-dark, .univer-dark *) {
    color: var(--univer-white) !important
}
.dark\:hover\:\!univer-bg-gray-600:hover:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-600) !important
}
.dark\:hover\:\!univer-bg-gray-700:hover:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-700) !important
}
.dark\:hover\:\!univer-bg-gray-800:hover:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-800) !important
}
.\[\&\>div\:first-child\]\:univer-px-2\.5>div:first-child {
    padding-left: 0.625rem;
    padding-right: 0.625rem
}
.\[\&\>div\]\:univer-h-5>div {
    height: 1.25rem
}
.\[\&\>div\]\:univer-ring-transparent>div {
    --univer-tw-ring-color: transparent
}
.\[\&_canvas\]\:univer-absolute canvas {
    position: absolute
}
*, ::before, ::after {
    --univer-tw-border-spacing-x: 0;
    --univer-tw-border-spacing-y: 0;
    --univer-tw-translate-x: 0;
    --univer-tw-translate-y: 0;
    --univer-tw-rotate: 0;
    --univer-tw-skew-x: 0;
    --univer-tw-skew-y: 0;
    --univer-tw-scale-x: 1;
    --univer-tw-scale-y: 1;
    --univer-tw-pan-x:  ;
    --univer-tw-pan-y:  ;
    --univer-tw-pinch-zoom:  ;
    --univer-tw-scroll-snap-strictness: proximity;
    --univer-tw-gradient-from-position:  ;
    --univer-tw-gradient-via-position:  ;
    --univer-tw-gradient-to-position:  ;
    --univer-tw-ordinal:  ;
    --univer-tw-slashed-zero:  ;
    --univer-tw-numeric-figure:  ;
    --univer-tw-numeric-spacing:  ;
    --univer-tw-numeric-fraction:  ;
    --univer-tw-ring-inset:  ;
    --univer-tw-ring-offset-width: 0px;
    --univer-tw-ring-offset-color: #fff;
    --univer-tw-ring-color: rgba(147, 197, 253, 0.5);
    --univer-tw-ring-offset-shadow: 0 0 rgba(0,0,0,0);
    --univer-tw-ring-shadow: 0 0 rgba(0,0,0,0);
    --univer-tw-shadow: 0 0 rgba(0,0,0,0);
    --univer-tw-shadow-colored: 0 0 rgba(0,0,0,0);
    --univer-tw-blur:  ;
    --univer-tw-brightness:  ;
    --univer-tw-contrast:  ;
    --univer-tw-grayscale:  ;
    --univer-tw-hue-rotate:  ;
    --univer-tw-invert:  ;
    --univer-tw-saturate:  ;
    --univer-tw-sepia:  ;
    --univer-tw-drop-shadow:  ;
    --univer-tw-backdrop-blur:  ;
    --univer-tw-backdrop-brightness:  ;
    --univer-tw-backdrop-contrast:  ;
    --univer-tw-backdrop-grayscale:  ;
    --univer-tw-backdrop-hue-rotate:  ;
    --univer-tw-backdrop-invert:  ;
    --univer-tw-backdrop-opacity:  ;
    --univer-tw-backdrop-saturate:  ;
    --univer-tw-backdrop-sepia:  ;
    --univer-tw-contain-size:  ;
    --univer-tw-contain-layout:  ;
    --univer-tw-contain-paint:  ;
    --univer-tw-contain-style:  
}
::backdrop {
    --univer-tw-border-spacing-x: 0;
    --univer-tw-border-spacing-y: 0;
    --univer-tw-translate-x: 0;
    --univer-tw-translate-y: 0;
    --univer-tw-rotate: 0;
    --univer-tw-skew-x: 0;
    --univer-tw-skew-y: 0;
    --univer-tw-scale-x: 1;
    --univer-tw-scale-y: 1;
    --univer-tw-pan-x:  ;
    --univer-tw-pan-y:  ;
    --univer-tw-pinch-zoom:  ;
    --univer-tw-scroll-snap-strictness: proximity;
    --univer-tw-gradient-from-position:  ;
    --univer-tw-gradient-via-position:  ;
    --univer-tw-gradient-to-position:  ;
    --univer-tw-ordinal:  ;
    --univer-tw-slashed-zero:  ;
    --univer-tw-numeric-figure:  ;
    --univer-tw-numeric-spacing:  ;
    --univer-tw-numeric-fraction:  ;
    --univer-tw-ring-inset:  ;
    --univer-tw-ring-offset-width: 0px;
    --univer-tw-ring-offset-color: #fff;
    --univer-tw-ring-color: rgba(147, 197, 253, 0.5);
    --univer-tw-ring-offset-shadow: 0 0 rgba(0,0,0,0);
    --univer-tw-ring-shadow: 0 0 rgba(0,0,0,0);
    --univer-tw-shadow: 0 0 rgba(0,0,0,0);
    --univer-tw-shadow-colored: 0 0 rgba(0,0,0,0);
    --univer-tw-blur:  ;
    --univer-tw-brightness:  ;
    --univer-tw-contrast:  ;
    --univer-tw-grayscale:  ;
    --univer-tw-hue-rotate:  ;
    --univer-tw-invert:  ;
    --univer-tw-saturate:  ;
    --univer-tw-sepia:  ;
    --univer-tw-drop-shadow:  ;
    --univer-tw-backdrop-blur:  ;
    --univer-tw-backdrop-brightness:  ;
    --univer-tw-backdrop-contrast:  ;
    --univer-tw-backdrop-grayscale:  ;
    --univer-tw-backdrop-hue-rotate:  ;
    --univer-tw-backdrop-invert:  ;
    --univer-tw-backdrop-opacity:  ;
    --univer-tw-backdrop-saturate:  ;
    --univer-tw-backdrop-sepia:  ;
    --univer-tw-contain-size:  ;
    --univer-tw-contain-layout:  ;
    --univer-tw-contain-paint:  ;
    --univer-tw-contain-style:  
}
* {
    scrollbar-color: initial;
    scrollbar-width: initial
}
.univer-pointer-events-none {
    pointer-events: none
}
.univer-absolute {
    position: absolute
}
.univer-relative {
    position: relative
}
.univer-inset-0 {
    top: 0px;
    right: 0px;
    bottom: 0px;
    left: 0px
}
.-univer-bottom-0\.5 {
    bottom: -0.125rem
}
.-univer-left-0\.5 {
    left: -0.125rem
}
.-univer-right-0\.5 {
    right: -0.125rem
}
.-univer-top-0\.5 {
    top: -0.125rem
}
.univer-left-0 {
    left: 0px
}
.univer-left-1 {
    left: 0.25rem
}
.univer-right-0 {
    right: 0px
}
.univer-right-5 {
    right: 1.25rem
}
.univer-right-\[60px\] {
    right: 60px
}
.univer-top-0 {
    top: 0px
}
.univer-top-0\.5 {
    top: 0.125rem
}
.univer-top-1\/2 {
    top: 50%
}
.univer-z-10 {
    z-index: 10
}
.univer-z-\[1001\] {
    z-index: 1001
}
.univer-z-\[100\] {
    z-index: 100
}
.univer-m-0 {
    margin: 0px
}
.univer-mx-1 {
    margin-left: 0.25rem;
    margin-right: 0.25rem
}
.univer-my-1 {
    margin-top: 0.25rem;
    margin-bottom: 0.25rem
}
.univer-my-1\.5 {
    margin-top: 0.375rem;
    margin-bottom: 0.375rem
}
.univer-my-2 {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem
}
.univer-mb-1\.5 {
    margin-bottom: 0.375rem
}
.univer-mb-2 {
    margin-bottom: 0.5rem
}
.univer-mb-3 {
    margin-bottom: 0.75rem
}
.univer-mb-4 {
    margin-bottom: 1rem
}
.univer-ml-1 {
    margin-left: 0.25rem
}
.univer-ml-1\.5 {
    margin-left: 0.375rem
}
.univer-ml-3 {
    margin-left: 0.75rem
}
.univer-ml-px {
    margin-left: 1px
}
.univer-mr-1\.5 {
    margin-right: 0.375rem
}
.univer-mr-2 {
    margin-right: 0.5rem
}
.univer-mr-5 {
    margin-right: 1.25rem
}
.univer-mt-1 {
    margin-top: 0.25rem
}
.univer-mt-2 {
    margin-top: 0.5rem
}
.univer-mt-3 {
    margin-top: 0.75rem
}
.univer-mt-4 {
    margin-top: 1rem
}
.univer-mt-auto {
    margin-top: auto
}
.univer-box-border {
    box-sizing: border-box
}
.univer-block {
    display: block
}
.univer-flex {
    display: flex
}
.univer-inline-flex {
    display: inline-flex
}
.univer-grid {
    display: grid
}
.univer-hidden {
    display: none
}
.univer-size-0 {
    width: 0px;
    height: 0px
}
.univer-size-10 {
    width: 2.5rem;
    height: 2.5rem
}
.univer-size-4 {
    width: 1rem;
    height: 1rem
}
.univer-size-6 {
    width: 1.5rem;
    height: 1.5rem
}
.univer-h-0\.5 {
    height: 0.125rem
}
.univer-h-20 {
    height: 5rem
}
.univer-h-4 {
    height: 1rem
}
.univer-h-5 {
    height: 1.25rem
}
.univer-h-6 {
    height: 1.5rem
}
.univer-h-60 {
    height: 15rem
}
.univer-h-7 {
    height: 1.75rem
}
.univer-h-8 {
    height: 2rem
}
.univer-h-9 {
    height: 2.25rem
}
.univer-h-\[270px\] {
    height: 270px
}
.univer-h-\[30px\] {
    height: 30px
}
.univer-h-\[calc\(100\%-16px\)\] {
    height: calc(100% - 16px)
}
.univer-h-\[calc\(100\%-8px\)\] {
    height: calc(100% - 8px)
}
.univer-h-full {
    height: 100%
}
.univer-h-px {
    height: 1px
}
.univer-max-h-52 {
    max-height: 13rem
}
.univer-max-h-\[100px\] {
    max-height: 100px
}
.univer-max-h-\[360px\] {
    max-height: 360px
}
.\!univer-w-\[90px\] {
    width: 90px !important
}
.univer-w-16 {
    width: 4rem
}
.univer-w-20 {
    width: 5rem
}
.univer-w-24 {
    width: 6rem
}
.univer-w-4 {
    width: 1rem
}
.univer-w-5 {
    width: 1.25rem
}
.univer-w-6 {
    width: 1.5rem
}
.univer-w-60 {
    width: 15rem
}
.univer-w-\[100px\] {
    width: 100px
}
.univer-w-\[130px\] {
    width: 130px
}
.univer-w-\[156px\] {
    width: 156px
}
.univer-w-\[300px\] {
    width: 300px
}
.univer-w-\[50\%\] {
    width: 50%
}
.univer-w-fit {
    width: -moz-fit-content;
    width: fit-content
}
.univer-w-full {
    width: 100%
}
.univer-min-w-0 {
    min-width: 0px
}
.univer-min-w-12 {
    min-width: 3rem
}
.univer-max-w-24 {
    max-width: 6rem
}
.univer-max-w-32 {
    max-width: 8rem
}
.univer-max-w-64 {
    max-width: 16rem
}
.univer-max-w-80 {
    max-width: 20rem
}
.univer-max-w-\[120px\] {
    max-width: 120px
}
.univer-max-w-\[190px\] {
    max-width: 190px
}
.univer-max-w-\[200px\] {
    max-width: 200px
}
.univer-max-w-\[calc\(100\%-112px\)\] {
    max-width: calc(100% - 112px)
}
.univer-flex-1 {
    flex: 1 1 0%
}
.univer-flex-shrink-0 {
    flex-shrink: 0
}
.univer-shrink-0 {
    flex-shrink: 0
}
.univer-flex-grow {
    flex-grow: 1
}
.univer-flex-grow-0 {
    flex-grow: 0
}
.-univer-translate-y-1\/2 {
    --univer-tw-translate-y: -50%;
    transform: translate(var(--univer-tw-translate-x), -50%) rotate(var(--univer-tw-rotate)) skewX(var(--univer-tw-skew-x)) skewY(var(--univer-tw-skew-y)) scaleX(var(--univer-tw-scale-x)) scaleY(var(--univer-tw-scale-y));
    transform: translate(var(--univer-tw-translate-x), var(--univer-tw-translate-y)) rotate(var(--univer-tw-rotate)) skewX(var(--univer-tw-skew-x)) skewY(var(--univer-tw-skew-y)) scaleX(var(--univer-tw-scale-x)) scaleY(var(--univer-tw-scale-y))
}
.univer-rotate-180 {
    --univer-tw-rotate: 180deg;
    transform: translate(var(--univer-tw-translate-x), var(--univer-tw-translate-y)) rotate(180deg) skewX(var(--univer-tw-skew-x)) skewY(var(--univer-tw-skew-y)) scaleX(var(--univer-tw-scale-x)) scaleY(var(--univer-tw-scale-y));
    transform: translate(var(--univer-tw-translate-x), var(--univer-tw-translate-y)) rotate(var(--univer-tw-rotate)) skewX(var(--univer-tw-skew-x)) skewY(var(--univer-tw-skew-y)) scaleX(var(--univer-tw-scale-x)) scaleY(var(--univer-tw-scale-y))
}
@keyframes univer-spin {
    to {
        transform: rotate(360deg)
    }
}
.univer-animate-spin {
    animation: univer-spin 1s linear infinite
}
.univer-cursor-default {
    cursor: default
}
.univer-cursor-not-allowed {
    cursor: not-allowed
}
.univer-cursor-pointer {
    cursor: pointer
}
.univer-select-none {
    -webkit-user-select: none;
       -moz-user-select: none;
            user-select: none
}
.univer-list-none {
    list-style-type: none
}
.univer-appearance-none {
    -webkit-appearance: none;
       -moz-appearance: none;
            appearance: none
}
.univer-grid-flow-col {
    grid-auto-flow: column
}
.univer-grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr))
}
.univer-grid-cols-\[1fr\,auto\,auto\,auto\] {
    grid-template-columns: 1fr auto auto auto
}
.univer-flex-row {
    flex-direction: row
}
.univer-flex-row-reverse {
    flex-direction: row-reverse
}
.univer-flex-col {
    flex-direction: column
}
.univer-flex-nowrap {
    flex-wrap: nowrap
}
.univer-items-center {
    align-items: center
}
.univer-justify-end {
    justify-content: flex-end
}
.univer-justify-center {
    justify-content: center
}
.univer-justify-between {
    justify-content: space-between
}
.univer-gap-1 {
    gap: 0.25rem
}
.univer-gap-2 {
    gap: 0.5rem
}
.univer-gap-x-2 {
    -moz-column-gap: 0.5rem;
         column-gap: 0.5rem
}
.univer-space-y-2 > :not([hidden]) ~ :not([hidden]) {
    --univer-tw-space-y-reverse: 0;
    margin-top: calc(0.5rem * (1 - 0));
    margin-top: calc(0.5rem * (1 - var(--univer-tw-space-y-reverse)));
    margin-top: calc(0.5rem * calc(1 - 0));
    margin-top: calc(0.5rem * calc(1 - var(--univer-tw-space-y-reverse)));
    margin-bottom: calc(0.5rem * 0);
    margin-bottom: calc(0.5rem * var(--univer-tw-space-y-reverse))
}
.univer-divide-x-0 > :not([hidden]) ~ :not([hidden]) {
    --univer-tw-divide-x-reverse: 0;
    border-right-width: calc(0px * 0);
    border-right-width: calc(0px * var(--univer-tw-divide-x-reverse));
    border-left-width: calc(0px * (1 - 0));
    border-left-width: calc(0px * (1 - var(--univer-tw-divide-x-reverse)));
    border-left-width: calc(0px * calc(1 - 0));
    border-left-width: calc(0px * calc(1 - var(--univer-tw-divide-x-reverse)))
}
.univer-divide-y > :not([hidden]) ~ :not([hidden]) {
    --univer-tw-divide-y-reverse: 0;
    border-top-width: calc(1px * (1 - 0));
    border-top-width: calc(1px * (1 - var(--univer-tw-divide-y-reverse)));
    border-top-width: calc(1px * calc(1 - 0));
    border-top-width: calc(1px * calc(1 - var(--univer-tw-divide-y-reverse)));
    border-bottom-width: calc(1px * 0);
    border-bottom-width: calc(1px * var(--univer-tw-divide-y-reverse))
}
.univer-divide-solid > :not([hidden]) ~ :not([hidden]) {
    border-style: solid
}
.univer-divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
    border-color: var(--univer-gray-200)
}
.univer-justify-self-center {
    justify-self: center
}
.univer-overflow-hidden {
    overflow: hidden
}
.univer-overflow-y-auto {
    overflow-y: auto
}
.univer-overflow-x-scroll {
    overflow-x: scroll
}
.univer-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}
.univer-text-ellipsis {
    text-overflow: ellipsis
}
.univer-whitespace-nowrap {
    white-space: nowrap
}
.univer-break-words {
    word-wrap: break-word
}
.univer-rounded {
    border-radius: 0.25rem
}
.univer-rounded-full {
    border-radius: 9999px
}
.univer-rounded-lg {
    border-radius: 0.5rem
}
.univer-rounded-md {
    border-radius: 0.375rem
}
.univer-rounded-sm {
    border-radius: 0.125rem
}
.univer-border-2 {
    border-width: 2px
}
.univer-border-4 {
    border-width: 4px
}
.univer-border-solid {
    border-style: solid
}
.univer-border-none {
    border-style: none
}
.univer-border-gray-100 {
    border-color: var(--univer-gray-100)
}
.univer-border-transparent {
    border-color: transparent
}
.univer-border-r-gray-200 {
    border-right-color: var(--univer-gray-200)
}
.univer-border-t-primary-500 {
    border-top-color: var(--univer-primary-500)
}
.\!univer-bg-gray-600 {
    background-color: var(--univer-gray-600) !important
}
.\!univer-bg-gray-700 {
    background-color: var(--univer-gray-700) !important
}
.\!univer-bg-gray-800 {
    background-color: var(--univer-gray-800) !important
}
.\!univer-bg-gray-900 {
    background-color: var(--univer-gray-900) !important
}
.\!univer-bg-slate-600 {
    --univer-tw-bg-opacity: 1 !important;
    background-color: rgba(71, 85, 105, 1) !important;
    background-color: rgba(71, 85, 105, var(--univer-tw-bg-opacity, 1)) !important
}
.univer-bg-blue-500 {
    background-color: var(--univer-blue-500)
}
.univer-bg-gray-100 {
    background-color: var(--univer-gray-100)
}
.univer-bg-gray-200 {
    background-color: var(--univer-gray-200)
}
.univer-bg-transparent {
    background-color: transparent
}
.univer-bg-white {
    background-color: var(--univer-white)
}
.univer-fill-gray-900 {
    fill: var(--univer-gray-900)
}
.univer-fill-primary-600 {
    fill: var(--univer-primary-600)
}
.univer-p-0 {
    padding: 0px
}
.univer-p-1 {
    padding: 0.25rem
}
.univer-p-1\.5 {
    padding: 0.375rem
}
.univer-p-2 {
    padding: 0.5rem
}
.univer-p-3 {
    padding: 0.75rem
}
.univer-p-4 {
    padding: 1rem
}
.univer-px-1 {
    padding-left: 0.25rem;
    padding-right: 0.25rem
}
.univer-px-1\.5 {
    padding-left: 0.375rem;
    padding-right: 0.375rem
}
.univer-px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem
}
.univer-px-3\.5 {
    padding-left: 0.875rem;
    padding-right: 0.875rem
}
.univer-px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem
}
.univer-py-0\.5 {
    padding-top: 0.125rem;
    padding-bottom: 0.125rem
}
.univer-py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem
}
.univer-py-1\.5 {
    padding-top: 0.375rem;
    padding-bottom: 0.375rem
}
.univer-py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem
}
.univer-py-5 {
    padding-top: 1.25rem;
    padding-bottom: 1.25rem
}
.univer-pb-1 {
    padding-bottom: 0.25rem
}
.univer-pl-3 {
    padding-left: 0.75rem
}
.univer-pl-6 {
    padding-left: 1.5rem
}
.univer-pt-1 {
    padding-top: 0.25rem
}
.univer-pt-2 {
    padding-top: 0.5rem
}
.univer-text-center {
    text-align: center
}
.univer-text-base {
    font-size: 1rem;
    line-height: 1.5rem
}
.univer-text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem
}
.univer-text-xs {
    font-size: 0.75rem;
    line-height: 1rem
}
.univer-font-bold {
    font-weight: 700
}
.univer-font-medium {
    font-weight: 500
}
.univer-font-semibold {
    font-weight: 600
}
.univer-leading-5 {
    line-height: 1.25rem
}
.univer-leading-6 {
    line-height: 1.5rem
}
.univer-leading-7 {
    line-height: 1.75rem
}
.\!univer-text-gray-700 {
    color: var(--univer-gray-700) !important
}
.\!univer-text-white {
    color: var(--univer-white) !important
}
.univer-text-blue-500 {
    color: var(--univer-blue-500)
}
.univer-text-gray-200 {
    color: var(--univer-gray-200)
}
.univer-text-gray-300 {
    color: var(--univer-gray-300)
}
.univer-text-gray-400 {
    color: var(--univer-gray-400)
}
.univer-text-gray-500 {
    color: var(--univer-gray-500)
}
.univer-text-gray-600 {
    color: var(--univer-gray-600)
}
.univer-text-gray-700 {
    color: var(--univer-gray-700)
}
.univer-text-gray-900 {
    color: var(--univer-gray-900)
}
.univer-text-green-600 {
    color: var(--univer-green-600)
}
.univer-text-primary-500 {
    color: var(--univer-primary-500)
}
.univer-text-primary-600 {
    color: var(--univer-primary-600)
}
.univer-text-primary-700 {
    color: var(--univer-primary-700)
}
.univer-text-red-500 {
    color: var(--univer-red-500)
}
.univer-text-red-600 {
    color: var(--univer-red-600)
}
.univer-text-white {
    color: var(--univer-white)
}
.univer-text-yellow-500 {
    color: var(--univer-yellow-500)
}
.univer-shadow {
    --univer-tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    --univer-tw-shadow-colored: 0 1px 3px 0 var(--univer-tw-shadow-color), 0 1px 2px -1px var(--univer-tw-shadow-color);
    box-shadow: 0 0 rgba(0,0,0,0), 0 0 rgba(0,0,0,0), 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    box-shadow: var(--univer-tw-ring-offset-shadow, 0 0 rgba(0,0,0,0)), var(--univer-tw-ring-shadow, 0 0 rgba(0,0,0,0)), var(--univer-tw-shadow)
}
.univer-shadow-lg {
    --univer-tw-shadow: 0px 4px 6px 0px rgba(30, 40, 77, 0.05), 0px 10px 15px -3px rgba(30, 40, 77, 0.10);
    --univer-tw-shadow-colored: 0px 4px 6px 0px var(--univer-tw-shadow-color), 0px 10px 15px -3px var(--univer-tw-shadow-color);
    box-shadow: 0 0 rgba(0,0,0,0), 0 0 rgba(0,0,0,0), 0px 4px 6px 0px rgba(30, 40, 77, 0.05), 0px 10px 15px -3px rgba(30, 40, 77, 0.10);
    box-shadow: var(--univer-tw-ring-offset-shadow, 0 0 rgba(0,0,0,0)), var(--univer-tw-ring-shadow, 0 0 rgba(0,0,0,0)), var(--univer-tw-shadow)
}
.univer-outline-none {
    outline: 2px solid transparent;
    outline-offset: 2px
}
.univer-blur-sm {
    --univer-tw-blur: blur(4px);
    filter: blur(4px) var(--univer-tw-brightness) var(--univer-tw-contrast) var(--univer-tw-grayscale) var(--univer-tw-hue-rotate) var(--univer-tw-invert) var(--univer-tw-saturate) var(--univer-tw-sepia) var(--univer-tw-drop-shadow);
    filter: var(--univer-tw-blur) var(--univer-tw-brightness) var(--univer-tw-contrast) var(--univer-tw-grayscale) var(--univer-tw-hue-rotate) var(--univer-tw-invert) var(--univer-tw-saturate) var(--univer-tw-sepia) var(--univer-tw-drop-shadow)
}
.univer-backdrop-blur {
    --univer-tw-backdrop-blur: blur(8px);
    -webkit-backdrop-filter: blur(8px) var(--univer-tw-backdrop-brightness) var(--univer-tw-backdrop-contrast) var(--univer-tw-backdrop-grayscale) var(--univer-tw-backdrop-hue-rotate) var(--univer-tw-backdrop-invert) var(--univer-tw-backdrop-opacity) var(--univer-tw-backdrop-saturate) var(--univer-tw-backdrop-sepia);
    -webkit-backdrop-filter: var(--univer-tw-backdrop-blur) var(--univer-tw-backdrop-brightness) var(--univer-tw-backdrop-contrast) var(--univer-tw-backdrop-grayscale) var(--univer-tw-backdrop-hue-rotate) var(--univer-tw-backdrop-invert) var(--univer-tw-backdrop-opacity) var(--univer-tw-backdrop-saturate) var(--univer-tw-backdrop-sepia);
    backdrop-filter: blur(8px) var(--univer-tw-backdrop-brightness) var(--univer-tw-backdrop-contrast) var(--univer-tw-backdrop-grayscale) var(--univer-tw-backdrop-hue-rotate) var(--univer-tw-backdrop-invert) var(--univer-tw-backdrop-opacity) var(--univer-tw-backdrop-saturate) var(--univer-tw-backdrop-sepia);
    backdrop-filter: var(--univer-tw-backdrop-blur) var(--univer-tw-backdrop-brightness) var(--univer-tw-backdrop-contrast) var(--univer-tw-backdrop-grayscale) var(--univer-tw-backdrop-hue-rotate) var(--univer-tw-backdrop-invert) var(--univer-tw-backdrop-opacity) var(--univer-tw-backdrop-saturate) var(--univer-tw-backdrop-sepia)
}
.univer-transition-\[colors\,box-shadow\] {
    transition-property: colors,box-shadow;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms
}
.univer-transition-\[height\] {
    transition-property: height;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms
}
.univer-transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms
}
.univer-transition-colors {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms
}
.univer-duration-200 {
    transition-duration: 200ms
}
.univer-ease-linear {
    transition-timing-function: linear
}
.after\:univer-absolute::after {
    content: var(--univer-tw-content);
    position: absolute
}
.after\:univer-right-0::after {
    content: var(--univer-tw-content);
    right: 0px
}
.after\:univer-top-1\/2::after {
    content: var(--univer-tw-content);
    top: 50%
}
.after\:univer-block::after {
    content: var(--univer-tw-content);
    display: block
}
.after\:univer-h-4::after {
    content: var(--univer-tw-content);
    height: 1rem
}
.after\:univer-w-px::after {
    content: var(--univer-tw-content);
    width: 1px
}
.after\:-univer-translate-y-1\/2::after {
    content: var(--univer-tw-content);
    --univer-tw-translate-y: -50%;
    transform: translate(var(--univer-tw-translate-x), -50%) rotate(var(--univer-tw-rotate)) skewX(var(--univer-tw-skew-x)) skewY(var(--univer-tw-skew-y)) scaleX(var(--univer-tw-scale-x)) scaleY(var(--univer-tw-scale-y));
    transform: translate(var(--univer-tw-translate-x), var(--univer-tw-translate-y)) rotate(var(--univer-tw-rotate)) skewX(var(--univer-tw-skew-x)) skewY(var(--univer-tw-skew-y)) scaleX(var(--univer-tw-scale-x)) scaleY(var(--univer-tw-scale-y))
}
.after\:univer-bg-gray-200::after {
    content: var(--univer-tw-content);
    background-color: var(--univer-gray-200)
}
.after\:univer-content-\[\"\"\]::after {
    --univer-tw-content: "";
    content: "";
    content: var(--univer-tw-content)
}
.last\:univer-mb-0:last-child {
    margin-bottom: 0px
}
.hover\:univer-border-primary-600:hover {
    border-color: var(--univer-primary-600)
}
.hover\:univer-bg-gray-100:hover {
    background-color: var(--univer-gray-100)
}
.hover\:univer-bg-gray-200:hover {
    background-color: var(--univer-gray-200)
}
.hover\:univer-bg-gray-50:hover {
    background-color: var(--univer-gray-50)
}
.hover\:univer-bg-transparent:hover {
    background-color: transparent
}
.focus\:univer-outline-none:focus {
    outline: 2px solid transparent;
    outline-offset: 2px
}
.univer-group:hover .group-hover\:univer-flex {
    display: flex
}
.dark\:\!univer-divide-gray-600:where(.univer-dark, .univer-dark *) > :not([hidden]) ~ :not([hidden]) {
    border-color: var(--univer-gray-600) !important
}
.dark\:\!univer-border-r-gray-700:where(.univer-dark, .univer-dark *) {
    border-right-color: var(--univer-gray-700) !important
}
.dark\:\!univer-bg-black:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-black) !important
}
.dark\:\!univer-bg-gray-600:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-600) !important
}
.dark\:\!univer-bg-gray-700:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-700) !important
}
.dark\:\!univer-bg-gray-800:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-800) !important
}
.dark\:\!univer-bg-gray-900:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-900) !important
}
.dark\:\!univer-bg-slate-600:where(.univer-dark, .univer-dark *) {
    --univer-tw-bg-opacity: 1 !important;
    background-color: rgba(71, 85, 105, 1) !important;
    background-color: rgba(71, 85, 105, var(--univer-tw-bg-opacity, 1)) !important
}
.dark\:\!univer-fill-white:where(.univer-dark, .univer-dark *) {
    fill: var(--univer-white) !important
}
.dark\:\!univer-text-gray-200:where(.univer-dark, .univer-dark *) {
    color: var(--univer-gray-200) !important
}
.dark\:\!univer-text-gray-700:where(.univer-dark, .univer-dark *) {
    color: var(--univer-gray-700) !important
}
.dark\:\!univer-text-green-400:where(.univer-dark, .univer-dark *) {
    color: var(--univer-green-400) !important
}
.dark\:\!univer-text-red-400:where(.univer-dark, .univer-dark *) {
    color: var(--univer-red-400) !important
}
.dark\:\!univer-text-white:where(.univer-dark, .univer-dark *) {
    color: var(--univer-white) !important
}
.dark\:hover\:\!univer-bg-gray-600:hover:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-600) !important
}
.dark\:hover\:\!univer-bg-gray-700:hover:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-700) !important
}
.dark\:hover\:\!univer-bg-gray-800:hover:where(.univer-dark, .univer-dark *) {
    background-color: var(--univer-gray-800) !important
}
.\[\&\>div\:first-child\]\:univer-px-2\.5>div:first-child {
    padding-left: 0.625rem;
    padding-right: 0.625rem
}
.\[\&\>div\]\:univer-h-5>div {
    height: 1.25rem
}
.\[\&\>div\]\:univer-ring-transparent>div {
    --univer-tw-ring-color: transparent
}
.\[\&_canvas\]\:univer-absolute canvas {
    position: absolute
}
.univer-date-picker {
    position: relative;
    width: 200px;
    height: 32px;
    border-radius: 4px;
    box-sizing: border-box;
    display: inline-flex;
    border-width: 1px;
    border-style: solid;
    border-color: var(--univer-gray-200);
    background-color: var(--univer-white);
    color: var(--univer-gray-900);
}
.univer-dark .univer-date-picker {
    border-color: var(--univer-gray-600);
    background-color: var(--univer-gray-900);
    color: var(--univer-white);
}

.univer-date-picker-rtl {
    direction: rtl;
}

.univer-date-picker-invalid {
    box-shadow: 0 0 2px red;
}

.univer-date-picker-panel {
    display: inline-block;
    vertical-align: top;
}

.univer-date-picker-panel-focused {
    border-color: blue;
}

.univer-date-picker-panel-rtl {
    direction: rtl;
}

.univer-date-picker-suffix-icon {
    position: absolute;
    top: 50%;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    padding: 0 8px;
    transform: translateY(-50%);
    cursor: pointer;
    color: var(--univer-gray-600);
}
.univer-dark .univer-date-picker-suffix-icon {
    color: var(--univer-gray-200);
}

.univer-date-picker-decade-panel,
.univer-date-picker-year-panel,
.univer-date-picker-month-panel,
.univer-date-picker-week-panel,
.univer-date-picker-date-panel,
.univer-date-picker-time-panel {
    display: flex;
    flex-direction: column;
}

.univer-date-picker-decade-panel table,
.univer-date-picker-year-panel table,
.univer-date-picker-month-panel table,
.univer-date-picker-week-panel table,
.univer-date-picker-date-panel table,
.univer-date-picker-time-panel table {
    text-align: center;
    border-collapse: collapse;
}

/* Header */
.univer-date-picker-header {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
}

.univer-date-picker-header > * {
    flex: none;
}

.univer-date-picker-header-view {
    height: 16px;
    flex: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
}

.univer-date-picker-header-view > button {
    padding: 0;
    border: 0;
    background-color: transparent;
    cursor: pointer;
    color: var(--univer-gray-900);
}
.univer-dark .univer-date-picker-header-view > button {
    color: var(--univer-white);
}

.univer-date-picker-header-super-prev-btn,
.univer-date-picker-header-super-next-btn,
.univer-date-picker-header-prev-btn,
.univer-date-picker-header-next-btn {
    width: 16px;
    height: 16px;
    padding: 0;
    line-height: 16px;
    font-size: 16px;
    border: none;
    background-color: transparent;
    cursor: pointer;
    color: var(--univer-gray-600);
}
.univer-dark .univer-date-picker-header-super-prev-btn,
.univer-dark .univer-date-picker-header-super-next-btn,
.univer-dark .univer-date-picker-header-prev-btn,
.univer-dark .univer-date-picker-header-next-btn {
    color: var(--univer-gray-200);
}

/* Content */
.univer-date-picker-cell {
    color: var(--univer-gray-600);
}
.univer-dark .univer-date-picker-cell {
    color: var(--univer-gray-200);
}

.univer-date-picker-cell-disabled {
    opacity: 0.2;
}

.univer-date-picker-cell-inner {
    display: inline-block;
    box-sizing: border-box;
    width: 100%;
    height: 24px;
    margin: 0;
    padding: 0;
    font-size: 12px;
    line-height: 24px;
    background: transparent;
    border: 0;
    border-radius: 4px;
    outline: none;
    cursor: pointer;
    transition: background 0.3s;
    padding-left: 0.375rem;
    padding-right: 0.375rem;
}

.univer-date-picker-cell-in-view {
    color: #333;
}

.univer-date-picker-cell-in-range > .univer-date-picker-cell-inner {
    background: rgba(0, 0, 255, 0.05);
}

.univer-date-picker-cell-hover > .univer-date-picker-cell-inner:hover {
    background-color: var(--univer-gray-100);
}

.univer-date-picker-cell-range-hover-start::after,
.univer-date-picker-cell-range-hover-end::after,
.univer-date-picker-cell-range-hover::after {
    position: absolute;
    top: 3px;
    right: 0;
    bottom: 0;
    left: 0;
    border: 1px solid green;
    border-right: 0;
    border-left: 0;
    content: '';
    pointer-events: none;
}

.univer-date-picker-cell-range-hover-start::after {
    border-left: 1px solid green !important;
}

.univer-date-picker-cell-range-hover-end::after {
    border-right: 1px solid green !important;
}

.univer-date-picker-cell-today > .univer-date-picker-cell-inner {
    border-width: 1px;
    border-style: solid;
    border-color: var(--univer-primary-600);
}

.univer-date-picker-cell-range-start > .univer-date-picker-cell-inner,
.univer-date-picker-cell-range-end > .univer-date-picker-cell-inner,
.univer-date-picker-cell-selected > .univer-date-picker-cell-inner {
    color: #fff;
    background-color: var(--univer-primary-600);
}

/* Preset */
.univer-date-picker-presets {
    background: #ccccff;
}

.univer-date-picker-presets ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

/* Now Button */
.univer-date-picker-now {
    padding-top: 12px;
}

.univer-date-picker-now-btn {
    font-size: 13px;
    cursor: pointer;
    color: var(--univer-primary-600);
}

/* Footer */
.univer-date-picker-footer,
.univer-date-picker-picker-footer {
    margin-top: 12px;
    border-width: 0px;
    border-top-width: 1px;
    border-style: solid;
    border-top-color: var(--univer-gray-200);
}

.univer-date-picker-ranges {
    margin: 0px;
    display: flex;
    list-style-type: none;
    align-items: center;
    justify-content: center;
    padding: 0px;
}

/* Clear Button */
.univer-date-picker-clear {
    position: absolute;
    top: 7px;
    right: 8px;
    cursor: pointer;
}

.univer-date-picker-clear-btn {
    width: 16px;
    height: 16px;
    text-align: center;
    line-height: 16px;
    border-radius: 50%;
    display: block;
    background-color: var(--univer-gray-500);
}

.univer-date-picker-clear-btn::before {
    content: '×';
    position: absolute;
    top: -1px;
    left: 0;
    right: 0;
    bottom: 0;
    color: white;
    font-size: 16px;
    line-height: 16px;
}

/* Dropdown */
.univer-date-picker-dropdown {
    position: absolute;
    pointer-events: none;
}

.univer-date-picker-dropdown-range {
    padding: 10px 0;
}

.univer-date-picker-dropdown-hidden {
    display: none;
}

/* Range Picker */
.univer-date-picker-range {
    position: relative;
    display: inline-flex;
}

.univer-date-picker-range-wrapper {
    display: flex;
}

.univer-date-picker-active-bar {
    bottom: 0;
    height: 3px;
    background: green;
    opacity: 0;
    transition: all 0.3s;
    pointer-events: none;
}

.univer-date-picker-focused .univer-date-picker-active-bar {
    opacity: 1;
}

/* Panel Container */
.univer-date-picker-panel-container {
    display: inline-block;
    vertical-align: top;
    transition: margin 0.3s;
    pointer-events: all;
}

.univer-date-picker-panel-layout {
    display: flex;
    flex-wrap: nowrap;
    align-items: stretch;
    overflow: hidden;
    border-radius: 0.25rem;
    border-width: 1px;
    border-style: solid;
    border-color: var(--univer-gray-200);
    background-color: var(--univer-white);
    padding: 1rem;
    --univer-tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    --univer-tw-shadow-colored: 0 1px 3px 0 var(--univer-tw-shadow-color), 0 1px 2px -1px var(--univer-tw-shadow-color);
    box-shadow: 0 0 rgba(0,0,0,0), 0 0 rgba(0,0,0,0), 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    box-shadow: var(--univer-tw-ring-offset-shadow, 0 0 rgba(0,0,0,0)), var(--univer-tw-ring-shadow, 0 0 rgba(0,0,0,0)), var(--univer-tw-shadow);
}
.univer-dark .univer-date-picker-panel-layout {
    border-color: var(--univer-gray-600);
    background-color: var(--univer-gray-900);
}

/* Input */
.univer-date-picker-input {
    position: relative;
    display: inline-flex;
    width: 100%;
}

.univer-date-picker-input > input {
    width: 100%;
    padding-left: 12px;
    border: none;
    border-radius: 4px;
    box-sizing: border-box;
    background-color: transparent;
}

.univer-date-picker-input > input:focus {
    outline: none;
}
.univer-date-range-picker {
    position: relative;
    width: 280px;
    height: 32px;
    border-radius: 4px;
    box-sizing: border-box;
    display: inline-flex;
    border-width: 1px;
    border-style: solid;
    border-color: var(--univer-gray-200);
    color: var(--univer-gray-900);
}
.univer-dark .univer-date-range-picker {
    border-color: var(--univer-gray-600);
    background-color: var(--univer-gray-900);
    color: var(--univer-white);
}

.univer-date-range-picker-rtl {
    direction: rtl;
}

.univer-date-range-picker-invalid {
    box-shadow: 0 0 2px red;
}

.univer-date-range-picker-panel {
    display: inline-block;
    vertical-align: top;
}

.univer-date-range-picker-panel-focused {
    border-color: blue;
}

.univer-date-range-picker-panel-rtl {
    direction: rtl;
}

.univer-date-range-picker-suffix-icon {
    position: absolute;
    top: 50%;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    padding: 0 8px;
    transform: translateY(-50%);
    cursor: pointer;
    color: var(--univer-gray-600);
}

/* Shared Panels */
.univer-date-range-picker-decade-panel,
.univer-date-range-picker-year-panel,
.univer-date-range-picker-month-panel,
.univer-date-range-picker-week-panel,
.univer-date-range-picker-date-panel,
.univer-date-range-picker-time-panel {
    display: flex;
    flex-direction: column;
}

.univer-date-range-picker-decade-panel table,
.univer-date-range-picker-year-panel table,
.univer-date-range-picker-month-panel table,
.univer-date-range-picker-week-panel table,
.univer-date-range-picker-date-panel table,
.univer-date-range-picker-time-panel table {
    text-align: center;
    border-collapse: collapse;
}

/* Header */
.univer-date-range-picker-header {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
}

.univer-date-range-picker-header > * {
    flex: none;
}

.univer-date-range-picker-header-view {
    height: 16px;
    flex: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
}

.univer-date-range-picker-header-view > button {
    padding: 0;
    border: 0;
    background-color: transparent;
    cursor: pointer;
}

.univer-date-range-picker-header-super-prev-btn,
.univer-date-range-picker-header-super-next-btn,
.univer-date-range-picker-header-prev-btn,
.univer-date-range-picker-header-next-btn {
    width: 16px;
    height: 16px;
    padding: 0;
    line-height: 16px;
    font-size: 16px;
    border: none;
    background-color: transparent;
    cursor: pointer;
    color: var(--univer-gray-600);
}
.univer-dark .univer-date-range-picker-header-super-prev-btn,
.univer-dark .univer-date-range-picker-header-super-next-btn,
.univer-dark .univer-date-range-picker-header-prev-btn,
.univer-dark .univer-date-range-picker-header-next-btn {
    color: var(--univer-gray-400);
}

/* Content */
.univer-date-range-picker-cell {
    color: var(--univer-gray-600);
}
.univer-dark .univer-date-range-picker-cell {
    color: var(--univer-gray-400);
}

.univer-date-range-picker-cell-disabled {
    opacity: 0.2;
}

.univer-date-range-picker-cell-inner {
    display: inline-block;
    box-sizing: border-box;
    width: 100%;
    height: 24px;
    margin: 0;
    padding: 0;
    font-size: 12px;
    line-height: 24px;
    background: transparent;
    border: 0;
    border-radius: 4px;
    outline: none;
    cursor: pointer;
    transition: background 0.3s;
}

.univer-date-range-picker-cell-in-view {
    color: #333;
}

.univer-date-range-picker-cell-in-range > .univer-date-range-picker-cell-inner {
    background: rgba(0, 0, 255, 0.05);
}

.univer-date-range-picker-cell-hover > .univer-date-range-picker-cell-inner {
    background-color: var(--univer-gray-100);
}

.univer-date-range-picker-cell-range-hover-start::after,
.univer-date-range-picker-cell-range-hover-end::after,
.univer-date-range-picker-cell-range-hover::after {
    position: absolute;
    top: 3px;
    right: 0;
    bottom: 0;
    left: 0;
    border: 1px solid green;
    border-right: 0;
    border-left: 0;
    content: '';
    pointer-events: none;
}

.univer-date-range-picker-cell-range-hover-start::after {
    border-left: 1px solid green !important;
}

.univer-date-range-picker-cell-range-hover-end::after {
    border-right: 1px solid green !important;
}

.univer-date-range-picker-cell-today > .univer-date-range-picker-cell-inner {
    border-width: 1px;
    border-style: solid;
    border-color: var(--univer-primary-600);
}

.univer-date-range-picker-cell-range-start > .univer-date-range-picker-cell-inner,
.univer-date-range-picker-cell-range-end > .univer-date-range-picker-cell-inner,
.univer-date-range-picker-cell-selected > .univer-date-range-picker-cell-inner {
    color: #fff;
    background-color: var(--univer-primary-600);
}

/* Preset */
.univer-date-range-picker-presets {
    background: #ccccff;
}

.univer-date-range-picker-presets ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

/* Now Button */
.univer-date-range-picker-now {
    padding-top: 12px;
}

.univer-date-range-picker-now-btn {
    font-size: 13px;
    cursor: pointer;
    color: var(--univer-primary-600);
}

/* Footer */
.univer-date-range-picker-footer,
.univer-date-range-picker-picker-footer {
    margin-top: 12px;
    border-width: 0px;
    border-top-width: 1px;
    border-style: solid;
    border-top-color: var(--univer-gray-200);
}

/* Dropdown */
.univer-date-range-picker-dropdown {
    position: absolute;
    pointer-events: none;
}

.univer-date-range-picker-dropdown-range {
    padding: 10px 0;
}

.univer-date-range-picker-dropdown-hidden {
    display: none;
}

/* Range Picker */
.univer-date-range-picker-range {
    position: relative;
    display: inline-flex;
}

.univer-date-range-picker-range-wrapper {
    display: flex;
}

.univer-date-range-picker-active-bar {
    bottom: 0;
    height: 3px;
    opacity: 0;
    transition: all 0.3s;
    pointer-events: none;
    background-color: var(--univer-primary-600);
}

.univer-date-range-picker-focused .univer-date-range-picker-active-bar {
    opacity: 1;
}

.univer-date-range-picker-panel-container {
    display: inline-block;
    vertical-align: top;
    transition: margin 0.3s;
    pointer-events: all;
}

.univer-date-range-picker-panel-layout {
    display: flex;
    flex-wrap: nowrap;
    align-items: stretch;
    overflow: hidden;
    border-radius: 0.25rem;
    border-width: 1px;
    border-style: solid;
    border-color: var(--univer-gray-200);
    background-color: var(--univer-white);
    padding: 1rem;
    --univer-tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    --univer-tw-shadow-colored: 0 1px 3px 0 var(--univer-tw-shadow-color), 0 1px 2px -1px var(--univer-tw-shadow-color);
    box-shadow: 0 0 rgba(0,0,0,0), 0 0 rgba(0,0,0,0), 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    box-shadow: var(--univer-tw-ring-offset-shadow, 0 0 rgba(0,0,0,0)), var(--univer-tw-ring-shadow, 0 0 rgba(0,0,0,0)), var(--univer-tw-shadow);
}
.univer-dark .univer-date-range-picker-panel-layout {
    border-color: var(--univer-gray-600);
    background-color: var(--univer-gray-900);
}

/* Input */
.univer-date-range-picker-input {
    position: relative;
    display: inline-flex;
    width: 100%;
}

.univer-date-range-picker-input > input {
    width: 100%;
    padding-left: 12px;
    border: none;
    border-radius: 4px;
    box-sizing: border-box;
    background-color: transparent;
}

.univer-date-range-picker-input > input:focus {
    outline: none;
}
.react-grid-layout {
  position: relative;
  transition: height 200ms ease;
}
.react-grid-item {
  transition: all 200ms ease;
  transition-property: left, top, width, height;
}
.react-grid-item img {
  pointer-events: none;
  -webkit-user-select: none;
          user-select: none;
}
.react-grid-item.cssTransforms {
  transition-property: transform, width, height;
}
.react-grid-item.resizing {
  transition: none;
  z-index: 1;
  will-change: width, height;
}

.react-grid-item.react-draggable-dragging {
  transition: none;
  z-index: 3;
  will-change: transform;
}

.react-grid-item.dropping {
  visibility: hidden;
}

.react-grid-item.react-grid-placeholder {
  background: red;
  opacity: 0.2;
  transition-duration: 100ms;
  z-index: 2;
  -webkit-user-select: none;
  -o-user-select: none;
  user-select: none;
}

.react-grid-item.react-grid-placeholder.placeholder-resizing {
  transition: none;
}

.react-grid-item > .react-resizable-handle {
  position: absolute;
  width: 20px;
  height: 20px;
}

.react-grid-item > .react-resizable-handle::after {
  content: "";
  position: absolute;
  right: 3px;
  bottom: 3px;
  width: 5px;
  height: 5px;
  border-right: 2px solid rgba(0, 0, 0, 0.4);
  border-bottom: 2px solid rgba(0, 0, 0, 0.4);
}

.react-resizable-hide > .react-resizable-handle {
  display: none;
}

.react-grid-item > .react-resizable-handle.react-resizable-handle-sw {
  bottom: 0;
  left: 0;
  cursor: sw-resize;
  transform: rotate(90deg);
}
.react-grid-item > .react-resizable-handle.react-resizable-handle-se {
  bottom: 0;
  right: 0;
  cursor: se-resize;
}
.react-grid-item > .react-resizable-handle.react-resizable-handle-nw {
  top: 0;
  left: 0;
  cursor: nw-resize;
  transform: rotate(180deg);
}
.react-grid-item > .react-resizable-handle.react-resizable-handle-ne {
  top: 0;
  right: 0;
  cursor: ne-resize;
  transform: rotate(270deg);
}
.react-grid-item > .react-resizable-handle.react-resizable-handle-w,
.react-grid-item > .react-resizable-handle.react-resizable-handle-e {
  top: 50%;
  margin-top: -10px;
  cursor: ew-resize;
}
.react-grid-item > .react-resizable-handle.react-resizable-handle-w {
  left: 0;
  transform: rotate(135deg);
}
.react-grid-item > .react-resizable-handle.react-resizable-handle-e {
  right: 0;
  transform: rotate(315deg);
}
.react-grid-item > .react-resizable-handle.react-resizable-handle-n,
.react-grid-item > .react-resizable-handle.react-resizable-handle-s {
  left: 50%;
  margin-left: -10px;
  cursor: ns-resize;
}
.react-grid-item > .react-resizable-handle.react-resizable-handle-n {
  top: 0;
  transform: rotate(225deg);
}
.react-grid-item > .react-resizable-handle.react-resizable-handle-s {
  bottom: 0;
  transform: rotate(45deg);
}
.univer-dropdown {
    position: absolute;
    z-index: 1070;
    top: -9999px;
    left: -9999px;
    display: block;
}

.univer-dropdown-hidden {
    display: none;
}

.univer-dropdown-slide-up-enter,
.univer-dropdown-slide-up-appear {
    transform-origin: 0 0;
    display: block !important;
    animation-duration: 0.15s;
    animation-fill-mode: both;
    opacity: 0;
    animation-play-state: paused;
    animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
}

.univer-dropdown-slide-up-leave {
    transform-origin: 0 0;
    display: block !important;
    animation-duration: 0.15s;
    animation-fill-mode: both;
    opacity: 1;
    animation-play-state: paused;
    animation-timing-function: cubic-bezier(0.6, 0.04, 0.98, 0.34);
}

.univer-dropdown-slide-up-enter.univer-dropdown-slide-up-enter-active.univer-dropdown-placement-bottomLeft,
.univer-dropdown-slide-up-appear.univer-dropdown-slide-up-appear-active.univer-dropdown-placement-bottomLeft,
.univer-dropdown-slide-up-enter.univer-dropdown-slide-up-enter-active.univer-dropdown-placement-bottomCenter,
.univer-dropdown-slide-up-appear.univer-dropdown-slide-up-appear-active.univer-dropdown-placement-bottomCenter,
.univer-dropdown-slide-up-enter.univer-dropdown-slide-up-enter-active.univer-dropdown-placement-bottomRight,
.univer-dropdown-slide-up-appear.univer-dropdown-slide-up-appear-active.univer-dropdown-placement-bottomRight {
    animation-name: dropdown-slide-up-in;
    animation-play-state: running;
}

.univer-dropdown-slide-up-enter.univer-dropdown-slide-up-enter-active.univer-dropdown-placement-topLeft,
.univer-dropdown-slide-up-appear.univer-dropdown-slide-up-appear-active.univer-dropdown-placement-topLeft,
.univer-dropdown-slide-up-enter.univer-dropdown-slide-up-enter-active.univer-dropdown-placement-topCenter,
.univer-dropdown-slide-up-appear.univer-dropdown-slide-up-appear-active.univer-dropdown-placement-topCenter,
.univer-dropdown-slide-up-enter.univer-dropdown-slide-up-enter-active.univer-dropdown-placement-topRight,
.univer-dropdown-slide-up-appear.univer-dropdown-slide-up-appear-active.univer-dropdown-placement-topRight {
    animation-name: dropdown-slide-down-in;
    animation-play-state: running;
}

.univer-dropdown-slide-up-leave.univer-dropdown-slide-up-leave-active.univer-dropdown-placement-bottomLeft,
.univer-dropdown-slide-up-leave.univer-dropdown-slide-up-leave-active.univer-dropdown-placement-bottomCenter,
.univer-dropdown-slide-up-leave.univer-dropdown-slide-up-leave-active.univer-dropdown-placement-bottomRight {
    animation-name: dropdown-slide-up-out;
    animation-play-state: running;
}

.univer-dropdown-slide-up-leave.univer-dropdown-slide-up-leave-active.univer-dropdown-placement-topLeft,
.univer-dropdown-slide-up-leave.univer-dropdown-slide-up-leave-active.univer-dropdown-placement-topCenter,
.univer-dropdown-slide-up-leave.univer-dropdown-slide-up-leave-active.univer-dropdown-placement-topRight {
    animation-name: dropdown-slide-down-out;
    animation-play-state: running;
}

@keyframes dropdown-slide-up-in {
    0% {
        transform-origin: 0% 0%;
        transform: scaleY(0);
        opacity: 0;
    }

    100% {
        transform-origin: 0% 0%;
        transform: scaleY(1);
        opacity: 1;
    }
}

@keyframes dropdown-slide-up-out {
    0% {
        transform-origin: 0% 0%;
        transform: scaleY(1);
        opacity: 1;
    }

    100% {
        transform-origin: 0% 0%;
        transform: scaleY(0);
        opacity: 0;
    }
}

@keyframes dropdown-slide-down-in {
    0% {
        transform-origin: 0% 100%;
        transform: scaleY(0);
        opacity: 0;
    }

    100% {
        transform-origin: 0% 100%;
        transform: scaleY(1);
        opacity: 1;
    }
}

@keyframes dropdown-slide-down-out {
    0% {
        transform-origin: 0% 100%;
        transform: scaleY(1);
        opacity: 1;
    }

    100% {
        transform-origin: 0% 100%;
        transform: scaleY(0);
        opacity: 0;
    }
}
.univer-menu {
    max-width: -moz-fit-content;
    max-width: fit-content;
    margin: 0px;
    box-sizing: border-box;
    width: -moz-fit-content;
    width: fit-content;
    min-width: 13rem;
    list-style-type: none;
    border-radius: 0.375rem;
    border-width: 1px;
    border-style: solid;
    border-color: var(--univer-gray-200);
    background-color: var(--univer-white);
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    --univer-tw-shadow: 0px 1px 6px -2px rgba(30, 40, 77, 0.08), 0px 2px 6px -1px rgba(30, 40, 77, 0.10);
    --univer-tw-shadow-colored: 0px 1px 6px -2px var(--univer-tw-shadow-color), 0px 2px 6px -1px var(--univer-tw-shadow-color);
    box-shadow: 0 0 rgba(0,0,0,0), 0 0 rgba(0,0,0,0), 0px 1px 6px -2px rgba(30, 40, 77, 0.08), 0px 2px 6px -1px rgba(30, 40, 77, 0.10);
    box-shadow: var(--univer-tw-ring-offset-shadow, 0 0 rgba(0,0,0,0)), var(--univer-tw-ring-shadow, 0 0 rgba(0,0,0,0)), var(--univer-tw-shadow);
}
.univer-dark .univer-menu {
    border-color: var(--univer-gray-600);
    background-color: var(--univer-gray-700);
}

.univer-menu-vertical {
    display: grid;
}

.univer-menu-hidden,
.univer-menu-submenu-hidden {
    display: none;
}

.univer-menu-item-group {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
}

.univer-menu-item-group:not(:last-child) {
    border-bottom: 1px solid #e3e5ea;
}
.univer-dark .univer-menu-item-group:not(:last-child) {
    border-bottom-color: var(--univer-gray-600);
}

.univer-menu-item-group-list {
    display: grid;
    margin: 0;
    padding: 0;
    grid-gap: 0.25rem;
    gap: 0.25rem;
}

.univer-menu-item-group-title {
    font-weight: 600;
    line-height: 1.5em;
    padding: 4px;
    padding-top: 6px;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    font-size: 0.75rem;
    line-height: 1rem;
    color: var(--univer-gray-600);
}
.univer-dark .univer-menu-item-group-title {
    color: var(--univer-white);
}

.univer-menu-item-group-title:empty {
    display: none;
}

.univer-menu-item-active,
.univer-menu-submenu-active {
    border-radius: 0.375rem;
    background-color: var(--univer-gray-50);
}
.univer-dark .univer-menu-item-active,
.univer-dark .univer-menu-submenu-active {
    background-color: var(--univer-gray-600);
}

.univer-menu-item-selected {
    transform: translateZ(0);
    border-radius: 0.375rem;
    background-color: var(--univer-gray-50);
}
.univer-dark .univer-menu-item-selected {
    background-color: var(--univer-gray-600);
}

.univer-menu-submenu-selected {
    border-radius: 0.375rem;
    background-color: var(--univer-gray-50);
}
.univer-dark .univer-menu-submenu-selected {
    background-color: var(--univer-gray-600);
}

.univer-menu > li.univer-menu-submenu {
    padding: 0;
}

.univer-menu-horizontal-sub,
.univer-menu-vertical-sub,
.univer-menu-vertical-left-sub,
.univer-menu-vertical-right-sub {
    min-width: 160px;
    margin-top: 0;
}

.univer-menu-item {
    display: flex;
    align-items: center;
    min-height: 32px;
    line-height: 1.5em;
    gap: 0.75rem;
    border-radius: 0.375rem;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: var(--univer-gray-900);
}
.univer-dark .univer-menu-item {
    color: var(--univer-white);
}

.univer-menu-item:hover {
    background-color: var(--univer-gray-50);
}
.univer-dark .univer-menu-item:hover {
    background-color: var(--univer-gray-600);
}

.univer-menu-item .icon {
    color: var(--univer-gray-600);
}
.univer-dark .univer-menu-item .icon {
    color: var(--univer-white);
}

.univer-menu-item,
.univer-menu-submenu {
    cursor: pointer;
    position: relative;
    box-sizing: border-box;
    margin: 0;
    white-space: nowrap;
    list-style: none;
}

.univer-menu-item.univer-menu-item-disabled,
.univer-menu-submenu.univer-menu-submenu-disabled {
    cursor: not-allowed;
    color: var(--univer-gray-400) !important;
}

.univer-menu-submenu-popup {
    position: absolute;
    z-index: 1070;
}

.univer-menu-submenu-popup .submenu-title-wrapper {
    padding-right: 1.25rem;
}

.univer-menu-submenu-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    min-height: 32px;
    line-height: 1.5em;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: var(--univer-gray-900);
}
.univer-dark .univer-menu-submenu-title {
    color: var(--univer-white);
}

.univer-menu-submenu-title-more-icon,
.univer-menu-item-more-icon {
    margin-left: 0.5rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: var(--univer-gray-400);
}

.univer-menu-horizontal {
    overflow: hidden;
    white-space: nowrap;
    border: none;
    border-bottom: 1px solid transparent;
    box-shadow: none;
    border-bottom-color: var(--univer-gray-200);
}
.univer-dark .univer-menu-horizontal {
    border-bottom-color: var(--univer-gray-600);
}

.univer-menu-horizontal > .univer-menu-item,
.univer-menu-horizontal > .univer-menu-submenu > .univer-menu-submenu-title {
    padding: 15px 20px;
}

.univer-menu-horizontal > .univer-menu-submenu,
.univer-menu-horizontal > .univer-menu-item {
    display: inline-block;
    vertical-align: bottom;
    border-bottom: 2px solid transparent;
}

.univer-menu-vertical,
.univer-menu-vertical-left,
.univer-menu-vertical-right,
.univer-menu-inline {
    width: 100%;
}

.univer-menu-vertical > .univer-menu-item,
.univer-menu-vertical > .univer-menu-submenu > .univer-menu-submenu-title,
.univer-menu-vertical-left > .univer-menu-item,
.univer-menu-vertical-left > .univer-menu-submenu > .univer-menu-submenu-title,
.univer-menu-vertical-right > .univer-menu-item,
.univer-menu-vertical-right > .univer-menu-submenu > .univer-menu-submenu-title,
.univer-menu-inline > .univer-menu-item,
.univer-menu-inline > .univer-menu-submenu > .univer-menu-submenu-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    width: 100%;
    min-height: 28px;
    padding: 0.25rem;
}

.univer-menu-vertical .univer-menu-submenu-arrow,
.univer-menu-vertical-left .univer-menu-submenu-arrow,
.univer-menu-vertical-right .univer-menu-submenu-arrow,
.univer-menu-inline .univer-menu-submenu-arrow {
    position: absolute;
    right: 16px;
    display: inline-block;
    font-size: inherit;
    line-height: 1.5em;
    text-align: center;
    text-transform: none;
    text-rendering: auto;
    vertical-align: baseline;
}

.univer-menu-sub-inline {
    padding: 0;
    border: none;
    border-radius: 0;
    box-shadow: none;
}

.univer-menu-sub-inline > .univer-menu-item,
.univer-menu-sub-inline > .univer-menu-submenu > .univer-menu-submenu-title {
    padding-top: 8px;
    padding-right: 0;
    padding-bottom: 8px;
}

.univer-menu-open-slide-up-enter,
.univer-menu-open-slide-up-appear {
    opacity: 0;
    animation-play-state: paused;
    animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
    transform-origin: 0 0;
    animation-duration: 0.3s;
    animation-fill-mode: both;
}

.univer-menu-open-slide-up-leave {
    opacity: 1;
    animation-play-state: paused;
    animation-timing-function: cubic-bezier(0.6, 0.04, 0.98, 0.34);
    transform-origin: 0 0;
    animation-duration: 0.3s;
    animation-fill-mode: both;
}

.univer-menu-open-slide-up-enter.univer-menu-open-slide-up-enter-active,
.univer-menu-open-slide-up-appear.univer-menu-open-slide-up-appear-active {
    animation-name: menu-open-slide-up-in;
    animation-play-state: running;
}

.univer-menu-open-slide-up-leave.univer-menu-open-slide-up-leave-active {
    animation-name: menu-open-slide-up-out;
    animation-play-state: running;
}

@keyframes menu-open-slide-up-in {
    0% {
        transform-origin: 0% 0%;
        transform: scaleY(0);
        opacity: 0;
    }

    100% {
        transform-origin: 0% 0%;
        transform: scaleY(1);
        opacity: 1;
    }
}

@keyframes menu-open-slide-up-out {
    0% {
        transform-origin: 0% 0%;
        transform: scaleY(1);
        opacity: 1;
    }

    100% {
        transform-origin: 0% 0%;
        transform: scaleY(0);
        opacity: 0;
    }
}

.univer-menu-open-zoom-enter,
.univer-menu-open-zoom-appear {
    opacity: 0;
    animation-play-state: paused;
    animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
    transform-origin: 0 0;
    animation-duration: 0.3s;
    animation-fill-mode: both;
}

.univer-menu-open-zoom-leave {
    animation-play-state: paused;
    animation-timing-function: cubic-bezier(0.6, 0.04, 0.98, 0.34);
    transform-origin: 0 0;
    animation-duration: 0.3s;
    animation-fill-mode: both;
}

.univer-menu-open-zoom-enter.univer-menu-open-zoom-enter-active,
.univer-menu-open-zoom-appear.univer-menu-open-zoom-appear-active {
    animation-name: menu-open-zoom-in;
    animation-play-state: running;
}

.univer-menu-open-zoom-leave.univer-menu-open-zoom-leave-active {
    animation-name: menu-open-zoom-out;
    animation-play-state: running;
}

@keyframes menu-open-zoom-in {
    0% {
        transform: scale(0, 0);
        opacity: 0;
    }

    100% {
        transform: scale(1, 1);
        opacity: 1;
    }
}

@keyframes menu-open-zoom-out {
    0% {
        transform: scale(1, 1);
    }

    100% {
        transform: scale(0, 0);
        opacity: 0;
    }
}
.univer-popup {
    position: fixed;
    z-index: 1070;
    top: -9999px;
    left: -9999px;
    border-radius: 6px;
    overflow: hidden;
    --univer-tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    --univer-tw-shadow-colored: 0 1px 3px 0 var(--univer-tw-shadow-color), 0 1px 2px -1px var(--univer-tw-shadow-color);
    box-shadow: 0 0 rgba(0,0,0,0), 0 0 rgba(0,0,0,0), 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    box-shadow: var(--univer-tw-ring-offset-shadow, 0 0 rgba(0,0,0,0)), var(--univer-tw-ring-shadow, 0 0 rgba(0,0,0,0)), var(--univer-tw-shadow);
}

.univer-popup-enter {
    transform-origin: 0 0;
    animation-duration: 0.15s;
    animation-fill-mode: both;
    opacity: 0;
    animation-play-state: paused;
    animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
}

.univer-popup-enter-active {
    animation-name: popup-slide-up-in;
    animation-play-state: running;
}

.univer-popup-exit {
    transform-origin: 0 0;
    animation-duration: 0.15s;
    animation-fill-mode: both;
    opacity: 1;
    animation-name: popup-slide-up-out;
    animation-play-state: running;
    animation-timing-function: cubic-bezier(0.6, 0.04, 0.98, 0.34);
}

.univer-popup-exit-active {
    display: none;
}

@keyframes popup-slide-up-in {
    0% {
        transform-origin: 0 0;
        transform: scaleY(0);
        opacity: 0;
    }

    100% {
        transform-origin: 0 0;
        transform: scaleY(1);
        opacity: 1;
    }
}

@keyframes popup-slide-up-out {
    0% {
        transform-origin: 0 0;
        transform: scaleY(1);
        opacity: 1;
    }

    100% {
        transform-origin: 0 0;
        transform: scaleY(0);
        opacity: 0;
    }
}

.univer-popup-mask {
    position: fixed;
    z-index: 1060;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}
.univer-tree {
    -webkit-user-select: none;
       -moz-user-select: none;
            user-select: none;
    position: relative;
    color: var(--univer-gray-900);
}
.univer-dark .univer-tree {
    color: var(--univer-white);
}

.univer-tree-icon {
    position: absolute;
    top: 50%;
    left: calc(-1 * 16px);
    transform: translateY(-50%) rotateZ(-90deg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
}

.univer-tree-icon-expand {
    transform: translateY(-50%);
}

.univer-tree-list {
    margin: 0;
    padding: 0;
    list-style: none;
    height: 100%;
}

.univer-tree-list .univer-tree-list {
    overflow: hidden;
    height: 0;
}

.univer-tree-list .univer-tree-list-expand {
    height: inherit;
}

.univer-tree-list-item {
    position: relative;
    padding-left: 20px;
    font-size: 13px;
}

.univer-tree-list-item > a {
    position: relative;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.univer-tree-list-item-title {
    display: inline-block;
    height: 100%;
    margin: 0 4px;
    flex-shrink: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.univer-tree-list-item-content {
    margin: 4px 0;
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
}

.univer-tree-list-item-content-selected-icon {
    position: absolute;
    top: 50%;
    left: calc(-1 * 20px);
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    line-height: 1.5rem;
}
