{"version": 3, "file": "ta-Cai0wUPn.mjs", "sources": ["../../../engine-render/src/components/docs/layout/hyphenation/patterns/ta.ts"], "sourcesContent": ["/**\n * Copyright 2023-present DreamNum Co., Ltd.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const Ta = [\n    '22,11,01,1,201,21',\n    '{\"‍\":0,\"‌\":1,\"அ\":1,\"ஆ\":1,\"இ\":1,\"ஈ\":1,\"உ\":1,\"ஊ\":1,\"எ\":1,\"ஏ\":1,\"ஐ\":1,\"ஒ\":1,\"ஓ\":1,\"ஔ\":1,\"ா\":2,\"ி\":2,\"ீ\":2,\"ு\":2,\"ூ\":2,\"ெ\":2,\"ே\":2,\"ை\":2,\"ொ\":2,\"ோ\":2,\"ௌ\":2,\"க\":[{\"்\":4},3],\"ங\":[{\"்\":4},3],\"ச\":[{\"்\":4},3],\"ஜ\":3,\"ஞ\":[{\"்\":4},3],\"ட\":[{\"்\":4},3],\"ண\":[{\"்\":4},3],\"த\":[{\"்\":4},3],\"ந\":[{\"்\":4},3],\"ப\":[{\"்\":4},3],\"ம\":[{\"்\":4},3],\"ய\":[{\"்\":4},3],\"ர\":[{\"்\":4},3],\"ற\":[{\"்\":4},3],\"ல\":[{\"்\":4},3],\"ள\":[{\"்\":4},3],\"ழ\":[{\"்\":4},3],\"வ\":[{\"்\":4},3],\"ஷ\":[{\"்\":4},3],\"ஸ\":[{\"்\":4},3],\"ஹ\":[{\"்\":4},3],\"ன\":[{\"்\":4}],\"ஂ\":5,\"ஃ\":5,\"ௗ\":5,\"்\":5}',\n    [],\n];\n"], "names": [], "mappings": "AAgBO,MAAM,KAAK;AAAA,EACd;AAAA,EACA;AAAA,EACA,CAAA;AACJ;"}