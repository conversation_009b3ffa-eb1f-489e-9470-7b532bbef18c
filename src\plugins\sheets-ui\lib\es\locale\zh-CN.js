const e = {
  spreadsheetLabel: "插件",
  spreadsheetRightLabel: "更多 Sheets",
  // toolbar.undo
  toolbar: {
    undo: "撤销",
    redo: "重做",
    formatPainter: "格式刷",
    font: "字体",
    fontSize: "字号",
    bold: "粗体",
    italic: "斜体",
    strikethrough: "删除线",
    subscript: "下标",
    superscript: "上标",
    underline: "下划线",
    textColor: {
      main: "文本颜色",
      right: "颜色选择"
    },
    resetColor: "重置颜色",
    fillColor: {
      main: "单元格颜色",
      right: "颜色选择"
    },
    border: {
      main: "边框",
      right: "边框类型"
    },
    mergeCell: {
      main: "合并单元格",
      right: "选择合并类型"
    },
    horizontalAlignMode: {
      main: "水平对齐",
      right: "对齐方式"
    },
    verticalAlignMode: {
      main: "垂直对齐",
      right: "对齐方式"
    },
    textWrapMode: {
      main: "文本换行",
      right: "换行方式"
    },
    textRotateMode: {
      main: "文本旋转",
      right: "旋转方式"
    },
    more: "更多",
    toggleGridlines: "切换网格线"
  },
  align: {
    left: "左对齐",
    center: "中间对齐",
    right: "右对齐",
    top: "顶部对齐",
    middle: "居中对齐",
    bottom: "底部对齐"
  },
  button: {
    confirm: "确定",
    cancel: "取消",
    close: "关闭",
    update: "Update",
    delete: "Delete",
    insert: "新建",
    prevPage: "上一页",
    nextPage: "下一页",
    total: "总共："
  },
  punctuation: {
    tab: "Tab 键",
    semicolon: "分号",
    comma: "逗号",
    space: "空格"
  },
  colorPicker: {
    collapse: "收起",
    customColor: "自定义",
    change: "切换",
    confirmColor: "确定",
    cancelColor: "取消"
  },
  borderLine: {
    borderTop: "上框线",
    borderBottom: "下框线",
    borderLeft: "左框线",
    borderRight: "右框线",
    borderNone: "无",
    borderAll: "所有",
    borderOutside: "外侧",
    borderInside: "内侧",
    borderHorizontal: "内侧横线",
    borderVertical: "内侧竖线",
    borderColor: "边框颜色",
    borderSize: "边框粗细",
    borderType: "边框线类型"
  },
  merge: {
    all: "全部合并",
    vertical: "垂直合并",
    horizontal: "水平合并",
    cancel: "取消合并",
    overlappingError: "不能合并重叠区域",
    partiallyError: "无法对部分合并单元格执行此操作",
    confirm: {
      title: "合并单元格仅保存左上角单元格的值，是否继续？",
      cancel: "取消合并",
      confirm: "继续合并",
      warning: "警告",
      dismantleMergeCellWarning: "此操作会导致一些合并单元格被拆散，是否继续?"
    }
  },
  filter: {
    confirm: {
      error: "出现了一个问题",
      notAllowedToInsertRange: "要移动这些单元格，请清除该区域的筛选器"
    }
  },
  textWrap: {
    overflow: "溢出",
    wrap: "自动换行",
    clip: "截断"
  },
  textRotate: {
    none: "无旋转",
    angleUp: "向上倾斜",
    angleDown: "向下倾斜",
    vertical: "竖排文字",
    rotationUp: "向上90°",
    rotationDown: "向下90°"
  },
  sheetConfig: {
    delete: "删除",
    copy: "复制",
    rename: "重命名",
    changeColor: "更改颜色",
    hide: "隐藏",
    unhide: "取消隐藏",
    moveLeft: "向左移",
    moveRight: "向右移",
    resetColor: "重置颜色",
    cancelText: "取消",
    chooseText: "确定颜色",
    tipNameRepeat: "标签页的名称不能重复！请重新修改",
    noMoreSheet: "工作薄内至少含有一张可视工作表。若需删除选定的工作表，请先插入一张新工作表或显示一张隐藏的工作表。",
    confirmDelete: "是否删除",
    redoDelete: "可以通过Ctrl+Z撤销删除",
    noHide: "不能隐藏, 至少保留一个sheet标签",
    chartEditNoOpt: "图表编辑模式下不允许该操作！",
    sheetNameErrorTitle: "错误",
    sheetNameSpecCharError: "名称不能超过 31 个字符，首尾不能是' 且名称不能包含: [ ] : \\ ? * /",
    sheetNameCannotIsEmptyError: "名称不能为空。",
    sheetNameAlreadyExistsError: "工作表已存在，请输入其它名称。",
    deleteSheet: "删除工作表",
    deleteSheetContent: "确认删除此工作表，删除后将不可找回，确定要删除吗？",
    addProtectSheet: "保护工作表",
    removeProtectSheet: "取消保护工作表",
    changeSheetPermission: "更改工作表权限",
    viewAllProtectArea: "查看所有保护范围"
  },
  rightClick: {
    copy: "复制",
    cut: "剪切",
    paste: "粘贴",
    pasteSpecial: "选择性粘贴",
    pasteValue: "仅粘贴值",
    pasteFormat: "仅粘贴格式",
    pasteColWidth: "仅粘贴列宽",
    pasteBesidesBorder: "仅粘贴边框以外内容",
    insert: "插入",
    delete: "删除",
    insertRow: "插入行",
    insertRowBefore: "在上方插入行",
    insertRowsAbove: "在上方插入",
    insertRowsAfter: "在下方插入",
    insertRowsAfterSuffix: "行",
    insertRowsAboveSuffix: "行",
    insertColumn: "插入列",
    insertColumnBefore: "在左侧插入列",
    insertColsLeft: "在左侧插入",
    insertColsRight: "在右侧插入",
    insertColsLeftSuffix: "列",
    insertColsRightSuffix: "列",
    deleteCell: "删除单元格",
    insertCell: "插入单元格",
    deleteSelected: "删除选中",
    hide: "隐藏",
    hideSelected: "隐藏选中",
    showHide: "显示隐藏",
    toTopAdd: "向上增加",
    toBottomAdd: "向下增加",
    toLeftAdd: "向左增加",
    toRightAdd: "向右增加",
    deleteSelectedRow: "删除选中行",
    deleteSelectedColumn: "删除选中列",
    hideSelectedRow: "隐藏选中行",
    showHideRow: "显示隐藏行",
    rowHeight: "行高",
    hideSelectedColumn: "隐藏选中列",
    showHideColumn: "显示隐藏列",
    columnWidth: "列宽",
    moveLeft: "左移",
    moveUp: "上移",
    moveRight: "右移",
    moveDown: "下移",
    add: "增加",
    row: "行",
    column: "列",
    confirm: "确认",
    clearSelection: "清除",
    clearContent: "清除内容",
    clearFormat: "清除格式",
    clearAll: "清除全部",
    root: "次方根",
    log: "log",
    delete0: "删除两端0值",
    removeDuplicate: "删除重复值",
    byRow: "按行",
    byCol: "按列",
    generateNewMatrix: "生成新矩阵",
    fitContent: "适合数据",
    freeze: "冻结",
    freezeCol: "冻结列",
    freezeRow: "冻结行",
    cancelFreeze: "取消冻结",
    deleteAllRowsAlert: "您无法删除工作表上的所有行",
    deleteAllColumnsAlert: "您无法删除工作表上的所有列",
    hideAllRowsAlert: "您无法隐藏工作表上的所有行",
    hideAllColumnsAlert: "您无法隐藏工作表上的所有列",
    protectRange: "保护行列",
    editProtectRange: "设置保护范围",
    removeProtectRange: "移除保护范围",
    turnOnProtectRange: "新增保护范围",
    viewAllProtectArea: "查看所有保护范围"
  },
  info: {
    tooltip: "提示",
    error: "错误",
    notChangeMerge: "不能对合并单元格做部分更改",
    detailUpdate: "新打开",
    detailSave: "已恢复本地缓存",
    row: "行",
    column: "列",
    loading: "渲染中···",
    copy: "副本",
    return: "返回",
    rename: "重命名",
    tips: "重命名",
    noName: "无标题的电子表格",
    wait: "待更新",
    add: "添加",
    addLast: "在底部添加",
    backTop: "回到顶部",
    // eslint-disable-next-line no-template-curly-in-string
    pageInfo: "共${total}条，${totalPage}页，当前已显示${currentPage}页",
    nextPage: "下一页",
    tipInputNumber: "请输入数字",
    tipInputNumberLimit: "增加范围限制在1-100",
    tipRowHeightLimit: "行高必须在0 ~ 545之间",
    tipColumnWidthLimit: "列宽必须在0 ~ 2038之间",
    // eslint-disable-next-line no-template-curly-in-string
    pageInfoFull: "共${total}条，${totalPage}页，已显示全部数据",
    problem: "出现了一个问题",
    forceStringInfo: "以文本形式存储的数字"
  },
  clipboard: {
    paste: {
      exceedMaxCells: "粘贴区域超出最大单元格数",
      overlappingMergedCells: "粘贴区域与合并单元格重叠"
    },
    shortCutNotify: {
      title: "请使用快捷键粘贴",
      useShortCutInstead: "检测到 Excel 内容，请使用快捷键粘贴"
    }
  },
  statusbar: {
    sum: "求和",
    average: "平均值",
    min: "最小值",
    max: "最大值",
    count: "数值计数",
    countA: "计数",
    clickToCopy: "点击复制数值",
    copied: "已复制"
  },
  autoFill: {
    copy: "复制单元格",
    series: "填充序列",
    formatOnly: "仅填充格式",
    noFormat: "不带格式填充"
  },
  rangeSelector: {
    placeholder: "选择范围或输入值",
    tooltip: "选择范围"
  },
  shortcut: {
    sheet: {
      "zoom-in": "放大",
      "zoom-out": "缩小",
      "reset-zoom": "恢复缩放",
      "select-below-cell": "选择下方单元格",
      "select-up-cell": "选择上方单元格",
      "select-left-cell": "选择左侧单元格",
      "select-right-cell": "选择右侧单元格",
      "select-next-cell": "选择后一个单元格",
      "select-previous-cell": "选择前一个单元格",
      "select-up-value-cell": "选择上方有值的单元格",
      "select-below-value-cell": "选择下方有值的单元格",
      "select-left-value-cell": "选择左侧有值的单元格",
      "select-right-value-cell": "选择右侧有值的单元格",
      "expand-selection-down": "向下扩展选区",
      "expand-selection-up": "向上扩展选区",
      "expand-selection-left": "向左扩展选区",
      "expand-selection-right": "向右扩展选区",
      "expand-selection-to-left-gap": "向左扩展选区到下一个边界",
      "expand-selection-to-below-gap": "向下扩展选区到下一个边界",
      "expand-selection-to-right-gap": "向右扩展选区到下一个边界",
      "expand-selection-to-up-gap": "向上扩展选区到下一个边界",
      "select-all": "全选",
      "toggle-editing": "开始 / 结束编辑",
      "delete-and-start-editing": "清空并开始编辑",
      "abort-editing": "放弃编辑",
      "break-line": "换行",
      "set-bold": "切换粗体",
      "set-italic": "切换斜体",
      "set-underline": "切换下划线",
      "set-strike-through": "切换删除线",
      "start-editing": "开始编辑（选区切换到编辑器）"
    }
  },
  "sheet-view": "浏览表格",
  "sheet-edit": "编辑表格",
  definedName: {
    managerTitle: "名称管理器",
    managerDescription: "通过选择单元格或公式，并在文本框中输入您想要的名称来创建一个定义名称。",
    addButton: "新建名称",
    featureTitle: "定义名称",
    ratioRange: "范围",
    ratioFormula: "公式",
    confirm: "确认",
    cancel: "取消",
    scopeWorkbook: "工作簿",
    inputNamePlaceholder: "请输入名称（必填）",
    inputCommentPlaceholder: "请输入备注",
    inputRangePlaceholder: "请输入范围（必填）",
    inputFormulaPlaceholder: "请输入公式（必填）",
    nameEmpty: "名称不能为空",
    nameDuplicate: "名称重复",
    formulaOrRefStringEmpty: "公式或引用字符串不能为空",
    formulaOrRefStringInvalid: "公式或引用字符串无效",
    defaultName: "DefinedName",
    updateButton: "修改",
    deleteButton: "删除",
    deleteConfirmText: "确定删除定义名称？",
    nameConflict: "与函数名称冲突",
    nameInvalid: "名称不包含空格或不允许的字符",
    nameSheetConflict: "名称与工作表名称冲突"
  },
  uploadLoading: {
    loading: "正在上传，当前剩余",
    error: "加载失败"
  },
  permission: {
    toolbarMenu: "保护",
    panel: {
      title: "保护行列",
      name: "名称",
      protectedRange: "保护区域",
      permissionDirection: "权限描述",
      permissionDirectionPlaceholder: "请输入权限描述",
      editPermission: "编辑权限",
      onlyICanEdit: "仅我可以编辑",
      designedUserCanEdit: "指定用户可以编辑",
      viewPermission: "查看权限",
      othersCanView: "其他人可以查看",
      noOneElseCanView: "其他人不可以查看",
      designedPerson: "指定人员",
      addPerson: "添加人员",
      canEdit: "可编辑",
      canView: "可查看",
      delete: "删除",
      currentSheet: "当前工作表",
      allSheet: "所有工作表",
      edit: "编辑",
      Print: "打印",
      Comment: "评论",
      Copy: "复制",
      SetCellStyle: "设置单元格样式",
      SetCellValue: "设置单元格值",
      SetHyperLink: "设置超链接",
      Sort: "排序",
      Filter: "筛选",
      PivotTable: "数据透视表",
      FloatImage: "浮动图片",
      RowHeightColWidth: "行高列宽",
      RowHeightColWidthReadonly: "只读行高列宽",
      FilterReadonly: "只读筛选",
      nameError: "名称不能为空",
      created: "创建",
      iCanEdit: "我可以编辑",
      iCanNotEdit: "我不可以编辑",
      iCanView: "我可以查看",
      iCanNotView: "我不可以查看",
      emptyRangeError: "范围不能为空",
      rangeOverlapError: "范围不能重叠",
      rangeOverlapOverPermissionError: "范围不能重叠已有权限范围",
      InsertHyperlink: "插入超链接",
      SetRowStyle: "设置行样式",
      SetColumnStyle: "设置列样式",
      InsertColumn: "插入列",
      InsertRow: "插入行",
      DeleteRow: "删除行",
      DeleteColumn: "删除列",
      EditExtraObject: "编辑其他对象"
    },
    dialog: {
      allowUserToEdit: "允许用户编辑",
      allowedPermissionType: "允许权限类型",
      setCellValue: "设置单元格值",
      setCellStyle: "设置单元格样式",
      copy: "复制",
      alert: "提示",
      search: "搜索",
      alertContent: "该范围已被保护，目前无编辑权限。如需编辑，请联系创建者。",
      userEmpty: "没有指定的人员，分享链接以邀请特定的人。",
      listEmpty: "你还没有设置任何范围或工作表为受保护状态。",
      commonErr: "该范围已被保护，目前无该操作权限。如需编辑，请联系创建者。",
      editErr: "该范围已被保护，目前无编辑权限。如需编辑，请联系创建者。",
      pasteErr: "该范围已被保护，目前无粘贴权限。如需粘贴，请联系创建者。",
      setStyleErr: "该范围已被保护，目前无设置样式权限。如需设置样式，请联系创建者。",
      copyErr: "该范围已被保护，目前无复制权限。如需复制，请联系创建者。",
      workbookCopyErr: "该工作簿已被保护，目前无复制权限。如需复制，请联系创建者。",
      setRowColStyleErr: "该范围已被保护，目前无设置行列样式权限。如需设置行列样式，请联系创建者。",
      moveRowColErr: "该范围已被保护，目前无移动行列权限。如需移动行列，请联系创建者。",
      moveRangeErr: "该范围已被保护，目前无移动选区权限。如需移动选区，请联系创建者。",
      autoFillErr: "该范围已被保护，目前无自动填充权限。如需自动填充，请联系创建者。",
      filterErr: "该范围已被保护，目前无筛选权限。如需筛选，请联系创建者。",
      operatorSheetErr: "该工作表已被保护，目前无操作工作表权限。如需操作工作表，请联系创建者。",
      insertOrDeleteMoveRangeErr: "插入、删除区域与保护范围相交，暂不支持此操作。",
      printErr: "该工作表已被保护，目前无打印权限。如需打印，请联系创建者。",
      formulaErr: "该范围或者引用范围已被保护，目前无编辑权限。如需编辑，请联系创建者。",
      hyperLinkErr: "该范围已被保护，目前无设置超链接权限。如需设置超链接，请联系创建者。"
    },
    button: {
      confirm: "确认",
      cancel: "取消",
      addNewPermission: "添加新权限"
    }
  }
};
export {
  e as default
};
