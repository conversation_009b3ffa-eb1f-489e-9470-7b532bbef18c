"use strict";var Tt=Object.defineProperty;var Ot=(e,t,a)=>t in e?Tt(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a;var L=(e,t,a)=>Ot(e,typeof t!="symbol"?t+"":t,a);Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const o=require("@univerjs/core"),A=require("@univerjs/engine-render"),H=require("@univerjs/sheets"),C=require("@univerjs/sheets-data-validation"),Y=require("@univerjs/data-validation"),M=require("@univerjs/ui"),oe=require("rxjs"),Pt=require("@univerjs/sheets-numfmt"),N=require("@univerjs/sheets-ui"),E=require("@univerjs/design"),b=require("react"),p=require("react/jsx-runtime"),et=require("@univerjs/engine-formula"),it=require("@univerjs/sheets-formula-ui");var Lt=Object.getOwnPropertyDescriptor,At=(e,t,a,n)=>{for(var r=n>1?void 0:n?Lt(t,a):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(r=s(r)||r);return r},ut=(e,t)=>(a,n)=>t(a,n,e);let he=class extends o.Disposable{constructor(t,a){super();L(this,"_open$",new oe.BehaviorSubject(!1));L(this,"open$",this._open$.pipe(oe.distinctUntilChanged()));L(this,"_activeRule");L(this,"_activeRule$",new oe.BehaviorSubject(void 0));L(this,"activeRule$",this._activeRule$.asObservable());L(this,"_closeDisposable",null);this._univerInstanceService=t,this._sidebarService=a,this.disposeWithMe(this._univerInstanceService.getCurrentTypeOfUnit$(o.UniverInstanceType.UNIVER_SHEET).pipe(oe.filter(n=>!n)).subscribe(()=>{this.close()})),this.disposeWithMe(this._sidebarService.sidebarOptions$.subscribe(n=>{n.id===Fe&&(n.visible||setTimeout(()=>{this._sidebarService.sidebarOptions$.next({visible:!1})}))}))}get activeRule(){return this._activeRule}get isOpen(){return this._open$.getValue()}dispose(){var t;super.dispose(),this._open$.next(!1),this._open$.complete(),this._activeRule$.complete(),(t=this._closeDisposable)==null||t.dispose()}open(){this._open$.next(!0)}close(){var t;this._open$.next(!1),(t=this._closeDisposable)==null||t.dispose()}setCloseDisposable(t){this._closeDisposable=o.toDisposable(()=>{t.dispose(),this._closeDisposable=null})}setActiveRule(t){this._activeRule=t,this._activeRule$.next(t)}};he=At([ut(0,o.IUniverInstanceService),ut(1,M.ISidebarService)],he);const fe="#ECECEC",ot="sheets-data-validation-ui.config",Ue={};var jt=Object.getOwnPropertyDescriptor,xt=(e,t,a,n)=>{for(var r=n>1?void 0:n?jt(t,a):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(r=s(r)||r);return r},Ce=(e,t)=>(a,n)=>t(a,n,e);let we=class extends o.Disposable{constructor(e,t,a,n,r,i){super(),this._sheetInterceptorService=e,this._dataValidationModel=t,this._dataValidatorRegistryService=a,this._dialogService=n,this._localeService=r,this._sheetsDataValidationValidatorService=i,this._initEditorBridgeInterceptor()}_initEditorBridgeInterceptor(){this._sheetInterceptorService.writeCellInterceptor.intercept(H.VALIDATE_CELL,{handler:async(e,t,a)=>{const n=await e,{row:r,col:i,unitId:s,subUnitId:d}=t,l=this._dataValidationModel.getRuleIdByLocation(s,d,r,i),c=l?this._dataValidationModel.getRuleById(s,d,l):void 0;if(n===!1)return a(Promise.resolve(!1));if(!c||c.errorStyle!==o.DataValidationErrorStyle.STOP)return a(Promise.resolve(!0));const u=this._dataValidatorRegistryService.getValidatorItem(c.type);return!u||await this._sheetsDataValidationValidatorService.validatorCell(s,d,r,i)===o.DataValidationStatus.VALID?a(Promise.resolve(!0)):(this._dialogService.open({width:368,title:{title:this._localeService.t("dataValidation.alert.title")},id:"reject-input-dialog",children:{title:u.getRuleFinalError(c,{row:r,col:i,unitId:s,subUnitId:d})},footer:{title:b.createElement(E.Button,{variant:"primary",onClick:()=>this._dialogService.close("reject-input-dialog")},this._localeService.t("dataValidation.alert.ok"))},onClose:()=>{this._dialogService.close("reject-input-dialog")}}),a(Promise.resolve(!1)))}})}showReject(e){this._dialogService.open({width:368,title:{title:this._localeService.t("dataValidation.alert.title")},id:"reject-input-dialog",children:{title:e},footer:{title:b.createElement(E.Button,{variant:"primary",onClick:()=>this._dialogService.close("reject-input-dialog")},this._localeService.t("dataValidation.alert.ok"))},onClose:()=>{this._dialogService.close("reject-input-dialog")}})}};we=xt([Ce(0,o.Inject(H.SheetInterceptorService)),Ce(1,o.Inject(C.SheetDataValidationModel)),Ce(2,o.Inject(Y.DataValidatorRegistryService)),Ce(3,M.IDialogService),Ce(4,o.Inject(o.LocaleService)),Ce(5,o.Inject(C.SheetsDataValidationValidatorService))],we);var Ut=Object.getOwnPropertyDescriptor,Ft=(e,t,a,n)=>{for(var r=n>1?void 0:n?Ut(t,a):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(r=s(r)||r);return r},le=(e,t)=>(a,n)=>t(a,n,e);const Ye=e=>{if(e==null||typeof e=="boolean")return;if(typeof e=="number"||!Number.isNaN(+e))return o.dayjs(o.numfmt.format("yyyy-MM-dd HH:mm:ss",Number(e)));const t=o.dayjs(e);if(t.isValid())return t};function kt(e,t){const a=Pt.getPatternType(t);if(e===a)return t;switch(e){case"datetime":return"yyyy-MM-dd hh:mm:ss";case"date":return"yyyy-MM-dd";case"time":return"HH:mm:ss"}}let Se=class extends o.Disposable{constructor(t,a,n,r,i,s,d,l,c,u,g){super();L(this,"_activeDropdown");L(this,"_activeDropdown$",new oe.Subject);L(this,"_currentPopup",null);L(this,"activeDropdown$",this._activeDropdown$.asObservable());L(this,"_zenVisible",!1);this._univerInstanceService=t,this._dataValidatorRegistryService=a,this._zenZoneService=n,this._dataValidationModel=r,this._sheetsSelectionsService=i,this._cellDropdownManagerService=s,this._sheetDataValidationModel=d,this._commandService=l,this._editorBridgeService=c,this._injector=u,this._configService=g,this._init(),this._initSelectionChange(),this.disposeWithMe(()=>{this._activeDropdown$.complete()})}get activeDropdown(){return this._activeDropdown}_init(){this.disposeWithMe(this._zenZoneService.visible$.subscribe(t=>{this._zenVisible=t,t&&this.hideDropdown()}))}_getDropdownByCell(t,a,n,r){const i=t?this._univerInstanceService.getUnit(t,o.UniverInstanceType.UNIVER_SHEET):this._univerInstanceService.getCurrentUnitForType(o.UniverInstanceType.UNIVER_SHEET);if(!i)return;const s=a?i.getSheetBySheetId(a):i.getActiveSheet();if(!s)return;const d=this._dataValidationModel.getRuleByLocation(i.getUnitId(),s.getSheetId(),n,r);if(!d)return;const l=this._dataValidatorRegistryService.getValidatorItem(d.type);return l==null?void 0:l.dropdownType}_initSelectionChange(){this.disposeWithMe(this._sheetsSelectionsService.selectionMoveEnd$.subscribe(t=>{t&&t.every(a=>!(a.primary&&this._getDropdownByCell(a.primary.unitId,a.primary.sheetId,a.primary.actualRow,a.primary.actualColumn)))&&this.hideDropdown()}))}showDropdown(t){var T,V,m,S;const{location:a}=t,{row:n,col:r,unitId:i,subUnitId:s,workbook:d,worksheet:l}=a;if(this._currentPopup&&this._currentPopup.dispose(),this._zenVisible)return;this._activeDropdown=t,this._activeDropdown$.next(this._activeDropdown);const c=this._sheetDataValidationModel.getRuleByLocation(i,s,n,r);if(!c)return;const u=this._dataValidatorRegistryService.getValidatorItem(c.type);if(!(u!=null&&u.dropdownType))return;let g;const h=async(_,D)=>{var j,U,B;if(!_)return!0;const R=_,y=l.getCell(n,r),w=R.format(D==="date"?"YYYY-MM-DD 00:00:00":"YYYY-MM-DD HH:mm:ss"),k=(j=o.numfmt.parseDate(w))==null?void 0:j.v,W=D==="time"?k%1:k,O=d.getStyles().getStyleByCell(y),x=(B=(U=O==null?void 0:O.n)==null?void 0:U.pattern)!=null?B:"";return c.errorStyle!==o.DataValidationErrorStyle.STOP||await u.validator({value:W,unitId:i,subUnitId:s,row:n,column:r,worksheet:l,workbook:d,interceptValue:w.replace("Z","").replace("T"," "),t:o.CellValueType.NUMBER},c)?(await this._commandService.executeCommand(H.SetRangeValuesCommand.id,{unitId:i,subUnitId:s,range:{startColumn:r,endColumn:r,startRow:n,endRow:n},value:{v:W,t:2,p:null,f:null,si:null,s:{n:{pattern:kt(D,x)}}}}),await this._commandService.executeCommand(N.SetCellEditVisibleOperation.id,{visible:!1,eventType:A.DeviceInputEventType.Keyboard,unitId:i,keycode:M.KeyCode.ESC}),!0):(this._injector.has(we)&&this._injector.get(we).showReject(u.getRuleFinalError(c,{row:n,col:r,unitId:i,subUnitId:s})),!1)};let f;switch(u.dropdownType){case Y.DataValidatorDropdownType.DATE:{const _=C.getCellValueOrigin(l.getCellRaw(n,r)),D=Ye(_),R=!!((T=c.bizInfo)!=null&&T.showTime);f={location:a,type:"datepicker",props:{showTime:R,onChange:y=>h(y,R?"datetime":"date"),defaultValue:D,patternType:"date"}};break}case Y.DataValidatorDropdownType.TIME:{const _=C.getCellValueOrigin(l.getCellRaw(n,r)),D=Ye(_);f={location:a,type:"datepicker",props:{onChange:R=>h(R,"time"),defaultValue:D,patternType:"time"}};break}case Y.DataValidatorDropdownType.DATETIME:{const _=C.getCellValueOrigin(l.getCellRaw(n,r)),D=Ye(_);f={location:a,type:"datepicker",props:{onChange:R=>h(R,"datetime"),defaultValue:D,patternType:"datetime"}};break}case Y.DataValidatorDropdownType.LIST:case Y.DataValidatorDropdownType.MULTIPLE_LIST:{const _=u.dropdownType===Y.DataValidatorDropdownType.MULTIPLE_LIST,D=async O=>{const x=C.serializeListOptions(O),j={unitId:i,subUnitId:s,range:{startColumn:r,endColumn:r,startRow:n,endRow:n},value:{v:x,p:null,f:null,si:null}};return this._commandService.executeCommand(H.SetRangeValuesCommand.id,j),this._editorBridgeService.isVisible().visible&&await this._commandService.executeCommand(N.SetCellEditVisibleOperation.id,{visible:!1,eventType:A.DeviceInputEventType.Keyboard,unitId:i,keycode:M.KeyCode.ESC}),!_},R=(c==null?void 0:c.renderMode)===o.DataValidationRenderMode.CUSTOM||(c==null?void 0:c.renderMode)===void 0,y=u.getListWithColor(c,i,s),w=C.getDataValidationCellValue(l.getCellRaw(n,r)),k=()=>{this._commandService.executeCommand(_e.id,{ruleId:c.uid}),g==null||g.dispose()},W=y.map(O=>({label:O.label,value:O.label,color:R||O.color?O.color||fe:"transparent"}));f={location:a,type:"list",props:{onChange:O=>D(O),options:W,onEdit:k,defaultValue:w,multiple:_,showEdit:(m=(V=this._configService.getConfig(ot))==null?void 0:V.showEditOnDropdown)!=null?m:!0}};break}case Y.DataValidatorDropdownType.CASCADE:{f={type:"cascader",props:{onChange:D=>{const R={unitId:i,subUnitId:s,range:{startColumn:r,endColumn:r,startRow:n,endRow:n},value:{v:D.join("/"),p:null,f:null,si:null}};return this._commandService.syncExecuteCommand(H.SetRangeValuesCommand.id,R),this._editorBridgeService.isVisible().visible&&this._commandService.syncExecuteCommand(N.SetCellEditVisibleOperation.id,{visible:!1,eventType:A.DeviceInputEventType.Keyboard,unitId:i,keycode:M.KeyCode.ESC}),!0},defaultValue:C.getDataValidationCellValue(l.getCellRaw(n,r)).split("/"),options:JSON.parse((S=c.formula1)!=null?S:"[]")},location:a};break}case Y.DataValidatorDropdownType.COLOR:{f={type:"color",props:{onChange:D=>{const R={unitId:i,subUnitId:s,range:{startColumn:r,endColumn:r,startRow:n,endRow:n},value:{v:D,p:null,f:null,si:null}};return this._commandService.syncExecuteCommand(H.SetRangeValuesCommand.id,R),this._editorBridgeService.isVisible().visible&&this._commandService.syncExecuteCommand(N.SetCellEditVisibleOperation.id,{visible:!1,eventType:A.DeviceInputEventType.Keyboard,unitId:i,keycode:M.KeyCode.ESC}),!0},defaultValue:C.getDataValidationCellValue(l.getCellRaw(n,r))},location:a};break}default:throw new Error("[DataValidationDropdownManagerService]: unknown type!")}if(g=this._cellDropdownManagerService.showDropdown({...f,onHide:()=>{this._activeDropdown=null,this._activeDropdown$.next(null)}}),!g)throw new Error("[DataValidationDropdownManagerService]: cannot show dropdown!");const v=new o.DisposableCollection;v.add(g),v.add({dispose:()=>{var _,D;(D=(_=this._activeDropdown)==null?void 0:_.onHide)==null||D.call(_)}}),this._currentPopup=v}hideDropdown(){this._activeDropdown&&(this._currentPopup&&this._currentPopup.dispose(),this._currentPopup=null,this._activeDropdown=null,this._activeDropdown$.next(null))}showDataValidationDropdown(t,a,n,r,i){const s=this._univerInstanceService.getUnit(t,o.UniverInstanceType.UNIVER_SHEET);if(!s)return;const d=s.getSheetBySheetId(a);if(!d)return;const l=this._dataValidationModel.getRuleByLocation(s.getUnitId(),d.getSheetId(),n,r);if(!l)return;const c=this._dataValidatorRegistryService.getValidatorItem(l.type);if(!c||!c.dropdownType){this.hideDropdown();return}this.showDropdown({location:{workbook:s,worksheet:d,row:n,col:r,unitId:t,subUnitId:a},onHide:i})}};Se=Ft([le(0,o.IUniverInstanceService),le(1,o.Inject(Y.DataValidatorRegistryService)),le(2,M.IZenZoneService),le(3,o.Inject(C.SheetDataValidationModel)),le(4,o.Inject(H.SheetsSelectionsService)),le(5,o.Inject(N.ISheetCellDropdownManagerService)),le(6,o.Inject(C.SheetDataValidationModel)),le(7,o.ICommandService),le(8,N.IEditorBridgeService),le(9,o.Inject(o.Injector)),le(10,o.IConfigService)],Se);const Fe="DataValidationPanel",_e={id:"data-validation.operation.open-validation-panel",type:o.CommandType.OPERATION,handler(e,t){if(!t)return!1;const{ruleId:a,isAdd:n}=t,r=e.get(he),i=e.get(Y.DataValidationModel),s=e.get(o.IUniverInstanceService),d=e.get(M.ISidebarService),l=H.getSheetCommandTarget(s);if(!l)return!1;const{unitId:c,subUnitId:u}=l,g=a?i.getRuleById(c,u,a):void 0;r.open(),r.setActiveRule(g&&{unitId:c,subUnitId:u,rule:g});const h=d.open({id:Fe,header:{title:n?"dataValidation.panel.addTitle":"dataValidation.panel.title"},children:{label:Fe},width:312,onClose:()=>r.close()});return r.setCloseDisposable(h),!0}},st={id:"data-validation.operation.close-validation-panel",type:o.CommandType.OPERATION,handler(e){return e.get(he).close(),!0}},St={id:"data-validation.operation.toggle-validation-panel",type:o.CommandType.OPERATION,handler(e){const t=e.get(o.ICommandService),a=e.get(he);return a.open(),a.isOpen?t.executeCommand(st.id):t.executeCommand(_e.id),!0}},He={type:o.CommandType.OPERATION,id:"sheet.operation.show-data-validation-dropdown",handler(e,t){if(!t)return!1;const a=e.get(Se),{unitId:n,subUnitId:r,row:i,column:s}=t,d=a.activeDropdown,l=d==null?void 0:d.location;return l&&l.unitId===n&&l.subUnitId===r&&l.row===i&&l.col===s||a.showDataValidationDropdown(n,r,i,s),!0}},_t={type:o.CommandType.OPERATION,id:"sheet.operation.hide-data-validation-dropdown",handler(e,t){return t?(e.get(Se).hideDropdown(),!0):!1}},$e={type:o.CommandType.COMMAND,id:"data-validation.command.addRuleAndOpen",handler(e){const t=e.get(o.IUniverInstanceService),a=H.getSheetCommandTarget(t);if(!a)return!1;const{workbook:n,worksheet:r}=a,i=C.createDefaultNewRule(e),s=e.get(o.ICommandService),d=n.getUnitId(),l=r.getSheetId(),c={rule:i,unitId:d,subUnitId:l};return s.syncExecuteCommand(C.AddSheetDataValidationCommand.id,c)?(s.syncExecuteCommand(_e.id,{ruleId:i.uid,isAdd:!0}),!0):!1}};var Nt=Object.getOwnPropertyDescriptor,Bt=(e,t,a,n)=>{for(var r=n>1?void 0:n?Nt(t,a):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(r=s(r)||r);return r},ye=(e,t)=>(a,n)=>t(a,n,e);const ge="SHEET_DATA_VALIDATION_ALERT";let Pe=class extends o.Disposable{constructor(e,t,a,n,r,i){super(),this._hoverManagerService=e,this._cellAlertManagerService=t,this._univerInstanceService=a,this._localeService=n,this._zenZoneService=r,this._dataValidationModel=i,this._init()}_init(){this._initCellAlertPopup(),this._initZenService()}_initCellAlertPopup(){this.disposeWithMe(this._hoverManagerService.currentCell$.pipe(oe.debounceTime(100)).subscribe(e=>{var t;if(e){const a=this._univerInstanceService.getUnit(e.location.unitId,o.UniverInstanceType.UNIVER_SHEET),n=a.getSheetBySheetId(e.location.subUnitId);if(!n)return;const r=this._dataValidationModel.getRuleByLocation(e.location.unitId,e.location.subUnitId,e.location.row,e.location.col);if(!r){this._cellAlertManagerService.removeAlert(ge);return}if(this._dataValidationModel.validator(r,{...e.location,workbook:a,worksheet:n})===o.DataValidationStatus.INVALID){const s=this._cellAlertManagerService.currentAlert.get(ge),d=(t=s==null?void 0:s.alert)==null?void 0:t.location;if(d&&d.row===e.location.row&&d.col===e.location.col&&d.subUnitId===e.location.subUnitId&&d.unitId===e.location.unitId){this._cellAlertManagerService.removeAlert(ge);return}const l=this._dataValidationModel.getValidator(r.type);if(!l){this._cellAlertManagerService.removeAlert(ge);return}this._cellAlertManagerService.showAlert({type:N.CellAlertType.ERROR,title:this._localeService.t("dataValidation.error.title"),message:l==null?void 0:l.getRuleFinalError(r,e.location),location:e.location,width:200,height:74,key:ge});return}}this._cellAlertManagerService.removeAlert(ge)}))}_initZenService(){this.disposeWithMe(this._zenZoneService.visible$.subscribe(e=>{e&&this._cellAlertManagerService.removeAlert(ge)}))}};Pe=Bt([ye(0,o.Inject(N.HoverManagerService)),ye(1,o.Inject(N.CellAlertManagerService)),ye(2,o.IUniverInstanceService),ye(3,o.Inject(o.LocaleService)),ye(4,M.IZenZoneService),ye(5,o.Inject(C.SheetDataValidationModel))],Pe);var Wt=Object.getOwnPropertyDescriptor,Ht=(e,t,a,n)=>{for(var r=n>1?void 0:n?Wt(t,a):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(r=s(r)||r);return r},Xe=(e,t)=>(a,n)=>t(a,n,e);let Ve=class extends o.Disposable{constructor(e,t,a){super(),this._autoFillService=e,this._sheetDataValidationModel=t,this._injector=a,this._initAutoFill()}_initAutoFill(){const e=()=>({redos:[],undos:[]}),t=(n,r)=>{const{source:i,target:s,unitId:d,subUnitId:l}=n,c=this._sheetDataValidationModel.getRuleObjectMatrix(d,l).clone(),u=N.virtualizeDiscreteRanges([i,s]),[g,h]=u.ranges,{mapFunc:f}=u,v={row:g.startRow,col:g.startColumn},T=N.getAutoFillRepeatRange(g,h),V=new o.ObjectMatrix,m=new Set;T.forEach(y=>{const w=y.repeatStartCell,k=y.relativeRange,W={startRow:v.row,startColumn:v.col,endColumn:v.col,endRow:v.row},O={startRow:w.row,startColumn:w.col,endColumn:w.col,endRow:w.row};o.Range.foreach(k,(x,j)=>{const U=o.Rectangle.getPositionRange({startRow:x,startColumn:j,endColumn:j,endRow:x},W),{row:B,col:K}=f(U.startRow,U.startColumn),Z=this._sheetDataValidationModel.getRuleIdByLocation(d,l,B,K)||"",te=o.Rectangle.getPositionRange({startRow:x,startColumn:j,endColumn:j,endRow:x},O),{row:ne,col:re}=f(te.startRow,te.startColumn);V.setValue(ne,re,Z),m.add(Z)})});const S=Array.from(m).map(y=>({id:y,ranges:o.queryObjectMatrix(V,w=>w===y)}));c.addRangeRules(S);const _=c.diff(this._sheetDataValidationModel.getRules(d,l)),{redoMutations:D,undoMutations:R}=C.getDataValidationDiffMutations(d,l,_,this._injector,"patched",r===N.APPLY_TYPE.ONLY_FORMAT);return{undos:R,redos:D}},a={id:C.DATA_VALIDATION_PLUGIN_NAME,onBeforeFillData:n=>{const{source:r,unitId:i,subUnitId:s}=n;for(const d of r.rows)for(const l of r.cols){const c=this._sheetDataValidationModel.getRuleByLocation(i,s,d,l);if(c&&c.type===o.DataValidationType.CHECKBOX){this._autoFillService.setDisableApplyType(N.APPLY_TYPE.SERIES,!0);return}}},onFillData:(n,r,i)=>i===N.APPLY_TYPE.COPY||i===N.APPLY_TYPE.ONLY_FORMAT||i===N.APPLY_TYPE.SERIES?t(n,i):e(),onAfterFillData:()=>{}};this.disposeWithMe(this._autoFillService.addHook(a))}};Ve=Ht([Xe(0,N.IAutoFillService),Xe(1,o.Inject(C.SheetDataValidationModel)),Xe(2,o.Inject(o.Injector))],Ve);var $t=Object.getOwnPropertyDescriptor,Yt=(e,t,a,n)=>{for(var r=n>1?void 0:n?$t(t,a):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(r=s(r)||r);return r},ze=(e,t)=>(a,n)=>t(a,n,e);let Ee=class extends o.Disposable{constructor(t,a,n){super();L(this,"_copyInfo");this._sheetClipboardService=t,this._sheetDataValidationModel=a,this._injector=n,this._initCopyPaste()}_initCopyPaste(){this._sheetClipboardService.addClipboardHook({id:C.DATA_VALIDATION_PLUGIN_NAME,onBeforeCopy:(t,a,n)=>this._collect(t,a,n),onPasteCells:(t,a,n,r)=>{const{copyType:i=N.COPY_TYPE.COPY,pasteType:s}=r,{range:d}=t||{},{range:l,unitId:c,subUnitId:u}=a;return this._generateMutations(l,{copyType:i,pasteType:s,copyRange:d,unitId:c,subUnitId:u})}})}_collect(t,a,n){const r=new o.ObjectMatrix;this._copyInfo={unitId:t,subUnitId:a,matrix:r};const i=this._injector.invoke(l=>H.rangeToDiscreteRange(n,l,t,a));if(!i)return;const{rows:s,cols:d}=i;s.forEach((l,c)=>{d.forEach((u,g)=>{const h=this._sheetDataValidationModel.getRuleIdByLocation(t,a,l,u);r.setValue(c,g,h!=null?h:"")})})}_generateMutations(t,a){if(!this._copyInfo)return{redos:[],undos:[]};if(a.copyType===N.COPY_TYPE.CUT)return this._copyInfo=null,{redos:[],undos:[]};if(!this._copyInfo||!this._copyInfo.matrix.getSizeOf()||!a.copyRange)return{redos:[],undos:[]};if([N.PREDEFINED_HOOK_NAME.SPECIAL_PASTE_COL_WIDTH,N.PREDEFINED_HOOK_NAME.SPECIAL_PASTE_VALUE,N.PREDEFINED_HOOK_NAME.SPECIAL_PASTE_FORMAT,N.PREDEFINED_HOOK_NAME.SPECIAL_PASTE_FORMULA].includes(a.pasteType))return{redos:[],undos:[]};const{unitId:r,subUnitId:i}=this._copyInfo;if(a.unitId!==r||i!==a.subUnitId){const s=this._sheetDataValidationModel.getRuleObjectMatrix(a.unitId,a.subUnitId).clone(),d=new o.ObjectMatrix,l=new Set,{ranges:[c,u],mapFunc:g}=N.virtualizeDiscreteRanges([a.copyRange,t]),h=N.getRepeatRange(c,u,!0),f=new Map;h.forEach(({startRange:m})=>{var S;(S=this._copyInfo)==null||S.matrix.forValue((_,D,R)=>{const y=o.Rectangle.getPositionRange({startRow:_,endRow:_,startColumn:D,endColumn:D},m),w=`${i}-${R}`,k=this._sheetDataValidationModel.getRuleById(r,i,R);!this._sheetDataValidationModel.getRuleById(a.unitId,a.subUnitId,w)&&k&&f.set(w,{...k,uid:w});const{row:W,col:O}=g(y.startRow,y.startColumn);l.add(w),d.setValue(W,O,w)})});const v=Array.from(l).map(m=>({id:m,ranges:o.queryObjectMatrix(d,S=>S===m)}));s.addRangeRules(v);const{redoMutations:T,undoMutations:V}=C.getDataValidationDiffMutations(a.unitId,a.subUnitId,s.diffWithAddition(this._sheetDataValidationModel.getRules(a.unitId,a.subUnitId),f.values()),this._injector,"patched",!1);return{redos:T,undos:V}}else{const s=this._sheetDataValidationModel.getRuleObjectMatrix(r,i).clone(),d=new o.ObjectMatrix,l=new Set,{ranges:[c,u],mapFunc:g}=N.virtualizeDiscreteRanges([a.copyRange,t]);N.getRepeatRange(c,u,!0).forEach(({startRange:V})=>{var m;(m=this._copyInfo)==null||m.matrix.forValue((S,_,D)=>{const R=o.Rectangle.getPositionRange({startRow:S,endRow:S,startColumn:_,endColumn:_},V),{row:y,col:w}=g(R.startRow,R.startColumn);d.setValue(y,w,D),l.add(D)})});const f=Array.from(l).map(V=>({id:V,ranges:o.queryObjectMatrix(d,m=>m===V)}));s.addRangeRules(f);const{redoMutations:v,undoMutations:T}=C.getDataValidationDiffMutations(r,i,s.diff(this._sheetDataValidationModel.getRules(r,i)),this._injector,"patched",!1);return{redos:v,undos:T}}}};Ee=Yt([ze(0,N.ISheetClipboardService),ze(1,o.Inject(C.SheetDataValidationModel)),ze(2,o.Inject(o.Injector))],Ee);var Xt=Object.getOwnPropertyDescriptor,zt=(e,t,a,n)=>{for(var r=n>1?void 0:n?Xt(t,a):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(r=s(r)||r);return r},Ke=(e,t)=>(a,n)=>t(a,n,e);let Me=class extends o.Disposable{constructor(e,t,a){super(),this._localeService=e,this._commandService=t,this._sheetPermissionCheckController=a,this._commandExecutedListener()}_commandExecutedListener(){this.disposeWithMe(this._commandService.beforeCommandExecuted(e=>{e.id===C.AddSheetDataValidationCommand.id&&(this._sheetPermissionCheckController.permissionCheckWithRanges({workbookTypes:[H.WorkbookEditablePermission],rangeTypes:[H.RangeProtectionPermissionEditPoint],worksheetTypes:[H.WorksheetEditPermission,H.WorksheetSetCellStylePermission]})||this._sheetPermissionCheckController.blockExecuteWithoutPermission(this._localeService.t("permission.dialog.setStyleErr"))),e.id===C.UpdateSheetDataValidationRangeCommand.id&&(this._sheetPermissionCheckController.permissionCheckWithRanges({workbookTypes:[H.WorkbookEditablePermission],rangeTypes:[H.RangeProtectionPermissionEditPoint],worksheetTypes:[H.WorksheetEditPermission,H.WorksheetSetCellStylePermission]},e.params.ranges)||this._sheetPermissionCheckController.blockExecuteWithoutPermission(this._localeService.t("permission.dialog.setStyleErr")))}))}};Me=zt([Ke(0,o.Inject(o.LocaleService)),Ke(1,o.ICommandService),Ke(2,o.Inject(H.SheetPermissionCheckController))],Me);const It="sheet.menu.data-validation";function Kt(e){return{id:It,type:M.MenuItemType.SUBITEMS,icon:"DataValidationIcon",tooltip:"dataValidation.title",hidden$:M.getMenuHiddenObservable(e,o.UniverInstanceType.UNIVER_SHEET),disabled$:N.getCurrentRangeDisable$(e,{workbookTypes:[H.WorkbookEditablePermission],worksheetTypes:[H.WorksheetSetCellStylePermission,H.WorksheetEditPermission],rangeTypes:[H.RangeProtectionPermissionEditPoint]})}}function Zt(e){return{id:_e.id,title:"dataValidation.panel.title",type:M.MenuItemType.BUTTON}}function Gt(e){return{id:$e.id,title:"dataValidation.panel.add",type:M.MenuItemType.BUTTON}}const qt={[M.RibbonDataGroup.RULES]:{[It]:{order:0,menuItemFactory:Kt,[_e.id]:{order:0,menuItemFactory:Zt},[$e.id]:{order:1,menuItemFactory:Gt}}}};var Jt=Object.getOwnPropertyDescriptor,Ct=(e,t,a,n)=>{for(var r=n>1?void 0:n?Jt(t,a):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(r=s(r)||r);return r},G=(e,t)=>(a,n)=>t(a,n,e);const yt={tr:{size:6,color:"#fe4b4b"}};let be=class extends o.RxDisposable{constructor(e,t,a,n,r,i,s,d,l,c,u){super(),this._commandService=e,this._menuManagerService=t,this._renderManagerService=a,this._univerInstanceService=n,this._autoHeightController=r,this._dropdownManagerService=i,this._sheetDataValidationModel=s,this._dataValidatorRegistryService=d,this._sheetInterceptorService=l,this._dataValidationCacheService=c,this._editorBridgeService=u,this._initMenu(),this._initDropdown(),this._initViewModelIntercept(),this._initAutoHeight()}_initMenu(){this._menuManagerService.mergeMenu(qt)}_initDropdown(){this._editorBridgeService&&this.disposeWithMe(this._editorBridgeService.visible$.subscribe(e=>{var a;if(!e.visible){((a=this._dropdownManagerService.activeDropdown)==null?void 0:a.trigger)==="editor-bridge"&&this._dropdownManagerService.hideDropdown();return}const t=this._editorBridgeService.getEditCellState();if(t){const{unitId:n,sheetId:r,row:i,column:s}=t,d=this._univerInstanceService.getUniverSheetInstance(n);if(!d)return;const l=this._sheetDataValidationModel.getRuleByLocation(n,r,i,s);if(!l)return;const c=this._dataValidatorRegistryService.getValidatorItem(l.type);if(!(c!=null&&c.dropdownType))return;const u=d.getActiveSheet();if(!u)return;const g=this._dropdownManagerService.activeDropdown,h=g==null?void 0:g.location;if(h&&h.unitId===n&&h.subUnitId===r&&h.row===i&&h.col===s)return;this._dropdownManagerService.showDropdown({location:{unitId:n,subUnitId:r,row:i,col:s,workbook:d,worksheet:u},trigger:"editor-bridge",closeOnOutSide:!1})}}))}_initViewModelIntercept(){this.disposeWithMe(this._sheetInterceptorService.intercept(H.INTERCEPTOR_POINT.CELL_CONTENT,{effect:o.InterceptorEffectEnum.Style,priority:H.InterceptCellContentPriority.DATA_VALIDATION,handler:(e,t,a)=>{var m,S,_,D,R;const{row:n,col:r,unitId:i,subUnitId:s,workbook:d,worksheet:l}=t,c=this._sheetDataValidationModel.getRuleIdByLocation(i,s,n,r);if(!c)return a(e);const u=this._sheetDataValidationModel.getRuleById(i,s,c);if(!u)return a(e);const g=(m=this._dataValidationCacheService.getValue(i,s,n,r))!=null?m:o.DataValidationStatus.VALID,h=this._dataValidatorRegistryService.getValidatorItem(u.type),f=t.rawData;let v;const T={get value(){var y;return v!==void 0||(v=(y=C.getCellValueOrigin(f))!=null?y:null),v}},V={get value(){var y;return`${(y=T.value)!=null?y:""}`}};return(!e||e===t.rawData)&&(e={...t.rawData}),e.markers={...e==null?void 0:e.markers,...g===o.DataValidationStatus.INVALID?yt:null},e.customRender=[...(S=e==null?void 0:e.customRender)!=null?S:[],...h!=null&&h.canvasRender?[h.canvasRender]:[]],e.fontRenderExtension={...e==null?void 0:e.fontRenderExtension,isSkip:((_=e==null?void 0:e.fontRenderExtension)==null?void 0:_.isSkip)||((D=h==null?void 0:h.skipDefaultFontRender)==null?void 0:D.call(h,u,T.value,t))},e.interceptorStyle={...e==null?void 0:e.interceptorStyle,...h==null?void 0:h.getExtraStyle(u,V.value,{get style(){const y=d.getStyles();return(typeof(e==null?void 0:e.s)=="string"?y.get(e==null?void 0:e.s):e==null?void 0:e.s)||{}}},n,r)},e.interceptorAutoHeight=()=>{var W,O,x,j,U,B;const y=(O=(W=this._renderManagerService.getRenderById(i))==null?void 0:W.with(N.SheetSkeletonManagerService).getSkeletonParam(s))==null?void 0:O.skeleton;if(!y)return;const w=y.worksheet.getMergedCell(n,r),k={data:e,style:y.getStyles().getStyleByCell(e),primaryWithCoord:y.getCellWithCoordByIndex((x=w==null?void 0:w.startRow)!=null?x:n,(j=w==null?void 0:w.startColumn)!=null?j:r),unitId:i,subUnitId:s,row:n,col:r,workbook:d,worksheet:l};return(B=(U=h==null?void 0:h.canvasRender)==null?void 0:U.calcCellAutoHeight)==null?void 0:B.call(U,k)},e.interceptorAutoWidth=()=>{var W,O,x,j,U,B;const y=(O=(W=this._renderManagerService.getRenderById(i))==null?void 0:W.with(N.SheetSkeletonManagerService).getSkeletonParam(s))==null?void 0:O.skeleton;if(!y)return;const w=y.worksheet.getMergedCell(n,r),k={data:e,style:y.getStyles().getStyleByCell(e),primaryWithCoord:y.getCellWithCoordByIndex((x=w==null?void 0:w.startRow)!=null?x:n,(j=w==null?void 0:w.startColumn)!=null?j:r),unitId:i,subUnitId:s,row:n,col:r,workbook:d,worksheet:l};return(B=(U=h==null?void 0:h.canvasRender)==null?void 0:U.calcCellAutoWidth)==null?void 0:B.call(U,k)},e.coverable=((R=e==null?void 0:e.coverable)!=null?R:!0)&&!(u.type===o.DataValidationType.LIST||u.type===o.DataValidationType.LIST_MULTIPLE),a(e)}}))}_initAutoHeight(){this._sheetDataValidationModel.ruleChange$.pipe(oe.filter(e=>e.source==="command"),oe.bufferTime(100)).subscribe(e=>{if(e.length===0)return;const t=[];if(e.forEach(a=>{var n;(a.rule.type===o.DataValidationType.LIST_MULTIPLE||a.rule.type===o.DataValidationType.LIST)&&(n=a.rule)!=null&&n.ranges&&t.push(...a.rule.ranges)}),t.length){const a=this._autoHeightController.getUndoRedoParamsOfAutoHeight(t);o.sequenceExecute(a.redos,this._commandService)}})}};be=Ct([G(0,o.ICommandService),G(1,M.IMenuManagerService),G(2,A.IRenderManagerService),G(3,o.IUniverInstanceService),G(4,o.Inject(N.AutoHeightController)),G(5,o.Inject(Se)),G(6,o.Inject(C.SheetDataValidationModel)),G(7,o.Inject(Y.DataValidatorRegistryService)),G(8,o.Inject(H.SheetInterceptorService)),G(9,o.Inject(C.DataValidationCacheService)),G(10,o.Optional(N.IEditorBridgeService))],be);let ht=class extends o.RxDisposable{constructor(e,t,a,n,r,i,s){super(),this._commandService=e,this._renderManagerService=t,this._autoHeightController=a,this._dataValidatorRegistryService=n,this._sheetInterceptorService=r,this._sheetDataValidationModel=i,this._dataValidationCacheService=s,this._initViewModelIntercept(),this._initAutoHeight()}_initViewModelIntercept(){this.disposeWithMe(this._sheetInterceptorService.intercept(H.INTERCEPTOR_POINT.CELL_CONTENT,{effect:o.InterceptorEffectEnum.Style,priority:H.InterceptCellContentPriority.DATA_VALIDATION,handler:(e,t,a)=>{var V,m,S,_,D;const{row:n,col:r,unitId:i,subUnitId:s,workbook:d,worksheet:l}=t,c=this._sheetDataValidationModel.getRuleIdByLocation(i,s,n,r);if(!c)return a(e);const u=this._sheetDataValidationModel.getRuleById(i,s,c);if(!u)return a(e);const g=(V=this._dataValidationCacheService.getValue(i,s,n,r))!=null?V:o.DataValidationStatus.VALID,h=this._dataValidatorRegistryService.getValidatorItem(u.type),f=l.getCellRaw(n,r),v=C.getCellValueOrigin(f),T=`${v!=null?v:""}`;return(!e||e===t.rawData)&&(e={...t.rawData}),e.markers={...e==null?void 0:e.markers,...g===o.DataValidationStatus.INVALID?yt:null},e.customRender=[...(m=e==null?void 0:e.customRender)!=null?m:[],...h!=null&&h.canvasRender?[h.canvasRender]:[]],e.fontRenderExtension={...e==null?void 0:e.fontRenderExtension,isSkip:((S=e==null?void 0:e.fontRenderExtension)==null?void 0:S.isSkip)||((_=h==null?void 0:h.skipDefaultFontRender)==null?void 0:_.call(h,u,v,t))},e.interceptorStyle={...e==null?void 0:e.interceptorStyle,...h==null?void 0:h.getExtraStyle(u,T,{get style(){const R=d.getStyles();return(typeof(e==null?void 0:e.s)=="string"?R.get(e==null?void 0:e.s):e==null?void 0:e.s)||{}}},n,r)},e.interceptorAutoHeight=()=>{var k,W,O,x,j,U;const R=(W=(k=this._renderManagerService.getRenderById(i))==null?void 0:k.with(N.SheetSkeletonManagerService).getSkeletonParam(s))==null?void 0:W.skeleton;if(!R)return;const y=R.worksheet.getMergedCell(n,r),w={data:e,style:R.getStyles().getStyleByCell(e),primaryWithCoord:R.getCellWithCoordByIndex((O=y==null?void 0:y.startRow)!=null?O:n,(x=y==null?void 0:y.startColumn)!=null?x:r),unitId:i,subUnitId:s,row:n,col:r,workbook:d,worksheet:l};return(U=(j=h==null?void 0:h.canvasRender)==null?void 0:j.calcCellAutoHeight)==null?void 0:U.call(j,w)},e.interceptorAutoWidth=()=>{var k,W,O,x,j,U;const R=(W=(k=this._renderManagerService.getRenderById(i))==null?void 0:k.with(N.SheetSkeletonManagerService).getSkeletonParam(s))==null?void 0:W.skeleton;if(!R)return;const y=R.worksheet.getMergedCell(n,r),w={data:e,style:R.getStyles().getStyleByCell(e),primaryWithCoord:R.getCellWithCoordByIndex((O=y==null?void 0:y.startRow)!=null?O:n,(x=y==null?void 0:y.startColumn)!=null?x:r),unitId:i,subUnitId:s,row:n,col:r,workbook:d,worksheet:l};return(U=(j=h==null?void 0:h.canvasRender)==null?void 0:j.calcCellAutoWidth)==null?void 0:U.call(j,w)},e.coverable=((D=e==null?void 0:e.coverable)!=null?D:!0)&&!(u.type===o.DataValidationType.LIST||u.type===o.DataValidationType.LIST_MULTIPLE),a(e)}}))}_initAutoHeight(){this._sheetDataValidationModel.ruleChange$.pipe(oe.filter(e=>e.source==="command"),oe.bufferTime(16)).subscribe(e=>{const t=[];if(e.forEach(a=>{var n;(a.rule.type===o.DataValidationType.LIST_MULTIPLE||a.rule.type===o.DataValidationType.LIST)&&(n=a.rule)!=null&&n.ranges&&t.push(...a.rule.ranges)}),t.length){const a=this._autoHeightController.getUndoRedoParamsOfAutoHeight(t);o.sequenceExecute(a.redos,this._commandService)}})}};ht=Ct([G(0,o.ICommandService),G(1,A.IRenderManagerService),G(2,o.Inject(N.AutoHeightController)),G(3,o.Inject(Y.DataValidatorRegistryService)),G(4,o.Inject(H.SheetInterceptorService)),G(5,o.Inject(C.SheetDataValidationModel)),G(6,o.Inject(C.DataValidationCacheService))],ht);var Qt=Object.getOwnPropertyDescriptor,en=(e,t,a,n)=>{for(var r=n>1?void 0:n?Qt(t,a):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(r=s(r)||r);return r},pt=(e,t)=>(a,n)=>t(a,n,e);let ke=class extends o.Disposable{constructor(e,t,a){super(),this._context=e,this._sheetDataValidationModel=t,this._sheetSkeletonManagerService=a,this._initSkeletonChange()}_initSkeletonChange(){const e=t=>{var n;if(!t.length)return;const a=new Set;t.forEach(r=>{a.add(r.subUnitId)}),a.forEach(r=>{var i;(i=this._sheetSkeletonManagerService.getSkeletonParam(r))==null||i.skeleton.makeDirty(!0)}),(n=this._context.mainComponent)==null||n.makeForceDirty()};this.disposeWithMe(this._sheetDataValidationModel.validStatusChange$.pipe(o.bufferDebounceTime(16)).subscribe(e))}};ke=en([pt(1,o.Inject(C.SheetDataValidationModel)),pt(2,o.Inject(N.SheetSkeletonManagerService))],ke);var ie=function(){return ie=Object.assign||function(e){for(var t,a=1,n=arguments.length;a<n;a++){t=arguments[a];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},ie.apply(this,arguments)},tn=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(a[n[r]]=e[n[r]]);return a},Ie=b.forwardRef(function(e,t){var a=e.icon,n=e.id,r=e.className,i=e.extend,s=tn(e,["icon","id","className","extend"]),d="univerjs-icon univerjs-icon-".concat(n," ").concat(r||"").trim(),l=b.useRef("_".concat(rn()));return Rt(a,"".concat(n),{defIds:a.defIds,idSuffix:l.current},ie({ref:t,className:d},s),i)});function Rt(e,t,a,n,r){return b.createElement(e.tag,ie(ie({key:t},nn(e,a,r)),n),(an(e,a).children||[]).map(function(i,s){return Rt(i,"".concat(t,"-").concat(e.tag,"-").concat(s),a,void 0,r)}))}function nn(e,t,a){var n=ie({},e.attrs);a!=null&&a.colorChannel1&&n.fill==="colorChannel1"&&(n.fill=a.colorChannel1),e.tag==="mask"&&n.id&&(n.id=n.id+t.idSuffix),Object.entries(n).forEach(function(i){var s=i[0],d=i[1];s==="mask"&&typeof d=="string"&&(n[s]=d.replace(/url\(#(.*)\)/,"url(#$1".concat(t.idSuffix,")")))});var r=t.defIds;return!r||r.length===0||(e.tag==="use"&&n["xlink:href"]&&(n["xlink:href"]=n["xlink:href"]+t.idSuffix),Object.entries(n).forEach(function(i){var s=i[0],d=i[1];typeof d=="string"&&(n[s]=d.replace(/url\(#(.*)\)/,"url(#$1".concat(t.idSuffix,")")))})),n}function an(e,t){var a,n=t.defIds;return!n||n.length===0?e:e.tag==="defs"&&(!((a=e.children)===null||a===void 0)&&a.length)?ie(ie({},e),{children:e.children.map(function(r){return typeof r.attrs.id=="string"&&n&&n.includes(r.attrs.id)?ie(ie({},r),{attrs:ie(ie({},r.attrs),{id:r.attrs.id+t.idSuffix})}):r})}):e}function rn(){return Math.random().toString(36).substring(2,8)}Ie.displayName="UniverIcon";var on={tag:"svg",attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 17 16",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M1.4917 3.07803C1.4917 2.19437 2.20804 1.47803 3.0917 1.47803H5.6917C6.57536 1.47803 7.2917 2.19437 7.2917 3.07803V5.67803C7.2917 6.56168 6.57535 7.27803 5.6917 7.27803H3.0917C2.20804 7.27803 1.4917 6.56168 1.4917 5.67803V3.07803ZM3.0917 2.67803C2.87078 2.67803 2.6917 2.85711 2.6917 3.07803V5.67803C2.6917 5.89894 2.87079 6.07803 3.0917 6.07803H5.6917C5.91261 6.07803 6.0917 5.89894 6.0917 5.67803V3.07803C6.0917 2.85711 5.91261 2.67803 5.6917 2.67803H3.0917Z",fillRule:"evenodd",clipRule:"evenodd"}},{tag:"path",attrs:{fill:"currentColor",d:"M14.6175 2.45279C14.8518 2.68711 14.8518 3.06701 14.6175 3.30132L11.6151 6.30365C11.3957 6.52307 11.0451 6.53897 10.8067 6.34031L8.80915 4.67566C8.55458 4.46352 8.52019 4.08518 8.73233 3.83062C8.94447 3.57605 9.32281 3.54166 9.57737 3.7538L11.154 5.06767L13.769 2.45278C14.0033 2.21847 14.3832 2.21848 14.6175 2.45279Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M14.1175 9.19746C14.3518 9.43178 14.3518 9.81168 14.1175 10.046L12.5418 11.6217L14.1175 13.1975C14.3518 13.4318 14.3518 13.8117 14.1175 14.046C13.8832 14.2803 13.5033 14.2803 13.269 14.046L11.6933 12.4703L10.1175 14.046C9.88321 14.2803 9.50331 14.2803 9.269 14.046C9.03468 13.8117 9.03468 13.4318 9.269 13.1975L10.8447 11.6217L9.269 10.046C9.03468 9.81168 9.03468 9.43178 9.269 9.19746C9.50331 8.96315 9.88321 8.96315 10.1175 9.19746L11.6933 10.7732L13.269 9.19746C13.5033 8.96315 13.8832 8.96315 14.1175 9.19746Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M3.0917 8.72168C2.20804 8.72168 1.4917 9.43802 1.4917 10.3217V12.9217C1.4917 13.8053 2.20804 14.5217 3.0917 14.5217H5.6917C6.57535 14.5217 7.2917 13.8053 7.2917 12.9217V10.3217C7.2917 9.43802 6.57536 8.72168 5.6917 8.72168H3.0917ZM2.6917 10.3217C2.6917 10.1008 2.87078 9.92168 3.0917 9.92168H5.6917C5.91261 9.92168 6.0917 10.1008 6.0917 10.3217V12.9217C6.0917 13.1426 5.91261 13.3217 5.6917 13.3217H3.0917C2.87079 13.3217 2.6917 13.1426 2.6917 12.9217V10.3217Z",fillRule:"evenodd",clipRule:"evenodd"}}]},Dt=b.forwardRef(function(e,t){return b.createElement(Ie,Object.assign({},e,{id:"data-validation-icon",ref:t,icon:on}))});Dt.displayName="DataValidationIcon";var sn={tag:"svg",attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 16",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M5.3313 1.4667C5.3313 1.13533 5.59993 0.866699 5.9313 0.866699H10.069C10.4004 0.866699 10.669 1.13533 10.669 1.4667C10.669 1.79807 10.4004 2.0667 10.069 2.0667H5.9313C5.59993 2.0667 5.3313 1.79807 5.3313 1.4667Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M1.09985 3.64443C1.09985 3.31306 1.36848 3.04443 1.69985 3.04443H14.2999C14.6312 3.04443 14.8999 3.31306 14.8999 3.64443C14.8999 3.9758 14.6312 4.24443 14.2999 4.24443H1.69985C1.36848 4.24443 1.09985 3.9758 1.09985 3.64443Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M6.12398 8.30171C6.35829 8.0674 6.73819 8.0674 6.97251 8.30171L8.00007 9.32928L9.02764 8.30171C9.26195 8.0674 9.64185 8.0674 9.87617 8.30171C10.1105 8.53603 10.1105 8.91593 9.87617 9.15024L8.8486 10.1778L9.87617 11.2054C10.1105 11.4397 10.1105 11.8196 9.87617 12.0539C9.64185 12.2882 9.26195 12.2882 9.02764 12.0539L8.00007 11.0263L6.97251 12.0539C6.73819 12.2882 6.35829 12.2882 6.12398 12.0539C5.88966 11.8196 5.88966 11.4397 6.12398 11.2054L7.15154 10.1778L6.12398 9.15024C5.88966 8.91593 5.88966 8.53603 6.12398 8.30171Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M4.75332 5.22217C3.86966 5.22217 3.15332 5.93851 3.15332 6.82217V12.5331C3.15332 13.9691 4.31738 15.1332 5.75332 15.1332H10.2465C11.6825 15.1332 12.8465 13.9691 12.8465 12.5331V6.82217C12.8465 5.93851 12.1302 5.22217 11.2465 5.22217H4.75332ZM4.35332 6.82217C4.35332 6.60125 4.53241 6.42217 4.75332 6.42217H11.2465C11.4674 6.42217 11.6465 6.60125 11.6465 6.82217V12.5331C11.6465 13.3063 11.0197 13.9332 10.2465 13.9332H5.75332C4.98012 13.9332 4.35332 13.3063 4.35332 12.5331V6.82217Z",fillRule:"evenodd",clipRule:"evenodd"}}]},lt=b.forwardRef(function(e,t){return b.createElement(Ie,Object.assign({},e,{id:"delete-icon",ref:t,icon:sn}))});lt.displayName="DeleteIcon";var ln={tag:"svg",attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 16",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M8.6 1.99991C8.60001 1.66854 8.33138 1.39991 8.00001 1.3999C7.66864 1.3999 7.40001 1.66853 7.4 1.9999L7.39996 7.3999H1.9999C1.66853 7.3999 1.3999 7.66853 1.3999 7.9999C1.3999 8.33127 1.66853 8.5999 1.9999 8.5999H7.39995L7.3999 13.9999C7.3999 14.3313 7.66853 14.5999 7.9999 14.5999C8.33127 14.5999 8.5999 14.3313 8.5999 13.9999L8.59995 8.5999H13.9999C14.3313 8.5999 14.5999 8.33127 14.5999 7.9999C14.5999 7.66853 14.3313 7.3999 13.9999 7.3999H8.59996L8.6 1.99991Z"}}]},wt=b.forwardRef(function(e,t){return b.createElement(Ie,Object.assign({},e,{id:"increase-icon",ref:t,icon:ln}))});wt.displayName="IncreaseIcon";var dn={tag:"svg",attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 16",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M11.3536 6.14645C11.5488 6.34171 11.5488 6.65829 11.3536 6.85355L8.35355 9.85355C8.15829 10.0488 7.84171 10.0488 7.64645 9.85355L4.64645 6.85355C4.45118 6.65829 4.45118 6.34171 4.64645 6.14645C4.84171 5.95118 5.15829 5.95118 5.35355 6.14645L8 8.79289L10.6464 6.14645C10.8417 5.95118 11.1583 5.95118 11.3536 6.14645Z",fillRule:"evenodd",clipRule:"evenodd"}}]},dt=b.forwardRef(function(e,t){return b.createElement(Ie,Object.assign({},e,{id:"more-down-icon",ref:t,icon:dn}))});dt.displayName="MoreDownIcon";var cn={tag:"svg",attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 16",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M4.64645 9.85355C4.45118 9.65829 4.45118 9.34171 4.64645 9.14645L7.64645 6.14645C7.84171 5.95118 8.15829 5.95118 8.35355 6.14645L11.3536 9.14645C11.5488 9.34171 11.5488 9.65829 11.3536 9.85355C11.1583 10.0488 10.8417 10.0488 10.6464 9.85355L8 7.20711L5.35355 9.85355C5.15829 10.0488 4.84171 10.0488 4.64645 9.85355Z",fillRule:"evenodd",clipRule:"evenodd"}}]},Vt=b.forwardRef(function(e,t){return b.createElement(Ie,Object.assign({},e,{id:"more-up-icon",ref:t,icon:cn}))});Vt.displayName="MoreUpIcon";var un={tag:"svg",attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 16",width:"1em",height:"1em"},children:[{tag:"mask",attrs:{id:"mask0_622_8",width:16,height:16,x:0,y:0,maskUnits:"userSpaceOnUse"},children:[{tag:"path",attrs:{fill:"#D9D9D9",d:"M0 0H16V16H0z"}}]},{tag:"g",attrs:{fill:"currentColor",mask:"url(#mask0_622_8)"},children:[{tag:"path",attrs:{d:"M6 5C6.55228 5 7 4.55228 7 4C7 3.44772 6.55228 3 6 3C5.44772 3 5 3.44772 5 4C5 4.55228 5.44772 5 6 5Z"}},{tag:"path",attrs:{d:"M6 9C6.55228 9 7 8.55229 7 8C7 7.44772 6.55228 7 6 7C5.44772 7 5 7.44772 5 8C5 8.55229 5.44772 9 6 9Z"}},{tag:"path",attrs:{d:"M7 12C7 12.5523 6.55228 13 6 13C5.44772 13 5 12.5523 5 12C5 11.4477 5.44772 11 6 11C6.55228 11 7 11.4477 7 12Z"}},{tag:"path",attrs:{d:"M10 5C10.5523 5 11 4.55228 11 4C11 3.44772 10.5523 3 10 3C9.44771 3 9 3.44772 9 4C9 4.55228 9.44771 5 10 5Z"}},{tag:"path",attrs:{d:"M11 8C11 8.55229 10.5523 9 10 9C9.44771 9 9 8.55229 9 8C9 7.44772 9.44771 7 10 7C10.5523 7 11 7.44772 11 8Z"}},{tag:"path",attrs:{d:"M10 13C10.5523 13 11 12.5523 11 12C11 11.4477 10.5523 11 10 11C9.44771 11 9 11.4477 9 12C9 12.5523 9.44771 13 10 13Z"}}]}]},Et=b.forwardRef(function(e,t){return b.createElement(Ie,Object.assign({},e,{id:"sequence-icon",ref:t,icon:un}))});Et.displayName="SequenceIcon";function hn(e){var c;const t=M.useDependency(o.LocaleService),a=M.useDependency(M.ComponentManager),{value:n,onChange:r,extraComponent:i}=e,[s,d]=b.useState(!1),l=i?a.get(i):null;return p.jsxs(p.Fragment,{children:[p.jsxs("div",{className:"univer-mb-3 univer-flex univer-cursor-pointer univer-items-center univer-text-sm univer-text-gray-900 dark:!univer-text-white",onClick:()=>d(!s),children:[t.t("dataValidation.panel.options"),s?p.jsx(Vt,{className:"univer-ml-1"}):p.jsx(dt,{className:"univer-ml-1"})]}),s&&p.jsxs(p.Fragment,{children:[l?p.jsx(l,{value:n,onChange:r}):null,p.jsx(E.FormLayout,{label:t.t("dataValidation.panel.invalid"),children:p.jsxs(E.RadioGroup,{value:`${(c=n.errorStyle)!=null?c:o.DataValidationErrorStyle.WARNING}`,onChange:u=>r({...n,errorStyle:+u}),children:[p.jsx(E.Radio,{value:`${o.DataValidationErrorStyle.WARNING}`,children:t.t("dataValidation.panel.showWarning")}),p.jsx(E.Radio,{value:`${o.DataValidationErrorStyle.STOP}`,children:t.t("dataValidation.panel.rejectInput")})]})}),p.jsx(E.FormLayout,{label:t.t("dataValidation.panel.messageInfo"),children:p.jsx(E.Checkbox,{checked:n.showErrorMessage,onChange:()=>r({...n,showErrorMessage:!n.showErrorMessage}),children:t.t("dataValidation.panel.showInfo")})}),n.showErrorMessage?p.jsx(E.FormLayout,{children:p.jsx(E.Input,{value:n.error,onChange:u=>r({...n,error:u})})}):null]})]})}const pn=e=>o.debounce(async(t,a,n,r)=>{const i=await e.executeCommand(t,a,n);r==null||r(i)},1e3);function gn(e,t,a){var n,r,i,s;return t?((r=(n=e.getUnit(t))==null?void 0:n.getSheetBySheetName(a))==null?void 0:r.getSheetId())||"":((s=(i=e.getCurrentUnitForType(o.UniverInstanceType.UNIVER_SHEET))==null?void 0:i.getSheetBySheetName(a))==null?void 0:s.getSheetId())||""}function vn(){var P,$;const[e,t]=b.useState(0),a=M.useDependency(he),n=M.useObservable(a.activeRule$,a.activeRule),{unitId:r,subUnitId:i,rule:s}=n||{},d=s.uid,l=M.useDependency(Y.DataValidatorRegistryService),c=M.useDependency(o.IUniverInstanceService),u=M.useDependency(M.ComponentManager),g=M.useDependency(o.ICommandService),h=M.useDependency(Y.DataValidationModel),f=M.useDependency(o.LocaleService),[v,T]=b.useState(s),V=l.getValidatorItem(v.type),[m,S]=b.useState(!1),_=l.getValidatorsByScope(Y.DataValidatorRegistryScope.SHEET),[D,R]=b.useState(()=>v.ranges.map(I=>({unitId:"",sheetId:"",range:I}))),y=b.useMemo(()=>pn(g),[g]),[w,k]=b.useState(!1),[W,O]=b.useState(!1),x=b.useRef(null),j=M.useDependency(H.SheetsSelectionsService);if(b.useEffect(()=>()=>{const I=j.getCurrentLastSelection();I&&j.setSelections([I])},[j]),b.useEffect(()=>{g.onCommandExecuted(I=>{(I.id===o.UndoCommand.id||I.id===o.RedoCommand.id)&&setTimeout(()=>{const F=h.getRuleById(r,i,d);t(X=>X+1),F&&(T(F),R(F.ranges.map(X=>({unitId:"",sheetId:"",range:X}))))},20)})},[g,h,d,i,r]),!V)return null;const U=V.operators,B=V.operatorNames,K=v.operator?Y.TWO_FORMULA_OPERATOR_COUNT.includes(v.operator):!1,Z=()=>{var I,F,X;(F=(I=x.current)==null?void 0:I.editor)!=null&&F.isFocus()&&te((X=x.current)==null?void 0:X.getValue()),!(!v.ranges.length||w)&&(V.validatorFormula(v,r,i).success?a.setActiveRule(null):S(!0))},te=M.useEvent(I=>{const F=I.split(",").filter(Boolean).map(et.deserializeRangeWithSheet).map(ee=>{const ct=ee.sheetName;if(ct){const bt=gn(c,ee.unitId,ct);return{...ee,sheetId:bt}}return{...ee,sheetId:""}});if(o.isUnitRangesEqual(F,D))return;R(F);const X=F.filter(ee=>(!ee.unitId||ee.unitId===r)&&(!ee.sheetId||ee.sheetId===i)).map(ee=>ee.range);if(T({...v,ranges:X}),X.length===0)return;const pe={unitId:r,subUnitId:i,ruleId:d,ranges:X};y(C.UpdateSheetDataValidationRangeCommand.id,pe)}),ne=I=>{if(o.shallowEqual(I,Y.getRuleSetting(v)))return;T({...v,...I});const F={unitId:r,subUnitId:i,ruleId:d,setting:I};y(C.UpdateSheetDataValidationSettingCommand.id,F,void 0)},re=async()=>{await g.executeCommand(C.RemoveSheetDataValidationCommand.id,{ruleId:d,unitId:r,subUnitId:i}),a.setActiveRule(null)},se={type:v.type,operator:v.operator,formula1:v.formula1,formula2:v.formula2,allowBlank:v.allowBlank},q=I=>{const F=l.getValidatorItem(I);if(!F)return;const X=F.operators,pe=h.getRuleById(r,i,d),ee=I===(pe==null?void 0:pe.type)||I.includes("list")&&(pe!=null&&pe.type.includes("list"))?{...pe,type:I}:{...v,type:I,operator:X[0],formula1:void 0,formula2:void 0};T(ee),g.executeCommand(C.UpdateSheetDataValidationSettingCommand.id,{unitId:r,subUnitId:i,ruleId:v.uid,setting:Y.getRuleSetting(ee)})},J=u.get(V.formulaInput),ce=b.useMemo(()=>D.map(I=>et.serializeRange(I.range)).join(","),[]),Q=Y.getRuleOptions(v),z=I=>{o.shallowEqual(I,Y.getRuleOptions(v))||(T({...v,...I}),y(C.UpdateSheetDataValidationOptionsCommand.id,{unitId:r,subUnitId:i,ruleId:d,options:I}))},ae=U.length&&!v.operator;return p.jsxs("div",{"data-u-comp":"data-validation-detail",className:"univer-py-2",children:[p.jsx(E.FormLayout,{label:f.t("dataValidation.panel.range"),error:!v.ranges.length||w?f.t("dataValidation.panel.rangeError"):"",children:p.jsx(it.RangeSelector,{selectorRef:x,unitId:r,subUnitId:i,initialValue:ce,onChange:(I,F)=>{var X;!W&&((X=x.current)!=null&&X.verify())&&te(F)},onFocusChange:(I,F)=>{var X;O(I),!I&&F&&((X=x.current)!=null&&X.verify())&&te(F)},onVerify:I=>k(!I)})}),p.jsx(E.FormLayout,{label:f.t("dataValidation.panel.type"),children:p.jsx(E.Select,{className:"univer-w-full",value:v.type,options:(P=_==null?void 0:_.sort((I,F)=>I.order-F.order))==null?void 0:P.map(I=>({label:f.t(I.title),value:I.id})),onChange:q})}),U!=null&&U.length?p.jsx(E.FormLayout,{label:f.t("dataValidation.panel.operator"),children:p.jsx(E.Select,{className:"univer-w-full",value:`${v.operator}`,options:[{value:"",label:f.t("dataValidation.operators.legal")},...U.map((I,F)=>({value:`${I}`,label:B[F]}))],onChange:I=>{ne({...se,operator:I})}})}):null,J&&!ae?p.jsx(J,{isTwoFormula:K,value:{formula1:v.formula1,formula2:v.formula2},onChange:I=>{ne({...se,...I})},showError:m,validResult:V.validatorFormula(v,r,i),unitId:r,subUnitId:i,ruleId:d},e+v.type):null,p.jsx(E.FormLayout,{children:p.jsx(E.Checkbox,{checked:($=v.allowBlank)!=null?$:!0,onChange:()=>{var I;return ne({...se,allowBlank:!((I=v.allowBlank)==null||I)})},children:f.t("dataValidation.panel.allowBlank")})}),p.jsx(hn,{value:Q,onChange:z,extraComponent:V.optionsInput}),p.jsxs("div",{className:"univer-mt-5 univer-flex univer-flex-row univer-justify-end",children:[p.jsx(E.Button,{className:"univer-ml-3",onClick:re,children:f.t("dataValidation.panel.removeRule")}),p.jsx(E.Button,{className:"univer-ml-3",variant:"primary",onClick:Z,children:f.t("dataValidation.panel.done")})]})]})}const mn=e=>{const{rule:t,onClick:a,unitId:n,subUnitId:r,disable:i}=e,s=M.useDependency(Y.DataValidatorRegistryService),d=M.useDependency(o.ICommandService),l=M.useDependency(N.IMarkSelectionService),c=s.getValidatorItem(t.type),u=b.useRef(void 0),[g,h]=b.useState(!1),f=M.useDependency(o.ThemeService),v=M.useObservable(f.currentTheme$),T=b.useMemo(()=>{var R;const m=f.getColorFromTheme("primary.600"),S=f.getColorFromTheme("loop-color.2"),_=(R=f.getColorFromTheme(S))!=null?R:m,D=new o.ColorKit(_).toRgb();return{fill:`rgba(${D.r}, ${D.g}, ${D.b}, 0.1)`,stroke:_}},[v]),V=m=>{d.executeCommand(C.RemoveSheetDataValidationCommand.id,{ruleId:t.uid,unitId:n,subUnitId:r}),m.stopPropagation()};return b.useEffect(()=>()=>{var m;u.current&&((m=u.current)==null||m.forEach(S=>{S&&l.removeShape(S)}))},[l]),p.jsxs("div",{className:E.clsx(`
                  univer-bg-secondary univer-relative univer--ml-2 univer--mr-2 univer-box-border univer-flex
                  univer-w-[287px] univer-cursor-pointer univer-flex-col univer-justify-between univer-overflow-hidden
                  univer-rounded-md univer-p-2 univer-pr-9
                `,{"hover:univer-bg-gray-50 dark:hover:!univer-bg-gray-700":!i,"univer-opacity-50":i}),onClick:a,onMouseEnter:()=>{i||(h(!0),u.current=t.ranges.map(m=>l.addShape({range:m,style:T,primary:null})))},onMouseLeave:()=>{var m;h(!1),(m=u.current)==null||m.forEach(S=>{S&&l.removeShape(S)}),u.current=void 0},children:[p.jsx("div",{className:"univer-truncate univer-text-sm univer-font-medium univer-leading-[22px] univer-text-gray-900 dark:!univer-text-white",children:c==null?void 0:c.generateRuleName(t)}),p.jsx("div",{className:"univer-text-secondary univer-truncate univer-text-xs univer-leading-[18px] dark:!univer-text-gray-300",children:t.ranges.map(m=>et.serializeRange(m)).join(",")}),g?p.jsx("div",{className:"univer-absolute univer-right-2 univer-top-[19px] univer-flex univer-h-5 univer-w-5 univer-items-center univer-justify-center univer-rounded hover:univer-bg-gray-200 dark:!univer-text-gray-300 dark:hover:!univer-bg-gray-700",onClick:V,children:p.jsx(lt,{})}):null]})};function fn(e){const t=M.useDependency(C.SheetDataValidationModel),a=M.useDependency(o.IUniverInstanceService),n=M.useDependency(o.ICommandService),r=M.useDependency(o.Injector),i=M.useDependency(he),s=M.useDependency(o.LocaleService),[d,l]=b.useState([]),{workbook:c}=e,u=M.useObservable(c.activeSheet$,void 0,!0),g=c.getUnitId(),h=u==null?void 0:u.getSheetId();b.useEffect(()=>{l(t.getRules(g,h));const S=t.ruleChange$.subscribe(_=>{_.unitId===g&&_.subUnitId===h&&l(t.getRules(g,h))});return()=>{S.unsubscribe()}},[g,h,t]);const f=async()=>{const S=C.createDefaultNewRule(r),_={unitId:g,subUnitId:h,rule:S};await n.executeCommand(C.AddSheetDataValidationCommand.id,_),i.setActiveRule({unitId:g,subUnitId:h,rule:S})},v=()=>{n.executeCommand(C.RemoveSheetAllDataValidationCommand.id,{unitId:g,subUnitId:h})},V=(S=>{const _=a.getCurrentUnitForType(o.UniverInstanceType.UNIVER_SHEET),D=_.getActiveSheet(),R=_.getUnitId(),y=D.getSheetId();return S.map(k=>H.checkRangesEditablePermission(r,R,y,k.ranges)?{...k}:{...k,disable:!0})})(d),m=V==null?void 0:V.some(S=>S.disable);return p.jsxs("div",{className:"univer-pb-4",children:[V==null?void 0:V.map(S=>{var _;return p.jsx(mn,{unitId:g,subUnitId:h,onClick:()=>{S.disable||i.setActiveRule({unitId:g,subUnitId:h,rule:S})},rule:S,disable:(_=S.disable)!=null?_:!1},S.uid)}),p.jsxs("div",{className:"univer-mt-4 univer-flex univer-flex-row univer-justify-end univer-gap-2",children:[d.length&&!m?p.jsx(E.Button,{onClick:v,children:s.t("dataValidation.panel.removeAll")}):null,p.jsx(E.Button,{variant:"primary",onClick:f,children:s.t("dataValidation.panel.add")})]})]})}const Sn=()=>{const e=M.useDependency(he),t=M.useObservable(e.activeRule$,e.activeRule),a=M.useDependency(o.IUniverInstanceService),n=M.useObservable(()=>a.getCurrentTypeOfUnit$(o.UniverInstanceType.UNIVER_SHEET),void 0,void 0,[]),r=M.useObservable(()=>{var i;return(i=n==null?void 0:n.activeSheet$)!=null?i:oe.of(null)},void 0,void 0,[]);return!n||!r?null:t&&t.subUnitId===r.getSheetId()?p.jsx(vn,{},t.rule.uid):p.jsx(fn,{workbook:n})},_n=e=>{const{isTwoFormula:t=!1,value:a,onChange:n,showError:r,validResult:i}=e,s=M.useDependency(o.LocaleService),d=r?i==null?void 0:i.formula1:"",l=r?i==null?void 0:i.formula2:"";return t?p.jsxs(p.Fragment,{children:[p.jsx(E.FormLayout,{error:d,children:p.jsx(E.Input,{className:"univer-w-full",placeholder:s.t("dataValidation.panel.formulaPlaceholder"),value:a==null?void 0:a.formula1,onChange:c=>{n==null||n({...a,formula1:c})}})}),p.jsx("div",{className:"-univer-mt-2 univer-mb-1 univer-text-sm univer-text-gray-400",children:s.t("dataValidation.panel.formulaAnd")}),p.jsx(E.FormLayout,{error:l,children:p.jsx(E.Input,{className:"univer-w-full",placeholder:s.t("dataValidation.panel.formulaPlaceholder"),value:a==null?void 0:a.formula2,onChange:c=>{n==null||n({...a,formula2:c})}})})]}):p.jsx(E.FormLayout,{error:d,children:p.jsx(E.Input,{className:"univer-w-full",placeholder:s.t("dataValidation.panel.formulaPlaceholder"),value:a==null?void 0:a.formula1,onChange:c=>{n==null||n({formula1:c})}})})};function In(e){const{value:t,onChange:a,showError:n,validResult:r}=e,i=M.useDependency(o.LocaleService),s=n?r==null?void 0:r.formula1:"",d=n?r==null?void 0:r.formula2:"",[l,c]=b.useState(!((t==null?void 0:t.formula1)===void 0&&(t==null?void 0:t.formula2)===void 0));return p.jsxs(p.Fragment,{children:[p.jsx(E.FormLayout,{children:p.jsx(E.Checkbox,{checked:l,onChange:u=>{u?c(!0):(c(!1),a==null||a({...t,formula1:void 0,formula2:void 0}))},children:i.t("dataValidation.checkbox.tips")})}),l?p.jsx(E.FormLayout,{label:i.t("dataValidation.checkbox.checked"),error:s,children:p.jsx(E.Input,{className:"univer-w-full",placeholder:i.t("dataValidation.panel.valuePlaceholder"),value:t==null?void 0:t.formula1,onChange:u=>{a==null||a({...t,formula1:u||void 0})}})}):null,l?p.jsx(E.FormLayout,{label:i.t("dataValidation.checkbox.unchecked"),error:d,children:p.jsx(E.Input,{className:"univer-w-full",placeholder:i.t("dataValidation.panel.valuePlaceholder"),value:t==null?void 0:t.formula2,onChange:u=>{a==null||a({...t,formula2:u||void 0})}})}):null]})}function Cn(e){var g;const{unitId:t,subUnitId:a,value:n,onChange:r,showError:i,validResult:s}=e,d=i?s==null?void 0:s.formula1:void 0,l=b.useRef(null),[c,u]=b.useState(!1);return M.useSidebarClick(h=>{var v;((v=l.current)==null?void 0:v.isClickOutSide(h))&&u(!1)}),p.jsx(E.FormLayout,{error:d,children:p.jsx(it.FormulaEditor,{ref:l,className:E.clsx("univer-box-border univer-h-8 univer-w-full univer-cursor-pointer univer-items-center univer-rounded-lg univer-bg-white univer-pt-2 univer-transition-colors hover:univer-border-primary-600 dark:!univer-bg-gray-700 dark:!univer-text-white [&>div:first-child]:univer-px-2.5 [&>div]:univer-h-5 [&>div]:univer-ring-transparent",E.borderClassName),initValue:(g=n==null?void 0:n.formula1)!=null?g:"=",unitId:t,subUnitId:a,isFocus:c,isSupportAcrossSheet:!0,onChange:h=>{const f=(h!=null?h:"").trim();f!==(n==null?void 0:n.formula1)&&(r==null||r({...n,formula1:f}))},onFocus:()=>u(!0)})})}const yn=["#FFFFFF","#FEE7E7","#FEF0E6","#EFFBD0","#E4F4FE","#E8ECFD","#F1EAFA","#FDE8F3","#E5E5E5","#FDCECE","#FDC49B","#DEF6A2","#9FDAFF","#D0D9FB","#E3D5F6","#FBD0E8","#656565","#FE4B4B","#FF8C51","#8BBB11","#0B9EFB","#3A60F7","#9E6DE3","#F248A6"],Rn=e=>{const{value:t,onChange:a,disabled:n}=e,[r,i]=b.useState(!1);return p.jsx(E.Dropdown,{align:"start",disabled:n,open:r,onOpenChange:i,overlay:p.jsx("div",{className:"univer-box-border univer-grid univer-w-fit univer-grid-cols-6 univer-flex-wrap univer-gap-2 univer-p-1.5",children:yn.map(s=>p.jsx("div",{className:E.clsx("univer-box-border univer-size-4 univer-cursor-pointer univer-rounded",E.borderClassName),style:{background:s},onClick:()=>{a(s),i(!1)}},s))}),children:p.jsxs("div",{className:E.clsx("univer-box-border univer-inline-flex univer-h-8 univer-w-16 univer-cursor-pointer univer-items-center univer-justify-between univer-gap-2 univer-rounded-lg univer-bg-white univer-px-2.5 univer-transition-colors univer-duration-200 hover:univer-border-primary-600 dark:!univer-bg-gray-700 dark:!univer-text-white",E.borderClassName),children:[p.jsx("div",{className:"univer-box-border univer-h-4 univer-w-4 univer-rounded univer-text-base",style:{background:t}}),p.jsx(dt,{})]})})},gt=e=>{const{item:t,commonProps:a,className:n}=e,{onItemChange:r,onItemDelete:i}=a;return p.jsxs("div",{className:E.clsx("univer-flex univer-items-center univer-gap-2",n),children:[!t.isRef&&p.jsx("div",{className:E.clsx("univer-cursor-move","draggableHandle"),children:p.jsx(Et,{})}),p.jsx(Rn,{value:t.color,onChange:s=>{r(t.id,t.label,s)}}),p.jsx(E.Input,{disabled:t.isRef,value:t.label,onChange:s=>{r(t.id,s,t.color)}}),t.isRef?null:p.jsx("div",{className:"univer-ml-1 univer-cursor-pointer univer-rounded univer-text-base hover:univer-bg-gray-200",children:p.jsx(lt,{onClick:()=>i(t.id)})})]})};function Dn(e){const{value:t,onChange:a=()=>{},unitId:n,subUnitId:r,validResult:i,showError:s,ruleId:d}=e,{formula1:l="",formula2:c=""}=t||{},[u,g]=b.useState(()=>o.isFormulaString(l)?"1":"0"),[h,f]=b.useState(u==="1"?l:"="),[v,T]=b.useState(u==="1"?l:"="),V=M.useDependency(o.LocaleService),m=M.useDependency(Y.DataValidatorRegistryService),S=M.useDependency(Y.DataValidationModel),_=M.useDependency(C.DataValidationFormulaController),[D,R]=b.useState(()=>c.split(",")),y=m.getValidatorItem(o.DataValidationType.LIST),[w,k]=b.useState([]),[W,O]=b.useState(""),x=s?i==null?void 0:i.formula1:"",j=b.useMemo(()=>S.ruleChange$.pipe(oe.debounceTime(16)),[]),U=M.useObservable(j),B=M.useEvent(a);b.useEffect(()=>{(async()=>{await new Promise(I=>{setTimeout(()=>I(!0),100)});const P=S.getRuleById(n,r,d),$=P==null?void 0:P.formula1;if(o.isFormulaString($)&&y&&P){const I=await y.getListAsync(P,n,r);k(I)}})()},[S,U,y,d,r,n]),b.useEffect(()=>{o.isFormulaString(l)&&l!==v&&(f(l),T(v))},[v,l]);const[K,Z]=b.useState(()=>{const P=u!=="1"?C.deserializeListOptions(l):[],$=c.split(",");return P.map((I,F)=>({label:I,color:$[F]||fe,isRef:!1,id:o.generateRandomId(4)}))}),te=(P,$,I)=>{const F=K.find(X=>X.id===P);F&&(F.label=$,F.color=I,Z([...K]))},ne=P=>{const $=K.findIndex(I=>I.id===P);$!==-1&&(K.splice($,1),Z([...K]))},re=c.split(","),se=b.useMemo(()=>w.map((P,$)=>({label:P,color:re[$]||fe,id:`${$}`,isRef:!0})),[re,w]),q=(P,$,I)=>{const F=[...D];F[+P]=I,R(F),B({formula1:l,formula2:F.join(",")})},J=()=>{Z([...K,{label:"",color:fe,isRef:!1,id:o.generateRandomId(4)}])};b.useEffect(()=>{if(u==="1")return;const P=new Set,$=[];K.map(I=>({labelList:I.label.split(","),item:I})).forEach(({item:I,labelList:F})=>{F.forEach(X=>{P.has(X)||(P.add(X),$.push({label:X,color:I.color}))})}),B({formula1:C.serializeListOptions($.map(I=>I.label)),formula2:$.map(I=>I.color===fe?"":I.color).join(",")})},[K,B,u,v,D]);const ce=M.useEvent(async P=>{if(!o.isFormulaString(P)){B==null||B({formula1:"",formula2:c});return}_.getFormulaRefCheck(P)?(B==null||B({formula1:o.isFormulaString(P)?P:"",formula2:c}),O("")):(B==null||B({formula1:"",formula2:c}),f("="),O(V.t("dataValidation.validFail.formulaError")))}),Q=b.useRef(null),[z,ae]=b.useState(!1);return M.useSidebarClick(P=>{var I;((I=Q.current)==null?void 0:I.isClickOutSide(P))&&ae(!1)}),p.jsxs(p.Fragment,{children:[p.jsx(E.FormLayout,{label:V.t("dataValidation.list.options"),children:p.jsxs(E.RadioGroup,{value:u,onChange:P=>{g(P),f(v),P==="1"&&B({formula1:v==="="?"":v,formula2:D.join(",")})},children:[p.jsx(E.Radio,{value:"0",children:V.t("dataValidation.list.customOptions")}),p.jsx(E.Radio,{value:"1",children:V.t("dataValidation.list.refOptions")})]})}),u==="1"?p.jsxs(E.FormLayout,{error:x||W||void 0,children:[p.jsx(it.FormulaEditor,{ref:Q,className:E.clsx("univer-box-border univer-h-8 univer-w-full univer-cursor-pointer univer-items-center univer-rounded-lg univer-bg-white univer-pt-2 univer-transition-colors hover:univer-border-primary-600 dark:!univer-bg-gray-700 dark:!univer-text-white [&>div:first-child]:univer-px-2.5 [&>div]:univer-h-5 [&>div]:univer-ring-transparent",E.borderClassName),initValue:h,unitId:n,subUnitId:r,isFocus:z,isSupportAcrossSheet:!0,onFocus:()=>ae(!0),onChange:(P="")=>{const $=(P!=null?P:"").trim();T($),ce($)}}),se.length>0&&p.jsx("div",{className:"univer-mt-3",children:se.map(P=>p.jsx(gt,{className:"univer-mb-3",item:P,commonProps:{onItemChange:q}},P.id))})]}):p.jsx(E.FormLayout,{error:x,children:p.jsxs("div",{className:"-univer-mt-3",children:[p.jsx(E.DraggableList,{list:K,onListChange:Z,rowHeight:28,margin:[0,12],draggableHandle:".draggableHandle",itemRender:P=>p.jsx(gt,{item:P,commonProps:{onItemChange:te,onItemDelete:ne}},P.id),idKey:"id"}),p.jsxs("a",{className:"univer-text-primary univer-flex univer-w-fit univer-cursor-pointer univer-flex-row univer-items-center univer-rounded univer-p-1 univer-px-2 univer-text-sm hover:univer-bg-primary-50 dark:hover:!univer-bg-gray-800",onClick:J,children:[p.jsx(wt,{className:"univer-mr-1"}),V.t("dataValidation.list.add")]})]})})]})}const wn=[[C.CUSTOM_FORMULA_INPUT_NAME,Cn],[C.BASE_FORMULA_INPUT_NAME,_n],[C.LIST_FORMULA_INPUT_NAME,Dn],[C.CHECKBOX_FORMULA_INPUT_NAME,In]],Vn="LIST_RENDER_MODE_OPTION_INPUT";function Ne(e){var r;const{value:t,onChange:a}=e,n=M.useDependency(o.LocaleService);return p.jsx(E.FormLayout,{label:n.t("dataValidation.renderMode.label"),children:p.jsxs(E.RadioGroup,{value:`${(r=t.renderMode)!=null?r:o.DataValidationRenderMode.CUSTOM}`,onChange:i=>a({...t,renderMode:+i}),children:[p.jsx(E.Radio,{value:`${o.DataValidationRenderMode.CUSTOM}`,children:n.t("dataValidation.renderMode.chip")}),p.jsx(E.Radio,{value:`${o.DataValidationRenderMode.ARROW}`,children:n.t("dataValidation.renderMode.arrow")}),p.jsx(E.Radio,{value:`${o.DataValidationRenderMode.TEXT}`,children:n.t("dataValidation.renderMode.text")})]})})}Ne.componentKey=Vn;const En="DATE_SHOW_TIME_OPTION";function Be(e){var r;const{value:t,onChange:a}=e,n=M.useDependency(o.LocaleService);return p.jsx(E.FormLayout,{children:p.jsx(E.Checkbox,{checked:(r=t.bizInfo)==null?void 0:r.showTime,onChange:i=>{a({...t,bizInfo:{...t.bizInfo,showTime:i}})},children:n.t("dataValidation.showTime.label")})})}Be.componentKey=En;var Mn=Object.getOwnPropertyDescriptor,bn=(e,t,a,n)=>{for(var r=n>1?void 0:n?Mn(t,a):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(r=s(r)||r);return r},Re=(e,t)=>(a,n)=>t(a,n,e);const Le=6;let tt=class{constructor(e,t,a,n,r,i){this._commandService=e,this._univerInstanceService=t,this._formulaService=a,this._themeService=n,this._renderManagerService=r,this._dataValidationModel=i}_calc(e,t){var c,u,g;const{vt:a,ht:n}=t||{},r=e.endX-e.startX-Le*2,i=e.endY-e.startY,s=((c=t==null?void 0:t.fs)!=null?c:10)*1.6;let d=0,l=0;switch(a){case o.VerticalAlign.TOP:l=0;break;case o.VerticalAlign.BOTTOM:l=0+(i-s);break;default:l=0+(i-s)/2;break}switch(n){case o.HorizontalAlign.LEFT:d=Le;break;case o.HorizontalAlign.RIGHT:d=Le+(r-s);break;default:d=Le+(r-s)/2;break}return{left:e.startX+d,top:e.startY+l,width:((u=t==null?void 0:t.fs)!=null?u:10)*1.6,height:((g=t==null?void 0:t.fs)!=null?g:10)*1.6}}calcCellAutoHeight(e){var a;const{style:t}=e;return((a=t==null?void 0:t.fs)!=null?a:10)*1.6}calcCellAutoWidth(e){var a;const{style:t}=e;return((a=t==null?void 0:t.fs)!=null?a:10)*1.6}async _parseFormula(e,t,a){var c,u,g,h,f,v,T,V,m;const{formula1:n=C.CHECKBOX_FORMULA_1,formula2:r=C.CHECKBOX_FORMULA_2}=e,i=await this._formulaService.getRuleFormulaResult(t,a,e.uid),s=C.getFormulaResult((g=(u=(c=i==null?void 0:i[0])==null?void 0:c.result)==null?void 0:u[0])==null?void 0:g[0]),d=C.getFormulaResult((v=(f=(h=i==null?void 0:i[1])==null?void 0:h.result)==null?void 0:f[0])==null?void 0:v[0]),l=C.isLegalFormulaResult(String(s))&&C.isLegalFormulaResult(String(d));return{formula1:o.isFormulaString(n)?C.getFormulaResult((m=(V=(T=i==null?void 0:i[0])==null?void 0:T.result)==null?void 0:V[0])==null?void 0:m[0]):n,formula2:o.isFormulaString(r)?d:r,isFormulaValid:l}}drawWith(e,t){var x,j,U,B;const{style:a,primaryWithCoord:n,unitId:r,subUnitId:i,worksheet:s,row:d,col:l}=t,c=n.isMergedMainCell?n.mergeInfo:n,u=C.getCellValueOrigin(s.getCellRaw(d,l)),g=this._dataValidationModel.getRuleByLocation(r,i,d,l);if(!g)return;const h=this._dataValidationModel.getValidator(g.type);if(!h||!((x=h.skipDefaultFontRender)!=null&&x.call(h,g,u,{unitId:r,subUnitId:i,row:d,column:l})))return;const f=h.parseFormulaSync(g,r,i),{formula1:v}=f,T=this._calc(c,a),{a:V,d:m}=e.getTransform(),S=A.fixLineWidthByScale(T.left,V),_=A.fixLineWidthByScale(T.top,m),D=A.Transform.create().composeMatrix({left:S,top:_,scaleX:1,scaleY:1,angle:0,skewX:0,skewY:0,flipX:!1,flipY:!1}),R=c.endX-c.startX,y=c.endY-c.startY;e.save(),e.beginPath(),e.rect(c.startX,c.startY,R,y),e.clip();const w=D.getMatrix();e.transform(w[0],w[1],w[2],w[3],w[4],w[5]);const k=((j=a==null?void 0:a.fs)!=null?j:10)*1.6,W=String(u)===String(v),O=this._themeService.getColorFromTheme("primary.600");A.CheckboxShape.drawWith(e,{checked:W,width:k,height:k,fill:(B=(U=a==null?void 0:a.cl)==null?void 0:U.rgb)!=null?B:O}),e.restore()}isHit(e,t){const a=t.primaryWithCoord.isMergedMainCell?t.primaryWithCoord.mergeInfo:t.primaryWithCoord,n=this._calc(a,t.style),r=n.top,i=n.top+n.height,s=n.left,d=n.left+n.width,{x:l,y:c}=e;return l<=d&&l>=s&&c<=i&&c>=r}async onPointerDown(e,t){var v;if(t.button===2)return;const{primaryWithCoord:a,unitId:n,subUnitId:r,worksheet:i,row:s,col:d}=e,l=C.getCellValueOrigin(i.getCellRaw(s,d)),c=this._dataValidationModel.getRuleByLocation(n,r,s,d);if(!c)return;const u=this._dataValidationModel.getValidator(c.type);if(!u||!((v=u.skipDefaultFontRender)!=null&&v.call(u,c,l,{unitId:n,subUnitId:r,row:s,column:d})))return;const{formula1:g,formula2:h}=await this._parseFormula(c,n,r),f={range:{startColumn:a.actualColumn,endColumn:a.actualColumn,startRow:a.actualRow,endRow:a.actualRow},value:{v:String(l)===C.transformCheckboxValue(String(g))?h:g,p:null}};this._commandService.executeCommand(H.SetRangeValuesCommand.id,f)}onPointerEnter(e,t){var a,n;(n=(a=A.getCurrentTypeOfRenderer(o.UniverInstanceType.UNIVER_SHEET,this._univerInstanceService,this._renderManagerService))==null?void 0:a.mainComponent)==null||n.setCursor(A.CURSOR_TYPE.POINTER)}onPointerLeave(e,t){var a,n;(n=(a=A.getCurrentTypeOfRenderer(o.UniverInstanceType.UNIVER_SHEET,this._univerInstanceService,this._renderManagerService))==null?void 0:a.mainComponent)==null||n.setCursor(A.CURSOR_TYPE.DEFAULT)}};tt=bn([Re(0,o.ICommandService),Re(1,o.IUniverInstanceService),Re(2,o.Inject(C.DataValidationFormulaService)),Re(3,o.Inject(o.ThemeService)),Re(4,o.Inject(A.IRenderManagerService)),Re(5,o.Inject(C.SheetDataValidationModel))],tt);var Tn=Object.getOwnPropertyDescriptor,On=(e,t,a,n)=>{for(var r=n>1?void 0:n?Tn(t,a):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(r=s(r)||r);return r},Pn=(e,t)=>(a,n)=>t(a,n,e);exports.BaseSheetDataValidatorView=class{constructor(t){L(this,"canvasRender",null);L(this,"dropdownType");L(this,"optionsInput");L(this,"formulaInput",C.LIST_FORMULA_INPUT_NAME);this.injector=t}};exports.BaseSheetDataValidatorView=On([Pn(0,o.Inject(o.Injector))],exports.BaseSheetDataValidatorView);class Ln extends exports.BaseSheetDataValidatorView{constructor(){super(...arguments);L(this,"id",o.DataValidationType.CHECKBOX);L(this,"canvasRender",this.injector.createInstance(tt));L(this,"formulaInput",C.CHECKBOX_FORMULA_INPUT_NAME)}}class An extends exports.BaseSheetDataValidatorView{constructor(){super(...arguments);L(this,"id",o.DataValidationType.CUSTOM);L(this,"formulaInput",C.CUSTOM_FORMULA_INPUT_NAME)}}const jn="data-validation.formula-input";class xn extends exports.BaseSheetDataValidatorView{constructor(){super(...arguments);L(this,"id",o.DataValidationType.DATE);L(this,"formulaInput",jn);L(this,"optionsInput",Be.componentKey);L(this,"dropdownType",Y.DataValidatorDropdownType.DATE)}}class Un extends exports.BaseSheetDataValidatorView{constructor(){super(...arguments);L(this,"id",o.DataValidationType.DECIMAL);L(this,"formulaInput",C.BASE_FORMULA_INPUT_NAME)}}const Fn=4;class kn extends A.Shape{static drawWith(t,a){const{fontString:n,info:r,fill:i,color:s}=a,{layout:d,text:l}=r;t.save(),A.Rect.drawWith(t,{width:d.width,height:d.height,radius:Fn,fill:i||fe}),t.font=n,t.fillStyle=s,t.textAlign="center",t.textBaseline="middle";const c=d.width/2,u=d.height/2;t.fillText(l,c,u),t.restore()}}const Nn=6,Bn=2,Wn=4,Hn=4,nt=6,We=6,me=14;function $n(e,t){const a=A.FontCache.getTextSize(e,t),n=a.width+Nn*2,{ba:r,bd:i}=a,s=r+i;return{width:n,height:s+Bn*2,ba:r}}function Ze(e,t,a,n){const r=me+nt*2,i=a-r,s=n-We*2,d=e.map(f=>({layout:$n(f,t),text:f})),l={width:0,height:0,items:[]};let c=0;d.forEach((f,v)=>{const{layout:T}=f,{width:V,height:m}=T;if(c+V<=i)l.items.push({...f,left:c}),l.width=c+V,l.height=Math.max(l.height,m),v<d.length-1?c+=V+Wn:c+=V;else if(c<i){const S=i-c;S>0&&(l.items.push({...f,left:c,clipped:!0,clippedWidth:S}),l.width=i,l.height=Math.max(l.height,m));return}});const u=[l],g=l.height,h=l.width;return{lines:u,totalHeight:g,contentWidth:i,contentHeight:s,cellAutoHeight:g+We*2,calcAutoWidth:h+r}}var Yn=Object.getOwnPropertyDescriptor,Xn=(e,t,a,n)=>{for(var r=n>1?void 0:n?Yn(t,a):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(r=s(r)||r);return r},Ae=(e,t)=>(a,n)=>t(a,n,e);const zn=new Path2D("M3.32201 4.84556C3.14417 5.05148 2.85583 5.05148 2.67799 4.84556L0.134292 1.90016C-0.152586 1.56798 0.0505937 1 0.456301 1L5.5437 1C5.94941 1 6.15259 1.56798 5.86571 1.90016L3.32201 4.84556Z");let at=class{constructor(e,t,a,n){L(this,"zIndex");L(this,"_dropdownInfoMap",new Map);this._commandService=e,this._univerInstanceService=t,this._renderManagerService=a,this._dataValidationModel=n}_ensureMap(e){let t=this._dropdownInfoMap.get(e);return t||(t=new Map,this._dropdownInfoMap.set(e,t)),t}_generateKey(e,t){return`${e}.${t}`}_drawDownIcon(e,t,a,n,r){const i=a-me+4;let s=4;switch(r){case o.VerticalAlign.MIDDLE:s=(n-me)/2+4;break;case o.VerticalAlign.BOTTOM:s=n-me+4;break}e.save(),e.translateWithPrecision(t.startX+i,t.startY+s),e.fillStyle="#565656",e.fill(zn),e.restore()}drawWith(e,t,a,n){var ne,re;const{primaryWithCoord:r,row:i,col:s,style:d,data:l,subUnitId:c}=t,u=r.isMergedMainCell?r.mergeInfo:r,g=l==null?void 0:l.fontRenderExtension,{leftOffset:h=0,rightOffset:f=0,topOffset:v=0,downOffset:T=0}=g||{},V=this._ensureMap(c),m=this._generateKey(i,s),S=this._dataValidationModel.getRuleByLocation(t.unitId,t.subUnitId,i,s);if(!S)return;const _=this._dataValidationModel.getValidator(S.type);if(!_)return;const D={startX:u.startX+h,endX:u.endX-f,startY:u.startY+v,endY:u.endY-T},R=D.endX-D.startX,y=D.endY-D.startY,{cl:w}=d||{},k=(ne=typeof w=="object"?w==null?void 0:w.rgb:w)!=null?ne:"#000",W=A.getFontStyleString(d!=null?d:void 0),{vt:O,ht:x}=d||{},j=O!=null?O:o.VerticalAlign.MIDDLE,U=(re=C.getCellValueOrigin(l))!=null?re:"",B=_.parseCellValue(U),K=_.getListWithColorMap(S),Z=Ze(B,W,R,y);this._drawDownIcon(e,D,R,y,j),e.save(),e.translateWithPrecision(D.startX,D.startY),e.beginPath(),e.rect(0,0,R-me,y),e.clip(),e.translateWithPrecision(nt,We);const te=(Z.contentHeight-Z.totalHeight)/2;e.translateWithPrecision(0,te),Z.lines.forEach((se,q)=>{e.save();const{height:J,items:ce}=se;e.translate(0,q*(J+Hn)),ce.forEach(z=>{e.save(),e.translateWithPrecision(z.left,0),z.clipped&&z.clippedWidth&&(e.beginPath(),e.rect(0,0,z.clippedWidth,z.layout.height),e.clip()),kn.drawWith(e,{...W,info:z,color:k,fill:K[z.text]}),e.restore()}),e.restore()}),e.restore(),V.set(m,{left:D.startX,top:D.startY,width:Z.contentWidth+nt+me,height:Z.contentHeight+We*2})}calcCellAutoHeight(e){var R;const{primaryWithCoord:t,style:a,data:n,row:r,col:i}=e,s=n==null?void 0:n.fontRenderExtension,{leftOffset:d=0,rightOffset:l=0,topOffset:c=0,downOffset:u=0}=s||{},g=t.isMergedMainCell?t.mergeInfo:t,h={startX:g.startX+d,endX:g.endX-l,startY:g.startY+c,endY:g.endY-u},f=this._dataValidationModel.getRuleByLocation(e.unitId,e.subUnitId,r,i);if(!f)return;const v=this._dataValidationModel.getValidator(f.type);if(!v)return;const T=h.endX-h.startX,V=h.endY-h.startY,m=(R=C.getCellValueOrigin(n))!=null?R:"",S=v.parseCellValue(m),_=A.getFontStyleString(a!=null?a:void 0);return Ze(S,_,T,V).cellAutoHeight}calcCellAutoWidth(e){var R;const{primaryWithCoord:t,style:a,data:n,row:r,col:i}=e,s=n==null?void 0:n.fontRenderExtension,{leftOffset:d=0,rightOffset:l=0,topOffset:c=0,downOffset:u=0}=s||{},g=t.isMergedMainCell?t.mergeInfo:t,h={startX:g.startX+d,endX:g.endX-l,startY:g.startY+c,endY:g.endY-u},f=this._dataValidationModel.getRuleByLocation(e.unitId,e.subUnitId,r,i);if(!f)return;const v=this._dataValidationModel.getValidator(f.type);if(!v)return;const T=h.endX-h.startX,V=h.endY-h.startY,m=(R=C.getCellValueOrigin(n))!=null?R:"",S=v.parseCellValue(m),_=A.getFontStyleString(a!=null?a:void 0);return Ze(S,_,T,V).calcAutoWidth}isHit(e,t){const{primaryWithCoord:a}=t,n=a.isMergedMainCell?a.mergeInfo:a,{endX:r}=n,{x:i}=e;return i>=r-me&&i<=r}onPointerDown(e,t){if(t.button===2)return;const{unitId:a,subUnitId:n,row:r,col:i}=e,s={unitId:a,subUnitId:n,row:r,column:i};this._commandService.executeCommand(He.id,s)}onPointerEnter(e,t){var a,n;return(n=(a=A.getCurrentTypeOfRenderer(o.UniverInstanceType.UNIVER_SHEET,this._univerInstanceService,this._renderManagerService))==null?void 0:a.mainComponent)==null?void 0:n.setCursor(A.CURSOR_TYPE.POINTER)}onPointerLeave(e,t){var a,n;return(n=(a=A.getCurrentTypeOfRenderer(o.UniverInstanceType.UNIVER_SHEET,this._univerInstanceService,this._renderManagerService))==null?void 0:a.mainComponent)==null?void 0:n.setCursor(A.CURSOR_TYPE.DEFAULT)}};at=Xn([Ae(0,o.ICommandService),Ae(1,o.IUniverInstanceService),Ae(2,o.Inject(A.IRenderManagerService)),Ae(3,o.Inject(C.SheetDataValidationModel))],at);class Kn extends exports.BaseSheetDataValidatorView{constructor(){super(...arguments);L(this,"id",o.DataValidationType.LIST_MULTIPLE);L(this,"canvasRender",this.injector.createInstance(at));L(this,"dropdownType",Y.DataValidatorDropdownType.MULTIPLE_LIST)}}var Zn=Object.getOwnPropertyDescriptor,Gn=(e,t,a,n)=>{for(var r=n>1?void 0:n?Zn(t,a):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(r=s(r)||r);return r},Oe=(e,t)=>(a,n)=>t(a,n,e);const ve=6,je=4,de=14,vt=2,ue=6,De=3,Ge=4,qn="#565656",mt=new Path2D("M3.32201 4.84556C3.14417 5.05148 2.85583 5.05148 2.67799 4.84556L0.134292 1.90016C-0.152586 1.56798 0.0505937 1 0.456301 1L5.5437 1C5.94941 1 6.15259 1.56798 5.86571 1.90016L3.32201 4.84556Z");function ft(e,t,a,n,r,i,s=!0){let d=0;const l=s?De:0;switch(r){case o.VerticalAlign.BOTTOM:d=t-n-l;break;case o.VerticalAlign.MIDDLE:d=(t-n)/2;break;default:d=l;break}d=Math.max(De,d);let c=0;switch(i){case o.HorizontalAlign.CENTER:c=(e-a)/2;break;case o.HorizontalAlign.RIGHT:c=e-a;break}return c=Math.max(ue,c),{paddingLeft:c,paddingTop:d}}let rt=class{constructor(e,t,a,n,r){L(this,"_dropdownInfoMap",new Map);L(this,"zIndex");this._univerInstanceService=e,this._localeService=t,this._commandService=a,this._renderManagerService=n,this._dataValidationModel=r}_ensureMap(e){let t=this._dropdownInfoMap.get(e);return t||(t=new Map,this._dropdownInfoMap.set(e,t)),t}_generateKey(e,t){return`${e}.${t}`}_drawDownIcon(e,t,a,n,r,i,s){const{t:d=o.DEFAULT_STYLES.pd.t,b:l=o.DEFAULT_STYLES.pd.b}=s,c=a-de;let u;switch(i){case o.VerticalAlign.MIDDLE:u=(n-je)/2;break;case o.VerticalAlign.BOTTOM:u=n-l-r-De+(r/2-je/2);break;default:u=d+De+(r/2-je/2);break}e.save(),e.translateWithPrecision(t.startX+c,t.startY+u),e.fillStyle="#565656",e.fill(mt),e.restore()}drawWith(e,t,a){var K,Z,te,ne,re,se;const{primaryWithCoord:n,row:r,col:i,style:s,data:d,subUnitId:l}=t,c=n.isMergedMainCell?n.mergeInfo:n,u=this._dataValidationModel.getRuleByLocation(t.unitId,t.subUnitId,r,i);if(!u)return;const g=this._dataValidationModel.getValidator(u.type);if(!g)return;const h=d==null?void 0:d.fontRenderExtension,{leftOffset:f=0,rightOffset:v=0,topOffset:T=0,downOffset:V=0}=h||{};if(!u||!g||!g||g.id.indexOf(o.DataValidationType.LIST)!==0||!g.skipDefaultFontRender(u))return;const m={startX:c.startX+f,endX:c.endX-v,startY:c.startY+T,endY:c.endY-V},S=m.endX-m.startX,_=m.endY-m.startY,D=this._ensureMap(l),R=this._generateKey(r,i),y=g.getListWithColor(u),w=C.getCellValueOrigin(d),k=`${w!=null?w:""}`,W=y.find(q=>q.label===k);let{tb:O,vt:x,ht:j,pd:U}=s||{};O=O!=null?O:o.WrapStrategy.WRAP,x=x!=null?x:o.VerticalAlign.BOTTOM,j=j!=null?j:o.DEFAULT_STYLES.ht,U=U!=null?U:o.DEFAULT_STYLES.pd;const B=A.getFontStyleString(s).fontCache;if(u.renderMode===o.DataValidationRenderMode.ARROW){const{l:q=o.DEFAULT_STYLES.pd.l,t:J=o.DEFAULT_STYLES.pd.t,r:ce=o.DEFAULT_STYLES.pd.r,b:Q=o.DEFAULT_STYLES.pd.b}=U,z=S-q-ce-de-4,ae=new A.DocSimpleSkeleton(k,B,O===o.WrapStrategy.WRAP,z,1/0);ae.calculate();const P=ae.getTotalWidth(),$=ae.getTotalHeight(),{paddingTop:I,paddingLeft:F}=ft(z,_-J-Q,P,$,x,j,!0);this._drawDownIcon(e,m,S,_,$,x,U),e.save(),e.translateWithPrecision(m.startX+q,m.startY+J),e.beginPath(),e.rect(0,0,S-q-ce,_-J-Q),e.clip(),e.translateWithPrecision(0,I),e.save(),e.translateWithPrecision(ve,0),e.beginPath(),e.rect(0,0,z,$),e.clip(),A.Text.drawWith(e,{text:k,fontStyle:B,width:z,height:$,color:(K=s==null?void 0:s.cl)==null?void 0:K.rgb,strokeLine:!!((Z=s==null?void 0:s.st)!=null&&Z.s),underline:!!((te=s==null?void 0:s.ul)!=null&&te.s),warp:O===o.WrapStrategy.WRAP,hAlign:o.HorizontalAlign.LEFT},ae),e.translateWithPrecision(F,0),e.restore(),e.restore(),D.set(R,{left:m.endX+q+a.rowHeaderWidth-de,top:m.startY+J+a.columnHeaderHeight,width:de,height:_-J-Q})}else{e.save(),e.translateWithPrecision(m.startX,m.startY),e.beginPath(),e.rect(0,0,S,_),e.clip();const q=S-ue*2-ve-de-4,J=new A.DocSimpleSkeleton(k,B,O===o.WrapStrategy.WRAP,q,1/0);J.calculate();const ce=J.getTotalWidth(),Q=J.getTotalHeight(),z=Q+vt*2,ae=Math.max(S-ue*2,1),{paddingTop:P,paddingLeft:$}=ft(ae,_,ce,z,x,j);e.translateWithPrecision(ue,P),A.Rect.drawWith(e,{width:ae,height:z,fill:(W==null?void 0:W.color)||fe,radius:Ge}),e.save(),e.translateWithPrecision(ve,vt),e.beginPath(),e.rect(0,0,q,Q),e.clip(),e.translateWithPrecision($,0),A.Text.drawWith(e,{text:k,fontStyle:B,width:q,height:Q,color:(ne=s==null?void 0:s.cl)==null?void 0:ne.rgb,strokeLine:!!((re=s==null?void 0:s.st)!=null&&re.s),underline:!!((se=s==null?void 0:s.ul)!=null&&se.s),warp:O===o.WrapStrategy.WRAP,hAlign:o.HorizontalAlign.LEFT},J),e.restore(),e.translateWithPrecision(q+ve+4,(Q-je)/2),e.fillStyle=qn,e.fill(mt),e.restore(),D.set(R,{left:m.startX+ue+a.rowHeaderWidth,top:m.startY+P+a.columnHeaderHeight,width:ae,height:z})}}calcCellAutoHeight(e){const{primaryWithCoord:t,style:a,data:n,row:r,col:i}=e,s=t.isMergedMainCell?t.mergeInfo:t,d=n==null?void 0:n.fontRenderExtension,{leftOffset:l=0,rightOffset:c=0,topOffset:u=0,downOffset:g=0}=d||{},h=this._dataValidationModel.getRuleByLocation(e.unitId,e.subUnitId,r,i);if(!h||h.renderMode===o.DataValidationRenderMode.TEXT)return;const f={startX:s.startX+l,endX:s.endX-c,startY:s.startY+u,endY:s.endY-g},v=f.endX-f.startX,T=C.getCellValueOrigin(n),V=`${T!=null?T:""}`;let{tb:m,pd:S}=a||{};const{t:_=o.DEFAULT_STYLES.pd.t,b:D=o.DEFAULT_STYLES.pd.b}=S!=null?S:{};if(m=m!=null?m:o.WrapStrategy.WRAP,h.renderMode===o.DataValidationRenderMode.ARROW){const R=v-de,y=new A.DocSimpleSkeleton(V,A.getFontStyleString(a).fontCache,m===o.WrapStrategy.WRAP,R,1/0);return y.calculate(),y.getTotalHeight()+_+D+De*2}else{const R=Math.max(v-ue*2-ve-de,10),y=new A.DocSimpleSkeleton(V,A.getFontStyleString(a).fontCache,m===o.WrapStrategy.WRAP,R,1/0);return y.calculate(),y.getTotalHeight()+De*2+4}}calcCellAutoWidth(e){const{primaryWithCoord:t,style:a,data:n,row:r,col:i}=e,s=t.isMergedMainCell?t.mergeInfo:t,d=n==null?void 0:n.fontRenderExtension,{leftOffset:l=0,rightOffset:c=0,topOffset:u=0,downOffset:g=0}=d||{},h=this._dataValidationModel.getRuleByLocation(e.unitId,e.subUnitId,r,i);if(!h||h.renderMode===o.DataValidationRenderMode.TEXT)return;const f={startX:s.startX+l,endX:s.endX-c,startY:s.startY+u,endY:s.endY-g},v=f.endX-f.startX,T=C.getCellValueOrigin(n),V=`${T!=null?T:""}`;let{tb:m,pd:S}=a||{};const{l:_=o.DEFAULT_STYLES.pd.l,r:D=o.DEFAULT_STYLES.pd.r}=S!=null?S:{};m=m!=null?m:o.WrapStrategy.WRAP;let R=ue*2+de;switch(h.renderMode){case o.DataValidationRenderMode.ARROW:R=de+ue*2+D+_;break;case o.DataValidationRenderMode.CUSTOM:R=de+ue*2+ve*2+D+_+Ge/2+1;break;default:R=de+ue*2+ve*2+D+_+Ge/2+1}const y=v-R,w=new A.DocSimpleSkeleton(V,A.getFontStyleString(a).fontCache,m===o.WrapStrategy.WRAP,y,1/0);return w.calculate(),w.getTotalWidth()+R}isHit(e,t){const{subUnitId:a,row:n,col:r}=t,s=this._ensureMap(a).get(this._generateKey(n,r)),d=this._dataValidationModel.getRuleByLocation(t.unitId,t.subUnitId,n,r);if(!d||!s||d.renderMode===o.DataValidationRenderMode.TEXT)return!1;const{top:l,left:c,width:u,height:g}=s,{x:h,y:f}=e;return h>=c&&h<=c+u&&f>=l&&f<=l+g}onPointerDown(e,t){if(t.button===2)return;const{unitId:a,subUnitId:n,row:r,col:i}=e,s={unitId:a,subUnitId:n,row:r,column:i};this._commandService.executeCommand(He.id,s)}onPointerEnter(e,t){var a,n;(n=(a=A.getCurrentTypeOfRenderer(o.UniverInstanceType.UNIVER_SHEET,this._univerInstanceService,this._renderManagerService))==null?void 0:a.mainComponent)==null||n.setCursor(A.CURSOR_TYPE.POINTER)}onPointerLeave(e,t){var a,n;(n=(a=A.getCurrentTypeOfRenderer(o.UniverInstanceType.UNIVER_SHEET,this._univerInstanceService,this._renderManagerService))==null?void 0:a.mainComponent)==null||n.setCursor(A.CURSOR_TYPE.DEFAULT)}};rt=Gn([Oe(0,o.IUniverInstanceService),Oe(1,o.Inject(o.LocaleService)),Oe(2,o.ICommandService),Oe(3,o.Inject(A.IRenderManagerService)),Oe(4,o.Inject(C.SheetDataValidationModel))],rt);class Jn extends exports.BaseSheetDataValidatorView{constructor(){super(...arguments);L(this,"id",o.DataValidationType.LIST);L(this,"canvasRender",this.injector.createInstance(rt));L(this,"dropdownType",Y.DataValidatorDropdownType.LIST);L(this,"optionsInput",Ne.componentKey);L(this,"formulaInput",C.LIST_FORMULA_INPUT_NAME)}}class Qn extends exports.BaseSheetDataValidatorView{constructor(){super(...arguments);L(this,"id",o.DataValidationType.TEXT_LENGTH);L(this,"formulaInput",C.BASE_FORMULA_INPUT_NAME)}}class ea extends exports.BaseSheetDataValidatorView{constructor(){super(...arguments);L(this,"id",o.DataValidationType.WHOLE);L(this,"formulaInput",C.BASE_FORMULA_INPUT_NAME)}}var ta=Object.getOwnPropertyDescriptor,na=(e,t,a,n)=>{for(var r=n>1?void 0:n?ta(t,a):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(r=s(r)||r);return r},qe=(e,t)=>(a,n)=>t(a,n,e);let Te=class extends o.RxDisposable{constructor(e,t,a){super(),this._injector=e,this._componentManger=t,this._dataValidatorRegistryService=a,this._initComponents(),this._registerValidatorViews()}_initComponents(){[["DataValidationIcon",Dt],[Fe,Sn],[Ne.componentKey,Ne],[Be.componentKey,Be],...wn].forEach(([e,t])=>{this.disposeWithMe(this._componentManger.register(e,t))})}_registerValidatorViews(){[Un,ea,Qn,xn,Ln,Jn,Kn,An].forEach(e=>{const t=this._injector.createInstance(e),a=this._dataValidatorRegistryService.getValidatorItem(t.id);a&&(a.formulaInput=t.formulaInput,a.canvasRender=t.canvasRender,a.dropdownType=t.dropdownType,a.optionsInput=t.optionsInput)})}};Te=na([qe(0,o.Inject(o.Injector)),qe(1,o.Inject(M.ComponentManager)),qe(2,o.Inject(Y.DataValidatorRegistryService))],Te);var aa=Object.getOwnPropertyDescriptor,ra=(e,t,a,n)=>{for(var r=n>1?void 0:n?aa(t,a):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(r=s(r)||r);return r},Je=(e,t)=>(a,n)=>t(a,n,e);const ia="SHEET_DATA_VALIDATION_UI_PLUGIN";var xe;exports.UniverSheetsDataValidationMobileUIPlugin=(xe=class extends o.Plugin{constructor(t=Ue,a,n,r){super(),this._config=t,this._injector=a,this._commandService=n,this._configService=r;const{menu:i,...s}=o.merge({},Ue,this._config);i&&this._configService.setConfig("menu",i,{merge:!0}),this._configService.setConfig(ot,s)}onStarting(){[[he],[Se],[Pe],[Ve],[be],[Me],[Ee],[Te]].forEach(t=>{this._injector.add(t)}),[$e,He,_t,st,_e,St].forEach(t=>{this._commandService.registerCommand(t)})}onReady(){this._injector.get(Ee),this._injector.get(Me),this._injector.get(A.IRenderManagerService).registerRenderModule(o.UniverInstanceType.UNIVER_SHEET,[ke])}onRendered(){this._injector.get(Te),this._injector.get(be)}onSteady(){this._injector.get(Ve)}},L(xe,"pluginName",ia),L(xe,"type",o.UniverInstanceType.UNIVER_SHEET),xe);exports.UniverSheetsDataValidationMobileUIPlugin=ra([Je(1,o.Inject(o.Injector)),Je(2,o.ICommandService),Je(3,o.IConfigService)],exports.UniverSheetsDataValidationMobileUIPlugin);var oa=Object.defineProperty,sa=Object.getOwnPropertyDescriptor,la=(e,t,a)=>t in e?oa(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a,da=(e,t,a,n)=>{for(var r=n>1?void 0:n?sa(t,a):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(r=s(r)||r);return r},Qe=(e,t)=>(a,n)=>t(a,n,e),Mt=(e,t,a)=>la(e,typeof t!="symbol"?t+"":t,a);const ca="SHEET_DATA_VALIDATION_UI_PLUGIN";exports.UniverSheetsDataValidationUIPlugin=class extends o.Plugin{constructor(t=Ue,a,n,r){super(),this._config=t,this._injector=a,this._commandService=n,this._configService=r;const{menu:i,...s}=o.merge({},Ue,this._config);i&&this._configService.setConfig("menu",i,{merge:!0}),this._configService.setConfig(ot,s)}onStarting(){[[he],[Se],[Pe],[Ve],[be],[Me],[Ee],[we],[Te]].forEach(t=>{this._injector.add(t)}),[$e,He,_t,st,_e,St].forEach(t=>{this._commandService.registerCommand(t)})}onReady(){this._injector.get(Ee),this._injector.get(Me),this._injector.get(we),this._injector.get(Pe),this._injector.get(A.IRenderManagerService).registerRenderModule(o.UniverInstanceType.UNIVER_SHEET,[ke])}onRendered(){this._injector.get(Te),this._injector.get(be)}onSteady(){this._injector.get(Ve)}};Mt(exports.UniverSheetsDataValidationUIPlugin,"pluginName",ca);Mt(exports.UniverSheetsDataValidationUIPlugin,"type",o.UniverInstanceType.UNIVER_SHEET);exports.UniverSheetsDataValidationUIPlugin=da([o.DependentOn(C.UniverSheetsDataValidationPlugin),Qe(1,o.Inject(o.Injector)),Qe(2,o.ICommandService),Qe(3,o.IConfigService)],exports.UniverSheetsDataValidationUIPlugin);
