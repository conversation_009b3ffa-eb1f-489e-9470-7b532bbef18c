{"version": 3, "file": "bn-D2KtSTkr.mjs", "sources": ["../../../engine-render/src/components/docs/layout/hyphenation/patterns/bn.ts"], "sourcesContent": ["/**\n * Copyright 2023-present DreamNum Co., Ltd.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const Bn = [\n    '22,11,01,1,21',\n    '{\"‍\":0,\"‌\":1,\"অ\":2,\"আ\":2,\"ই\":2,\"ঈ\":2,\"উ\":2,\"ঊ\":2,\"ঋ\":2,\"ৠ\":2,\"ঌ\":2,\"ৡ\":2,\"এ\":2,\"ঐ\":2,\"ও\":2,\"ঔ\":2,\"া\":2,\"ি\":2,\"ী\":2,\"ু\":2,\"ূ\":2,\"ৃ\":2,\"ৄ\":2,\"ৢ\":2,\"ৣ\":2,\"ে\":2,\"ৈ\":2,\"ো\":2,\"ৌ\":2,\"়\":0,\"ৗ\":2,\"ক\":3,\"খ\":3,\"গ\":3,\"ঘ\":3,\"ঙ\":3,\"চ\":3,\"ছ\":3,\"জ\":3,\"ঝ\":3,\"ঞ\":3,\"ট\":3,\"ঠ\":3,\"ড\":3,\"ড়\":3,\"ঢ\":3,\"ঢ়\":3,\"ণ\":3,\"ত\":3,\"থ\":3,\"দ\":3,\"ধ\":3,\"ন\":3,\"প\":3,\"ফ\":3,\"ব\":3,\"ভ\":3,\"ম\":3,\"য\":3,\"য়\":3,\"র\":3,\"ল\":3,\"শ\":3,\"ষ\":3,\"স\":3,\"হ\":3,\"ৎ\":2,\"ঃ\":4,\"ং\":4,\"ঁ\":4,\"ঽ\":4,\"্\":0}',\n    [],\n];\n"], "names": [], "mappings": "AAgBO,MAAM,KAAK;AAAA,EACd;AAAA,EACA;AAAA,EACA,CAAA;AACJ;"}