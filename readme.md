# Univer Sheet 演示项目

一个基于 Univer Sheet 的完整演示项目，展示各种表格功能和数据处理能力。

## 🚀 功能特性

### 自动加载演示
- ✅ 页面打开时自动加载所有演示功能
- ✅ 数据加载时一次性应用所有格式和功能
- ✅ 无需分步骤设置，开箱即用
- ✅ 包含完整的员工数据演示

### 核心功能
- 📊 **智能筛选**：支持多列筛选、条件筛选
- ❄️ **冻结窗格**：冻结表头和关键列
- 🔗 **超链接**：城市名称链接到地图
- 🛡️ **数据保护**：保护重要数据区域
- 🎯 **单元格选择**：支持单选和多选区域
- 🆕 **新增单元格类型**：单选、多选、复选框等

### 查找替换功能
- 🔍 **查找功能**：Ctrl+F 快速查找
- 🔄 **替换功能**：批量替换内容
- 📍 **定位功能**：快速定位到指定内容

## 🛠️ 技术栈

- **Univer Sheet** - 核心表格组件
- **TypeScript** - 类型安全
- **Vite** - 快速构建工具
- **ESLint** - 代码质量检查

## 📦 安装和运行

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

## 🎯 使用说明

1. **启动项目**：运行 `npm run dev`
2. **打开浏览器**：访问 `http://localhost:5173`
3. **开箱即用**：页面加载后立即显示完整功能的表格
4. **交互体验**：
   - 点击表头下拉箭头进行筛选
   - 点击城市名称跳转到地图
   - 尝试不同的单元格类型（单选、多选、复选框）
   - 使用 Ctrl+F 进行查找替换
   - 体验冻结窗格和数据保护功能

## 📋 演示数据

项目包含完整的员工数据演示，包括：
- 员工基本信息（ID、姓名、年龄、部门、职位、城市、薪资等）
- 多种数据类型演示
- 智能筛选和排序功能
- 数据验证和格式化

## 🔧 开发说明

### 项目结构
```
src/
├── main.ts              # 主入口文件
├── demo-manager.ts      # 演示管理器
├── setup-univer.ts      # Univer初始化
├── sample-data.ts       # 示例数据
├── api.ts              # API接口函数
└── style.css           # 样式文件
```

### 核心组件
- **DemoManager**: 统一管理所有演示功能
- **自动初始化**: 页面加载时自动设置所有功能
- **通知系统**: 实时显示操作状态和提示信息

## �� 许可证

MIT License
